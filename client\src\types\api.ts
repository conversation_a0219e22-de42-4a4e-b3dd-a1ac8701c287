// API Response Types - matching your backend PostResponse type
export interface Author {
  id: string;
  name: string | null;
  email: string;
}

export interface Comment {
  id: string;
  content: string;
  createdAt: string; // Date strings from API
  editedAt?: string | null;
  author: Author;
}

export interface Post {
  id: string;
  title: string;
  content: string | null;
  publishedAt: string | null; // Date strings from API
  updatedAt?: string;
  createdAt?: string;
  published?: boolean;
  imageURL?: string | null;
  author: Author;
  comments?: Comment[];
}

// API Error Response
export interface ApiError {
  message: string;
  status?: number;
}

// API Response wrapper
export interface ApiResponse<T> {
  data?: T;
  error?: ApiError;
}
