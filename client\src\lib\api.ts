import { Post, ApiResponse } from '@/types/api';

// Base API URL - adjust this to match your backend server
const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';

/**
 * Generic API request function with error handling
 */
async function apiRequest<T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> {
  try {
    const url = `${API_BASE_URL}${endpoint}`;

    const config: RequestInit = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      // Include credentials for authentication cookies
      credentials: 'include',
      ...options,
    };

    console.log(`🌐 Making API request to: ${url}`);

    const response = await fetch(url, config);

    // Handle non-200 responses
    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ API Error (${response.status}):`, errorText);

      return {
        error: {
          message:
            errorText || `HTTP ${response.status}: ${response.statusText}`,
          status: response.status,
        },
      };
    }

    const data = await response.json();
    console.log(`✅ API Success:`, data);

    return { data };
  } catch (error) {
    console.error('❌ Network Error:', error);

    return {
      error: {
        message:
          error instanceof Error ? error.message : 'Network error occurred',
        status: 0,
      },
    };
  }
}

/**
 * Fetch all posts from the backend
 */
export async function fetchPosts(): Promise<ApiResponse<Post[]>> {
  return apiRequest<Post[]>('/api/posts');
}

/**
 * Fetch a single post by ID
 */
export async function fetchPost(id: string): Promise<ApiResponse<Post>> {
  return apiRequest<Post>(`/api/posts/${id}`);
}

/**
 * Create a new post (requires admin authentication)
 */
export async function createPost(postData: {
  title: string;
  content: string;
  published: boolean;
  imageURL?: string;
}): Promise<ApiResponse<Post>> {
  return apiRequest<Post>('/api/posts', {
    method: 'POST',
    body: JSON.stringify(postData),
  });
}

// Utility functions for data transformation

/**
 * Create a slug from a title
 */
function createSlug(title: string): string {
  return title
    .toLowerCase()
    .replace(/[^a-z0-9]+/g, '-')
    .replace(/(^-|-$)/g, '');
}

/**
 * Create an excerpt from content
 */
function createExcerpt(
  content: string | null,
  maxLength: number = 150
): string {
  if (!content) return 'No content available.';

  // Remove HTML tags if any
  const plainText = content.replace(/<[^>]*>/g, '');

  if (plainText.length <= maxLength) return plainText;

  return plainText.substring(0, maxLength).trim() + '...';
}

/**
 * Format date for display
 */
function formatDate(dateString: string | null): string {
  if (!dateString) return 'No date';

  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
  });
}

/**
 * Transform API Post to PostCard format
 */
export function transformPostForCard(post: Post) {
  return {
    id: post.id,
    title: post.title,
    excerpt: createExcerpt(post.content),
    date: formatDate(post.publishedAt || post.createdAt),
    slug: createSlug(post.title),
    author: post.author.name || post.author.email,
    category: 'General', // You can add category logic later
    tags: [], // You can add tags logic later
    viewCount: 0, // You can add view count from your backend later
  };
}
