{"version": 3, "sources": ["../src/runtime/core/public/index.ts", "../src/runtime/core/public/validator.ts", "../src/runtime/core/types/exported/ObjectEnums.ts", "../src/runtime/strictEnum.ts", "../src/runtime/utils/getRuntime.ts", "../../../node_modules/.pnpm/decimal.js@10.5.0/node_modules/decimal.js/decimal.mjs"], "sourcesContent": ["import { validator } from './validator'\n\n/*\n * /!\\ These exports are exposed to the user. Proceed with caution.\n *\n * TODO: Move more hardcoded utils from generation into here\n */\n\nexport { validator }\n", "import { Args, Operation } from '../types/exported/Public'\nimport { Exact } from '../types/exported/Utils'\n\nexport function validator<V>(): <S>(select: Exact<S, V>) => S\nexport function validator<C, M extends Exclude<keyof C, `$${string}`>, O extends keyof C[M] & Operation>(\n  client: C,\n  model: M,\n  operation: O,\n): <S>(select: Exact<S, Args<C[M], O>>) => S\nexport function validator<\n  C,\n  M extends Exclude<keyof C, `$${string}`>,\n  O extends keyof C[M] & Operation,\n  P extends keyof Args<C[M], O>,\n>(client: C, model: M, operation: O, prop: P): <S>(select: Exact<S, Args<C[M], O>[P]>) => S\nexport function validator(..._args: any[]) {\n  return (args: any) => args\n}\n", "/**\n * Module-private symbol used to distinguish between instances of\n * `ObjectEnumValue` created inside and outside this module.\n */\nconst secret = Symbol()\n\n/**\n * Emulate a private property via a WeakMap manually. Using native private\n * properties is a breaking change for downstream users with minimal TypeScript\n * configs, because TypeScript uses ES3 as the default target.\n *\n * TODO: replace this with a `#representation` private property in the\n * `ObjectEnumValue` class and document minimal required `target` for TypeScript.\n */\nconst representations = new WeakMap<ObjectEnumValue, string>()\n\n/**\n * Base class for unique values of object-valued enums.\n */\nexport abstract class ObjectEnumValue {\n  constructor(arg?: symbol) {\n    if (arg === secret) {\n      representations.set(this, `Prisma.${this._getName()}`)\n    } else {\n      representations.set(this, `new Prisma.${this._getNamespace()}.${this._getName()}()`)\n    }\n  }\n\n  abstract _getNamespace(): string\n\n  _getName() {\n    return this.constructor.name\n  }\n\n  toString() {\n    return representations.get(this)!\n  }\n}\n\nclass NullTypesEnumValue extends ObjectEnumValue {\n  override _getNamespace() {\n    return 'NullTypes'\n  }\n}\n\nclass DbNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_DbNull!: void\n}\nsetClassName(DbNull, 'DbNull')\n\nclass JsonNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_JsonNull!: void\n}\nsetClassName(JsonNull, 'JsonNull')\n\nclass AnyNull extends NullTypesEnumValue {\n  // Phantom private property to prevent structural type equality\n  // eslint-disable-next-line no-unused-private-class-members\n  readonly #_brand_AnyNull!: void\n}\nsetClassName(AnyNull, 'AnyNull')\n\nexport const objectEnumValues = {\n  classes: {\n    DbNull,\n    JsonNull,\n    AnyNull,\n  },\n  instances: {\n    DbNull: new DbNull(secret),\n    JsonNull: new JsonNull(secret),\n    AnyNull: new AnyNull(secret),\n  },\n}\n\n/**\n * See helper in @internals package. Can not be used here\n * because importing internal breaks browser build.\n *\n * @param classObject\n * @param name\n */\nfunction setClassName(classObject: Function, name: string) {\n  Object.defineProperty(classObject, 'name', {\n    value: name,\n    configurable: true,\n  })\n}\n", "/**\n * List of properties that won't throw exception on access and return undefined instead\n */\nconst allowList = new Set([\n  'toJSON', // used by JSON.stringify\n  '$$typeof', // used by old React tooling\n  'asymmetricMatch', // used by Jest\n  Symbol.iterator, // used by various JS constructs/methods\n  Symbol.toStringTag, // Used by .toString()\n  Symbol.isConcatSpreadable, // Used by Array#concat,\n  Symbol.toPrimitive, // Used when getting converted to primitive values\n])\n/**\n * Generates more strict variant of an enum which, unlike regular enum,\n * throws on non-existing property access. This can be useful in following situations:\n * - we have an API, that accepts both `undefined` and `SomeEnumType` as an input\n * - enum values are generated dynamically from DMMF.\n *\n * In that case, if using normal enums and no compile-time typechecking, using non-existing property\n * will result in `undefined` value being used, which will be accepted. Using strict enum\n * in this case will help to have a runtime exception, telling you that you are probably doing something wrong.\n *\n * Note: if you need to check for existence of a value in the enum you can still use either\n * `in` operator or `hasOwnProperty` function.\n *\n * @param definition\n * @returns\n */\nexport function makeStrictEnum<T extends Record<PropertyKey, string | number>>(definition: T): T {\n  return new Proxy(definition, {\n    get(target, property) {\n      if (property in target) {\n        return target[property]\n      }\n      if (allowList.has(property)) {\n        return undefined\n      }\n      throw new TypeError(`Invalid enum value: ${String(property)}`)\n    },\n  })\n}\n", "// https://runtime-keys.proposal.wintercg.org/\nexport type RuntimeName = 'workerd' | 'deno' | 'netlify' | 'node' | 'bun' | 'edge-light' | '' /* unknown */\n\n/**\n * Indicates if running in Node.js or a Node.js compatible runtime.\n *\n * **Note:** When running code in Bun and Deno with Node.js compatibility mode, `isNode` flag will be also `true`, indicating running in a Node.js compatible runtime.\n */\nconst isNode = () => globalThis.process?.release?.name === 'node'\n\n/**\n * Indicates if running in Bun runtime.\n */\nconst isBun = () => !!globalThis.Bun || !!globalThis.process?.versions?.bun\n\n/**\n * Indicates if running in Deno runtime.\n */\nconst isDeno = () => !!globalThis.Deno\n\n/**\n * Indicates if running in Netlify runtime.\n */\nconst isNetlify = () => typeof globalThis.Netlify === 'object'\n\n/**\n * Indicates if running in EdgeLight (Vercel Edge) runtime.\n */\nconst isEdgeLight = () => typeof globalThis.EdgeRuntime === 'object'\n\n/**\n * Indicates if running in Cloudflare Workers runtime.\n * See: https://developers.cloudflare.com/workers/runtime-apis/web-standards/#navigatoruseragent\n */\nconst isWorkerd = () => globalThis.navigator?.userAgent === 'Cloudflare-Workers'\n\nfunction detectRuntime(): RuntimeName {\n  // Note: we're currently not taking 'fastly' into account. Why?\n  const runtimeChecks = [\n    [isNetlify, 'netlify'],\n    [isEdgeLight, 'edge-light'],\n    [isWorkerd, 'workerd'],\n    [isDeno, 'deno'],\n    [isBun, 'bun'],\n    [isNode, 'node'],\n  ] as const\n\n  const detectedRuntime =\n    runtimeChecks\n      // TODO: Transforming destructuring to the configured target environment ('chrome58', 'edge16', 'firefox57', 'safari11') is not supported yet,\n      // so we can't write the following code yet:\n      // ```\n      // .flatMap(([isCurrentRuntime, runtime]) => isCurrentRuntime() ? [runtime] : [])\n      // ```\n      .flatMap((check) => (check[0]() ? [check[1]] : []))\n      .at(0) ?? ''\n\n  return detectedRuntime\n}\n\nconst runtimesPrettyNames = {\n  node: 'Node.js',\n  workerd: 'Cloudflare Workers',\n  deno: 'Deno and Deno Deploy',\n  netlify: 'Netlify Edge Functions',\n  'edge-light':\n    'Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)',\n} as const\n\ntype GetRuntimeOutput = {\n  id: RuntimeName\n  prettyName: string\n  isEdge: boolean\n}\n\nexport function getRuntime(): GetRuntimeOutput {\n  const runtimeId = detectRuntime()\n\n  return {\n    id: runtimeId,\n    // Fallback to the runtimeId if the runtime is not in the list\n    prettyName: runtimesPrettyNames[runtimeId] || runtimeId,\n    isEdge: ['workerd', 'deno', 'netlify', 'edge-light'].includes(runtimeId),\n  }\n}\n", "/*!\r\n *  decimal.js v10.5.0\r\n *  An arbitrary-precision Decimal type for JavaScript.\r\n *  https://github.com/MikeMcl/decimal.js\r\n *  Copyright (c) 2025 <PERSON> <<EMAIL>>\r\n *  MIT Licence\r\n */\r\n\r\n\r\n// -----------------------------------  EDITABLE DEFAULTS  ------------------------------------ //\r\n\r\n\r\n  // The maximum exponent magnitude.\r\n  // The limit on the value of `toExpNeg`, `toExpPos`, `minE` and `maxE`.\r\nvar EXP_LIMIT = 9e15,                      // 0 to 9e15\r\n\r\n  // The limit on the value of `precision`, and on the value of the first argument to\r\n  // `toDecimalPlaces`, `toExponential`, `toFixed`, `toPrecision` and `toSignificantDigits`.\r\n  MAX_DIGITS = 1e9,                        // 0 to 1e9\r\n\r\n  // Base conversion alphabet.\r\n  NUMERALS = '0123456789abcdef',\r\n\r\n  // The natural logarithm of 10 (1025 digits).\r\n  LN10 = '2.3025850929940456840179914546843642076011014886287729760333279009675726096773524802359972050895982983419677840422862486334095254650828067566662873690987816894829072083255546808437998948262331985283935053089653777326288461633662222876982198867465436674744042432743651550489343149393914796194044002221051017141748003688084012647080685567743216228355220114804663715659121373450747856947683463616792101806445070648000277502684916746550586856935673420670581136429224554405758925724208241314695689016758940256776311356919292033376587141660230105703089634572075440370847469940168269282808481184289314848524948644871927809676271275775397027668605952496716674183485704422507197965004714951050492214776567636938662976979522110718264549734772662425709429322582798502585509785265383207606726317164309505995087807523710333101197857547331541421808427543863591778117054309827482385045648019095610299291824318237525357709750539565187697510374970888692180205189339507238539205144634197265287286965110862571492198849978748873771345686209167058',\r\n\r\n  // Pi (1025 digits).\r\n  PI = '3.1415926535897932384626433832795028841971693993751058209749445923078164062862089986280348253421170679821480865132823066470938446095505822317253594081284811174502841027019385211055596446229489549303819644288109756659334461284756482337867831652712019091456485669234603486104543266482133936072602491412737245870066063155881748815209209628292540917153643678925903600113305305488204665213841469519415116094330572703657595919530921861173819326117931051185480744623799627495673518857527248912279381830119491298336733624406566430860213949463952247371907021798609437027705392171762931767523846748184676694051320005681271452635608277857713427577896091736371787214684409012249534301465495853710507922796892589235420199561121290219608640344181598136297747713099605187072113499999983729780499510597317328160963185950244594553469083026425223082533446850352619311881710100031378387528865875332083814206171776691473035982534904287554687311595628638823537875937519577818577805321712268066130019278766111959092164201989380952572010654858632789',\r\n\r\n\r\n  // The initial configuration properties of the Decimal constructor.\r\n  DEFAULTS = {\r\n\r\n    // These values must be integers within the stated ranges (inclusive).\r\n    // Most of these values can be changed at run-time using the `Decimal.config` method.\r\n\r\n    // The maximum number of significant digits of the result of a calculation or base conversion.\r\n    // E.g. `Decimal.config({ precision: 20 });`\r\n    precision: 20,                         // 1 to MAX_DIGITS\r\n\r\n    // The rounding mode used when rounding to `precision`.\r\n    //\r\n    // ROUND_UP         0 Away from zero.\r\n    // ROUND_DOWN       1 Towards zero.\r\n    // ROUND_CEIL       2 Towards +Infinity.\r\n    // ROUND_FLOOR      3 Towards -Infinity.\r\n    // ROUND_HALF_UP    4 Towards nearest neighbour. If equidistant, up.\r\n    // ROUND_HALF_DOWN  5 Towards nearest neighbour. If equidistant, down.\r\n    // ROUND_HALF_EVEN  6 Towards nearest neighbour. If equidistant, towards even neighbour.\r\n    // ROUND_HALF_CEIL  7 Towards nearest neighbour. If equidistant, towards +Infinity.\r\n    // ROUND_HALF_FLOOR 8 Towards nearest neighbour. If equidistant, towards -Infinity.\r\n    //\r\n    // E.g.\r\n    // `Decimal.rounding = 4;`\r\n    // `Decimal.rounding = Decimal.ROUND_HALF_UP;`\r\n    rounding: 4,                           // 0 to 8\r\n\r\n    // The modulo mode used when calculating the modulus: a mod n.\r\n    // The quotient (q = a / n) is calculated according to the corresponding rounding mode.\r\n    // The remainder (r) is calculated as: r = a - n * q.\r\n    //\r\n    // UP         0 The remainder is positive if the dividend is negative, else is negative.\r\n    // DOWN       1 The remainder has the same sign as the dividend (JavaScript %).\r\n    // FLOOR      3 The remainder has the same sign as the divisor (Python %).\r\n    // HALF_EVEN  6 The IEEE 754 remainder function.\r\n    // EUCLID     9 Euclidian division. q = sign(n) * floor(a / abs(n)). Always positive.\r\n    //\r\n    // Truncated division (1), floored division (3), the IEEE 754 remainder (6), and Euclidian\r\n    // division (9) are commonly used for the modulus operation. The other rounding modes can also\r\n    // be used, but they may not give useful results.\r\n    modulo: 1,                             // 0 to 9\r\n\r\n    // The exponent value at and beneath which `toString` returns exponential notation.\r\n    // JavaScript numbers: -7\r\n    toExpNeg: -7,                          // 0 to -EXP_LIMIT\r\n\r\n    // The exponent value at and above which `toString` returns exponential notation.\r\n    // JavaScript numbers: 21\r\n    toExpPos:  21,                         // 0 to EXP_LIMIT\r\n\r\n    // The minimum exponent value, beneath which underflow to zero occurs.\r\n    // JavaScript numbers: -324  (5e-324)\r\n    minE: -EXP_LIMIT,                      // -1 to -EXP_LIMIT\r\n\r\n    // The maximum exponent value, above which overflow to Infinity occurs.\r\n    // JavaScript numbers: 308  (1.7976931348623157e+308)\r\n    maxE: EXP_LIMIT,                       // 1 to EXP_LIMIT\r\n\r\n    // Whether to use cryptographically-secure random number generation, if available.\r\n    crypto: false                          // true/false\r\n  },\r\n\r\n\r\n// ----------------------------------- END OF EDITABLE DEFAULTS ------------------------------- //\r\n\r\n\r\n  inexact, quadrant,\r\n  external = true,\r\n\r\n  decimalError = '[DecimalError] ',\r\n  invalidArgument = decimalError + 'Invalid argument: ',\r\n  precisionLimitExceeded = decimalError + 'Precision limit exceeded',\r\n  cryptoUnavailable = decimalError + 'crypto unavailable',\r\n  tag = '[object Decimal]',\r\n\r\n  mathfloor = Math.floor,\r\n  mathpow = Math.pow,\r\n\r\n  isBinary = /^0b([01]+(\\.[01]*)?|\\.[01]+)(p[+-]?\\d+)?$/i,\r\n  isHex = /^0x([0-9a-f]+(\\.[0-9a-f]*)?|\\.[0-9a-f]+)(p[+-]?\\d+)?$/i,\r\n  isOctal = /^0o([0-7]+(\\.[0-7]*)?|\\.[0-7]+)(p[+-]?\\d+)?$/i,\r\n  isDecimal = /^(\\d+(\\.\\d*)?|\\.\\d+)(e[+-]?\\d+)?$/i,\r\n\r\n  BASE = 1e7,\r\n  LOG_BASE = 7,\r\n  MAX_SAFE_INTEGER = 9007199254740991,\r\n\r\n  LN10_PRECISION = LN10.length - 1,\r\n  PI_PRECISION = PI.length - 1,\r\n\r\n  // Decimal.prototype object\r\n  P = { toStringTag: tag };\r\n\r\n\r\n// Decimal prototype methods\r\n\r\n\r\n/*\r\n *  absoluteValue             abs\r\n *  ceil\r\n *  clampedTo                 clamp\r\n *  comparedTo                cmp\r\n *  cosine                    cos\r\n *  cubeRoot                  cbrt\r\n *  decimalPlaces             dp\r\n *  dividedBy                 div\r\n *  dividedToIntegerBy        divToInt\r\n *  equals                    eq\r\n *  floor\r\n *  greaterThan               gt\r\n *  greaterThanOrEqualTo      gte\r\n *  hyperbolicCosine          cosh\r\n *  hyperbolicSine            sinh\r\n *  hyperbolicTangent         tanh\r\n *  inverseCosine             acos\r\n *  inverseHyperbolicCosine   acosh\r\n *  inverseHyperbolicSine     asinh\r\n *  inverseHyperbolicTangent  atanh\r\n *  inverseSine               asin\r\n *  inverseTangent            atan\r\n *  isFinite\r\n *  isInteger                 isInt\r\n *  isNaN\r\n *  isNegative                isNeg\r\n *  isPositive                isPos\r\n *  isZero\r\n *  lessThan                  lt\r\n *  lessThanOrEqualTo         lte\r\n *  logarithm                 log\r\n *  [maximum]                 [max]\r\n *  [minimum]                 [min]\r\n *  minus                     sub\r\n *  modulo                    mod\r\n *  naturalExponential        exp\r\n *  naturalLogarithm          ln\r\n *  negated                   neg\r\n *  plus                      add\r\n *  precision                 sd\r\n *  round\r\n *  sine                      sin\r\n *  squareRoot                sqrt\r\n *  tangent                   tan\r\n *  times                     mul\r\n *  toBinary\r\n *  toDecimalPlaces           toDP\r\n *  toExponential\r\n *  toFixed\r\n *  toFraction\r\n *  toHexadecimal             toHex\r\n *  toNearest\r\n *  toNumber\r\n *  toOctal\r\n *  toPower                   pow\r\n *  toPrecision\r\n *  toSignificantDigits       toSD\r\n *  toString\r\n *  truncated                 trunc\r\n *  valueOf                   toJSON\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of this Decimal.\r\n *\r\n */\r\nP.absoluteValue = P.abs = function () {\r\n  var x = new this.constructor(this);\r\n  if (x.s < 0) x.s = 1;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of positive Infinity.\r\n *\r\n */\r\nP.ceil = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal clamped to the range\r\n * delineated by `min` and `max`.\r\n *\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nP.clampedTo = P.clamp = function (min, max) {\r\n  var k,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n  min = new Ctor(min);\r\n  max = new Ctor(max);\r\n  if (!min.s || !max.s) return new Ctor(NaN);\r\n  if (min.gt(max)) throw Error(invalidArgument + max);\r\n  k = x.cmp(min);\r\n  return k < 0 ? min : x.cmp(max) > 0 ? max : new Ctor(x);\r\n};\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if the value of this Decimal is greater than the value of `y`,\r\n *  -1    if the value of this Decimal is less than the value of `y`,\r\n *   0    if they have the same value,\r\n *   NaN  if the value of either Decimal is NaN.\r\n *\r\n */\r\nP.comparedTo = P.cmp = function (y) {\r\n  var i, j, xdL, ydL,\r\n    x = this,\r\n    xd = x.d,\r\n    yd = (y = new x.constructor(y)).d,\r\n    xs = x.s,\r\n    ys = y.s;\r\n\r\n  // Either NaN or ±Infinity?\r\n  if (!xd || !yd) {\r\n    return !xs || !ys ? NaN : xs !== ys ? xs : xd === yd ? 0 : !xd ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Either zero?\r\n  if (!xd[0] || !yd[0]) return xd[0] ? xs : yd[0] ? -ys : 0;\r\n\r\n  // Signs differ?\r\n  if (xs !== ys) return xs;\r\n\r\n  // Compare exponents.\r\n  if (x.e !== y.e) return x.e > y.e ^ xs < 0 ? 1 : -1;\r\n\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Compare digit by digit.\r\n  for (i = 0, j = xdL < ydL ? xdL : ydL; i < j; ++i) {\r\n    if (xd[i] !== yd[i]) return xd[i] > yd[i] ^ xs < 0 ? 1 : -1;\r\n  }\r\n\r\n  // Compare lengths.\r\n  return xdL === ydL ? 0 : xdL > ydL ^ xs < 0 ? 1 : -1;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * cos(0)         = 1\r\n * cos(-0)        = 1\r\n * cos(Infinity)  = NaN\r\n * cos(-Infinity) = NaN\r\n * cos(NaN)       = NaN\r\n *\r\n */\r\nP.cosine = P.cos = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.d) return new Ctor(NaN);\r\n\r\n  // cos(0) = cos(-0) = 1\r\n  if (!x.d[0]) return new Ctor(1);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = cosine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 3 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *\r\n * Return a new Decimal whose value is the cube root of the value of this Decimal, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n *  cbrt(0)  =  0\r\n *  cbrt(-0) = -0\r\n *  cbrt(1)  =  1\r\n *  cbrt(-1) = -1\r\n *  cbrt(N)  =  N\r\n *  cbrt(-I) = -I\r\n *  cbrt(I)  =  I\r\n *\r\n * Math.cbrt(x) = (x < 0 ? -Math.pow(-x, 1/3) : Math.pow(x, 1/3))\r\n *\r\n */\r\nP.cubeRoot = P.cbrt = function () {\r\n  var e, m, n, r, rep, s, sd, t, t3, t3plusx,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = x.s * mathpow(x.s * x, 1 / 3);\r\n\r\n   // Math.cbrt underflow/overflow?\r\n   // Pass x to Math.pow as integer, then adjust the exponent of the result.\r\n  if (!s || Math.abs(s) == 1 / 0) {\r\n    n = digitsToString(x.d);\r\n    e = x.e;\r\n\r\n    // Adjust n exponent so it is a multiple of 3 away from x exponent.\r\n    if (s = (e - n.length + 1) % 3) n += (s == 1 || s == -2 ? '0' : '00');\r\n    s = mathpow(n, 1 / 3);\r\n\r\n    // Rarely, e may be one less than the result exponent value.\r\n    e = mathfloor((e + 1) / 3) - (e % 3 == (e < 0 ? -1 : 2));\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n    r.s = x.s;\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Halley's method.\r\n  // TODO? Compare Newton's method.\r\n  for (;;) {\r\n    t = r;\r\n    t3 = t.times(t).times(t);\r\n    t3plusx = t3.plus(x);\r\n    r = divide(t3plusx.plus(x).times(t), t3plusx.plus(t3), sd + 2, 1);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or 4999\r\n      // , i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return the number of decimal places of the value of this Decimal.\r\n *\r\n */\r\nP.decimalPlaces = P.dp = function () {\r\n  var w,\r\n    d = this.d,\r\n    n = NaN;\r\n\r\n  if (d) {\r\n    w = d.length - 1;\r\n    n = (w - mathfloor(this.e / LOG_BASE)) * LOG_BASE;\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    w = d[w];\r\n    if (w) for (; w % 10 == 0; w /= 10) n--;\r\n    if (n < 0) n = 0;\r\n  }\r\n\r\n  return n;\r\n};\r\n\r\n\r\n/*\r\n *  n / 0 = I\r\n *  n / N = N\r\n *  n / I = 0\r\n *  0 / n = 0\r\n *  0 / 0 = N\r\n *  0 / N = N\r\n *  0 / I = 0\r\n *  N / n = N\r\n *  N / 0 = N\r\n *  N / N = N\r\n *  N / I = N\r\n *  I / n = I\r\n *  I / 0 = I\r\n *  I / N = N\r\n *  I / I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal divided by `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedBy = P.div = function (y) {\r\n  return divide(this, new this.constructor(y));\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the integer part of dividing the value of this Decimal\r\n * by the value of `y`, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.dividedToIntegerBy = P.divToInt = function (y) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n  return finalise(divide(x, new Ctor(y), 0, 1, 1), Ctor.precision, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is equal to the value of `y`, otherwise return false.\r\n *\r\n */\r\nP.equals = P.eq = function (y) {\r\n  return this.cmp(y) === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number in the\r\n * direction of negative Infinity.\r\n *\r\n */\r\nP.floor = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 3);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than the value of `y`, otherwise return\r\n * false.\r\n *\r\n */\r\nP.greaterThan = P.gt = function (y) {\r\n  return this.cmp(y) > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is greater than or equal to the value of `y`,\r\n * otherwise return false.\r\n *\r\n */\r\nP.greaterThanOrEqualTo = P.gte = function (y) {\r\n  var k = this.cmp(y);\r\n  return k == 1 || k === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [1, Infinity]\r\n *\r\n * cosh(x) = 1 + x^2/2! + x^4/4! + x^6/6! + ...\r\n *\r\n * cosh(0)         = 1\r\n * cosh(-0)        = 1\r\n * cosh(Infinity)  = Infinity\r\n * cosh(-Infinity) = Infinity\r\n * cosh(NaN)       = NaN\r\n *\r\n *  x        time taken (ms)   result\r\n * 1000      9                 9.8503555700852349694e+433\r\n * 10000     25                4.4034091128314607936e+4342\r\n * 100000    171               1.4033316802130615897e+43429\r\n * 1000000   3817              1.5166076984010437725e+434294\r\n * 10000000  abandoned after 2 minute wait\r\n *\r\n * TODO? Compare performance of cosh(x) = 0.5 * (exp(x) + exp(-x))\r\n *\r\n */\r\nP.hyperbolicCosine = P.cosh = function () {\r\n  var k, n, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    one = new Ctor(1);\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s ? 1 / 0 : NaN);\r\n  if (x.isZero()) return one;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  // Argument reduction: cos(4x) = 1 - 8cos^2(x) + 8cos^4(x) + 1\r\n  // i.e. cos(x) = 1 - cos^2(x/4)(8 - 8cos^2(x/4))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  // TODO? Estimation reused from cosine() and may not be optimal here.\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    n = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    n = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(n), new Ctor(1), true);\r\n\r\n  // Reverse argument reduction\r\n  var cosh2_x,\r\n    i = k,\r\n    d8 = new Ctor(8);\r\n  for (; i--;) {\r\n    cosh2_x = x.times(x);\r\n    x = one.minus(cosh2_x.times(d8.minus(cosh2_x.times(d8))));\r\n  }\r\n\r\n  return finalise(x, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * sinh(x) = x + x^3/3! + x^5/5! + x^7/7! + ...\r\n *\r\n * sinh(0)         = 0\r\n * sinh(-0)        = -0\r\n * sinh(Infinity)  = Infinity\r\n * sinh(-Infinity) = -Infinity\r\n * sinh(NaN)       = NaN\r\n *\r\n * x        time taken (ms)\r\n * 10       2 ms\r\n * 100      5 ms\r\n * 1000     14 ms\r\n * 10000    82 ms\r\n * 100000   886 ms            1.4033316802130615897e+43429\r\n * 200000   2613 ms\r\n * 300000   5407 ms\r\n * 400000   8824 ms\r\n * 500000   13026 ms          8.7080643612718084129e+217146\r\n * 1000000  48543 ms\r\n *\r\n * TODO? Compare performance of sinh(x) = 0.5 * (exp(x) - exp(-x))\r\n *\r\n */\r\nP.hyperbolicSine = P.sinh = function () {\r\n  var k, pr, rm, len,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n  } else {\r\n\r\n    // Alternative argument reduction: sinh(3x) = sinh(x)(3 + 4sinh^2(x))\r\n    // i.e. sinh(x) = sinh(x/3)(3 + 4sinh^2(x/3))\r\n    // 3 multiplications and 1 addition\r\n\r\n    // Argument reduction: sinh(5x) = sinh(x)(5 + sinh^2(x)(20 + 16sinh^2(x)))\r\n    // i.e. sinh(x) = sinh(x/5)(5 + sinh^2(x/5)(20 + 16sinh^2(x/5)))\r\n    // 4 multiplications and 2 additions\r\n\r\n    // Estimate the optimum number of times to use the argument reduction.\r\n    k = 1.4 * Math.sqrt(len);\r\n    k = k > 16 ? 16 : k | 0;\r\n\r\n    x = x.times(1 / tinyPow(5, k));\r\n    x = taylorSeries(Ctor, 2, x, x, true);\r\n\r\n    // Reverse argument reduction\r\n    var sinh2_x,\r\n      d5 = new Ctor(5),\r\n      d16 = new Ctor(16),\r\n      d20 = new Ctor(20);\r\n    for (; k--;) {\r\n      sinh2_x = x.times(x);\r\n      x = x.times(d5.plus(sinh2_x.times(d16.times(sinh2_x).plus(d20))));\r\n    }\r\n  }\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of the value in radians of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * tanh(x) = sinh(x) / cosh(x)\r\n *\r\n * tanh(0)         = 0\r\n * tanh(-0)        = -0\r\n * tanh(Infinity)  = 1\r\n * tanh(-Infinity) = -1\r\n * tanh(NaN)       = NaN\r\n *\r\n */\r\nP.hyperbolicTangent = P.tanh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(x.s);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 7;\r\n  Ctor.rounding = 1;\r\n\r\n  return divide(x.sinh(), x.cosh(), Ctor.precision = pr, Ctor.rounding = rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine (inverse cosine) in radians of the value of\r\n * this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [0, pi]\r\n *\r\n * acos(x) = pi/2 - asin(x)\r\n *\r\n * acos(0)       = pi/2\r\n * acos(-0)      = pi/2\r\n * acos(1)       = 0\r\n * acos(-1)      = pi\r\n * acos(1/2)     = pi/3\r\n * acos(-1/2)    = 2*pi/3\r\n * acos(|x| > 1) = NaN\r\n * acos(NaN)     = NaN\r\n *\r\n */\r\nP.inverseCosine = P.acos = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    k = x.abs().cmp(1),\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n    return k === 0\r\n      // |x| is 1\r\n      ? x.isNeg() ? getPi(Ctor, pr, rm) : new Ctor(0)\r\n      // |x| > 1 or x is NaN\r\n      : new Ctor(NaN);\r\n  }\r\n\r\n  if (x.isZero()) return getPi(Ctor, pr + 4, rm).times(0.5);\r\n\r\n  // TODO? Special case acos(0.5) = pi/3 and acos(-0.5) = 2*pi/3\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  // See https://github.com/MikeMcl/decimal.js/pull/217\r\n  x = new Ctor(1).minus(x).div(x.plus(1)).sqrt().atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [1, Infinity]\r\n * Range: [0, Infinity]\r\n *\r\n * acosh(x) = ln(x + sqrt(x^2 - 1))\r\n *\r\n * acosh(x < 1)     = NaN\r\n * acosh(NaN)       = NaN\r\n * acosh(Infinity)  = Infinity\r\n * acosh(-Infinity) = NaN\r\n * acosh(0)         = NaN\r\n * acosh(-0)        = NaN\r\n * acosh(1)         = 0\r\n * acosh(-1)        = NaN\r\n *\r\n */\r\nP.inverseHyperbolicCosine = P.acosh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.lte(1)) return new Ctor(x.eq(1) ? 0 : NaN);\r\n  if (!x.isFinite()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(Math.abs(x.e), x.sd()) + 4;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).minus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * asinh(x) = ln(x + sqrt(x^2 + 1))\r\n *\r\n * asinh(NaN)       = NaN\r\n * asinh(Infinity)  = Infinity\r\n * asinh(-Infinity) = -Infinity\r\n * asinh(0)         = 0\r\n * asinh(-0)        = -0\r\n *\r\n */\r\nP.inverseHyperbolicSine = P.asinh = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite() || x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 2 * Math.max(Math.abs(x.e), x.sd()) + 6;\r\n  Ctor.rounding = 1;\r\n  external = false;\r\n\r\n  x = x.times(x).plus(1).sqrt().plus(x);\r\n\r\n  external = true;\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.ln();\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent in radians of the\r\n * value of this Decimal.\r\n *\r\n * Domain: [-1, 1]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * atanh(x) = 0.5 * ln((1 + x) / (1 - x))\r\n *\r\n * atanh(|x| > 1)   = NaN\r\n * atanh(NaN)       = NaN\r\n * atanh(Infinity)  = NaN\r\n * atanh(-Infinity) = NaN\r\n * atanh(0)         = 0\r\n * atanh(-0)        = -0\r\n * atanh(1)         = Infinity\r\n * atanh(-1)        = -Infinity\r\n *\r\n */\r\nP.inverseHyperbolicTangent = P.atanh = function () {\r\n  var pr, rm, wpr, xsd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.e >= 0) return new Ctor(x.abs().eq(1) ? x.s / 0 : x.isZero() ? x : NaN);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  xsd = x.sd();\r\n\r\n  if (Math.max(xsd, pr) < 2 * -x.e - 1) return finalise(new Ctor(x), pr, rm, true);\r\n\r\n  Ctor.precision = wpr = xsd - x.e;\r\n\r\n  x = divide(x.plus(1), new Ctor(1).minus(x), wpr + pr, 1);\r\n\r\n  Ctor.precision = pr + 4;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.ln();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(0.5);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine (inverse sine) in radians of the value of this\r\n * Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * asin(x) = 2*atan(x/(1 + sqrt(1 - x^2)))\r\n *\r\n * asin(0)       = 0\r\n * asin(-0)      = -0\r\n * asin(1/2)     = pi/6\r\n * asin(-1/2)    = -pi/6\r\n * asin(1)       = pi/2\r\n * asin(-1)      = -pi/2\r\n * asin(|x| > 1) = NaN\r\n * asin(NaN)     = NaN\r\n *\r\n * TODO? Compare performance of Taylor series.\r\n *\r\n */\r\nP.inverseSine = P.asin = function () {\r\n  var halfPi, k,\r\n    pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  k = x.abs().cmp(1);\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (k !== -1) {\r\n\r\n    // |x| is 1\r\n    if (k === 0) {\r\n      halfPi = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      halfPi.s = x.s;\r\n      return halfPi;\r\n    }\r\n\r\n    // |x| > 1 or x is NaN\r\n    return new Ctor(NaN);\r\n  }\r\n\r\n  // TODO? Special case asin(1/2) = pi/6 and asin(-1/2) = -pi/6\r\n\r\n  Ctor.precision = pr + 6;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.div(new Ctor(1).minus(x.times(x)).sqrt().plus(1)).atan();\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return x.times(2);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent (inverse tangent) in radians of the value\r\n * of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi/2, pi/2]\r\n *\r\n * atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n *\r\n * atan(0)         = 0\r\n * atan(-0)        = -0\r\n * atan(1)         = pi/4\r\n * atan(-1)        = -pi/4\r\n * atan(Infinity)  = pi/2\r\n * atan(-Infinity) = -pi/2\r\n * atan(NaN)       = NaN\r\n *\r\n */\r\nP.inverseTangent = P.atan = function () {\r\n  var i, j, k, n, px, t, r, wpr, x2,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding;\r\n\r\n  if (!x.isFinite()) {\r\n    if (!x.s) return new Ctor(NaN);\r\n    if (pr + 4 <= PI_PRECISION) {\r\n      r = getPi(Ctor, pr + 4, rm).times(0.5);\r\n      r.s = x.s;\r\n      return r;\r\n    }\r\n  } else if (x.isZero()) {\r\n    return new Ctor(x);\r\n  } else if (x.abs().eq(1) && pr + 4 <= PI_PRECISION) {\r\n    r = getPi(Ctor, pr + 4, rm).times(0.25);\r\n    r.s = x.s;\r\n    return r;\r\n  }\r\n\r\n  Ctor.precision = wpr = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  // TODO? if (x >= 1 && pr <= PI_PRECISION) atan(x) = halfPi * x.s - atan(1 / x);\r\n\r\n  // Argument reduction\r\n  // Ensure |x| < 0.42\r\n  // atan(x) = 2 * atan(x / (1 + sqrt(1 + x^2)))\r\n\r\n  k = Math.min(28, wpr / LOG_BASE + 2 | 0);\r\n\r\n  for (i = k; i; --i) x = x.div(x.times(x).plus(1).sqrt().plus(1));\r\n\r\n  external = false;\r\n\r\n  j = Math.ceil(wpr / LOG_BASE);\r\n  n = 1;\r\n  x2 = x.times(x);\r\n  r = new Ctor(x);\r\n  px = x;\r\n\r\n  // atan(x) = x - x^3/3 + x^5/5 - x^7/7 + ...\r\n  for (; i !== -1;) {\r\n    px = px.times(x2);\r\n    t = r.minus(px.div(n += 2));\r\n\r\n    px = px.times(x2);\r\n    r = t.plus(px.div(n += 2));\r\n\r\n    if (r.d[j] !== void 0) for (i = j; r.d[i] === t.d[i] && i--;);\r\n  }\r\n\r\n  if (k) r = r.times(2 << (k - 1));\r\n\r\n  external = true;\r\n\r\n  return finalise(r, Ctor.precision = pr, Ctor.rounding = rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is a finite number, otherwise return false.\r\n *\r\n */\r\nP.isFinite = function () {\r\n  return !!this.d;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is an integer, otherwise return false.\r\n *\r\n */\r\nP.isInteger = P.isInt = function () {\r\n  return !!this.d && mathfloor(this.e / LOG_BASE) > this.d.length - 2;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is NaN, otherwise return false.\r\n *\r\n */\r\nP.isNaN = function () {\r\n  return !this.s;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is negative, otherwise return false.\r\n *\r\n */\r\nP.isNegative = P.isNeg = function () {\r\n  return this.s < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is positive, otherwise return false.\r\n *\r\n */\r\nP.isPositive = P.isPos = function () {\r\n  return this.s > 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is 0 or -0, otherwise return false.\r\n *\r\n */\r\nP.isZero = function () {\r\n  return !!this.d && this.d[0] === 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThan = P.lt = function (y) {\r\n  return this.cmp(y) < 0;\r\n};\r\n\r\n\r\n/*\r\n * Return true if the value of this Decimal is less than or equal to `y`, otherwise return false.\r\n *\r\n */\r\nP.lessThanOrEqualTo = P.lte = function (y) {\r\n  return this.cmp(y) < 1;\r\n};\r\n\r\n\r\n/*\r\n * Return the logarithm of the value of this Decimal to the specified base, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * If no base is specified, return log[10](arg).\r\n *\r\n * log[base](arg) = ln(arg) / ln(base)\r\n *\r\n * The result will always be correctly rounded if the base of the log is 10, and 'almost always'\r\n * otherwise:\r\n *\r\n * Depending on the rounding mode, the result may be incorrectly rounded if the first fifteen\r\n * rounding digits are [49]99999999999999 or [50]00000000000000. In that case, the maximum error\r\n * between the result and the correctly rounded result will be one ulp (unit in the last place).\r\n *\r\n * log[-b](a)       = NaN\r\n * log[0](a)        = NaN\r\n * log[1](a)        = NaN\r\n * log[NaN](a)      = NaN\r\n * log[Infinity](a) = NaN\r\n * log[b](0)        = -Infinity\r\n * log[b](-0)       = -Infinity\r\n * log[b](-a)       = NaN\r\n * log[b](1)        = 0\r\n * log[b](Infinity) = Infinity\r\n * log[b](NaN)      = NaN\r\n *\r\n * [base] {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nP.logarithm = P.log = function (base) {\r\n  var isBase10, d, denominator, k, inf, num, sd, r,\r\n    arg = this,\r\n    Ctor = arg.constructor,\r\n    pr = Ctor.precision,\r\n    rm = Ctor.rounding,\r\n    guard = 5;\r\n\r\n  // Default base is 10.\r\n  if (base == null) {\r\n    base = new Ctor(10);\r\n    isBase10 = true;\r\n  } else {\r\n    base = new Ctor(base);\r\n    d = base.d;\r\n\r\n    // Return NaN if base is negative, or non-finite, or is 0 or 1.\r\n    if (base.s < 0 || !d || !d[0] || base.eq(1)) return new Ctor(NaN);\r\n\r\n    isBase10 = base.eq(10);\r\n  }\r\n\r\n  d = arg.d;\r\n\r\n  // Is arg negative, non-finite, 0 or 1?\r\n  if (arg.s < 0 || !d || !d[0] || arg.eq(1)) {\r\n    return new Ctor(d && !d[0] ? -1 / 0 : arg.s != 1 ? NaN : d ? 0 : 1 / 0);\r\n  }\r\n\r\n  // The result will have a non-terminating decimal expansion if base is 10 and arg is not an\r\n  // integer power of 10.\r\n  if (isBase10) {\r\n    if (d.length > 1) {\r\n      inf = true;\r\n    } else {\r\n      for (k = d[0]; k % 10 === 0;) k /= 10;\r\n      inf = k !== 1;\r\n    }\r\n  }\r\n\r\n  external = false;\r\n  sd = pr + guard;\r\n  num = naturalLogarithm(arg, sd);\r\n  denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n\r\n  // The result will have 5 rounding digits.\r\n  r = divide(num, denominator, sd, 1);\r\n\r\n  // If at a rounding boundary, i.e. the result's rounding digits are [49]9999 or [50]0000,\r\n  // calculate 10 further digits.\r\n  //\r\n  // If the result is known to have an infinite decimal expansion, repeat this until it is clear\r\n  // that the result is above or below the boundary. Otherwise, if after calculating the 10\r\n  // further digits, the last 14 are nines, round up and assume the result is exact.\r\n  // Also assume the result is exact if the last 14 are zero.\r\n  //\r\n  // Example of a result that will be incorrectly rounded:\r\n  // log[1048576](4503599627370502) = 2.60000000000000009610279511444746...\r\n  // The above result correctly rounded using ROUND_CEIL to 1 decimal place should be 2.7, but it\r\n  // will be given as 2.6 as there are 15 zeros immediately after the requested decimal place, so\r\n  // the exact result would be assumed to be 2.6, which rounded using ROUND_CEIL to 1 decimal\r\n  // place is still 2.6.\r\n  if (checkRoundingDigits(r.d, k = pr, rm)) {\r\n\r\n    do {\r\n      sd += 10;\r\n      num = naturalLogarithm(arg, sd);\r\n      denominator = isBase10 ? getLn10(Ctor, sd + 10) : naturalLogarithm(base, sd);\r\n      r = divide(num, denominator, sd, 1);\r\n\r\n      if (!inf) {\r\n\r\n        // Check for 14 nines from the 2nd rounding digit, as the first may be 4.\r\n        if (+digitsToString(r.d).slice(k + 1, k + 15) + 1 == 1e14) {\r\n          r = finalise(r, pr + 1, 0);\r\n        }\r\n\r\n        break;\r\n      }\r\n    } while (checkRoundingDigits(r.d, k += 10, rm));\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.max = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, -1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments and the value of this Decimal.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\nP.min = function () {\r\n  Array.prototype.push.call(arguments, this);\r\n  return maxOrMin(this.constructor, arguments, 1);\r\n};\r\n */\r\n\r\n\r\n/*\r\n *  n - 0 = n\r\n *  n - N = N\r\n *  n - I = -I\r\n *  0 - n = -n\r\n *  0 - 0 = 0\r\n *  0 - N = N\r\n *  0 - I = -I\r\n *  N - n = N\r\n *  N - 0 = N\r\n *  N - N = N\r\n *  N - I = N\r\n *  I - n = I\r\n *  I - 0 = I\r\n *  I - N = N\r\n *  I - I = N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal minus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.minus = P.sub = function (y) {\r\n  var d, e, i, j, k, len, pr, rm, xd, xe, xLTy, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return y negated if x is finite and y is ±Infinity.\r\n    else if (x.d) y.s = -y.s;\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with different signs.\r\n    // Return NaN if both are ±Infinity with the same sign.\r\n    else y = new Ctor(y.d || x.s !== y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n  // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.plus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return y negated if x is zero and y is non-zero.\r\n    if (yd[0]) y.s = -y.s;\r\n\r\n    // Return x if y is zero and x is non-zero.\r\n    else if (xd[0]) y = new Ctor(x);\r\n\r\n    // Return zero if both are zero.\r\n    // From IEEE 754 (2008) 6.3: 0 - 0 = -0 - -0 = -0 when rounding to -Infinity.\r\n    else return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  e = mathfloor(y.e / LOG_BASE);\r\n  xe = mathfloor(x.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  k = xe - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (k) {\r\n    xLTy = k < 0;\r\n\r\n    if (xLTy) {\r\n      d = xd;\r\n      k = -k;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = xe;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Numbers with massively different exponents would result in a very high number of\r\n    // zeros needing to be prepended, but this can be avoided while still ensuring correct\r\n    // rounding by limiting the number of zeros to `Math.ceil(pr / LOG_BASE) + 2`.\r\n    i = Math.max(Math.ceil(pr / LOG_BASE), len) + 2;\r\n\r\n    if (k > i) {\r\n      k = i;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents.\r\n    d.reverse();\r\n    for (i = k; i--;) d.push(0);\r\n    d.reverse();\r\n\r\n  // Base 1e7 exponents equal.\r\n  } else {\r\n\r\n    // Check digits to determine which is the bigger number.\r\n\r\n    i = xd.length;\r\n    len = yd.length;\r\n    xLTy = i < len;\r\n    if (xLTy) len = i;\r\n\r\n    for (i = 0; i < len; i++) {\r\n      if (xd[i] != yd[i]) {\r\n        xLTy = xd[i] < yd[i];\r\n        break;\r\n      }\r\n    }\r\n\r\n    k = 0;\r\n  }\r\n\r\n  if (xLTy) {\r\n    d = xd;\r\n    xd = yd;\r\n    yd = d;\r\n    y.s = -y.s;\r\n  }\r\n\r\n  len = xd.length;\r\n\r\n  // Append zeros to `xd` if shorter.\r\n  // Don't add zeros to `yd` if shorter as subtraction only needs to start at `yd` length.\r\n  for (i = yd.length - len; i > 0; --i) xd[len++] = 0;\r\n\r\n  // Subtract yd from xd.\r\n  for (i = yd.length; i > k;) {\r\n\r\n    if (xd[--i] < yd[i]) {\r\n      for (j = i; j && xd[--j] === 0;) xd[j] = BASE - 1;\r\n      --xd[j];\r\n      xd[i] += BASE;\r\n    }\r\n\r\n    xd[i] -= yd[i];\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; xd[--len] === 0;) xd.pop();\r\n\r\n  // Remove leading zeros and adjust exponent accordingly.\r\n  for (; xd[0] === 0; xd.shift()) --e;\r\n\r\n  // Zero?\r\n  if (!xd[0]) return new Ctor(rm === 3 ? -0 : 0);\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n *   n % 0 =  N\r\n *   n % N =  N\r\n *   n % I =  n\r\n *   0 % n =  0\r\n *  -0 % n = -0\r\n *   0 % 0 =  N\r\n *   0 % N =  N\r\n *   0 % I =  0\r\n *   N % n =  N\r\n *   N % 0 =  N\r\n *   N % N =  N\r\n *   N % I =  N\r\n *   I % n =  N\r\n *   I % 0 =  N\r\n *   I % N =  N\r\n *   I % I =  N\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal modulo `y`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * The result depends on the modulo mode.\r\n *\r\n */\r\nP.modulo = P.mod = function (y) {\r\n  var q,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // Return NaN if x is ±Infinity or NaN, or y is NaN or ±0.\r\n  if (!x.d || !y.s || y.d && !y.d[0]) return new Ctor(NaN);\r\n\r\n  // Return x if y is ±Infinity or x is ±0.\r\n  if (!y.d || x.d && !x.d[0]) {\r\n    return finalise(new Ctor(x), Ctor.precision, Ctor.rounding);\r\n  }\r\n\r\n  // Prevent rounding of intermediate calculations.\r\n  external = false;\r\n\r\n  if (Ctor.modulo == 9) {\r\n\r\n    // Euclidian division: q = sign(y) * floor(x / abs(y))\r\n    // result = x - q * y    where  0 <= result < abs(y)\r\n    q = divide(x, y.abs(), 0, 3, 1);\r\n    q.s *= y.s;\r\n  } else {\r\n    q = divide(x, y, 0, Ctor.modulo, 1);\r\n  }\r\n\r\n  q = q.times(y);\r\n\r\n  external = true;\r\n\r\n  return x.minus(q);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of the value of this Decimal,\r\n * i.e. the base e raised to the power the value of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalExponential = P.exp = function () {\r\n  return naturalExponential(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of the value of this Decimal,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.naturalLogarithm = P.ln = function () {\r\n  return naturalLogarithm(this);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal negated, i.e. as if multiplied by\r\n * -1.\r\n *\r\n */\r\nP.negated = P.neg = function () {\r\n  var x = new this.constructor(this);\r\n  x.s = -x.s;\r\n  return finalise(x);\r\n};\r\n\r\n\r\n/*\r\n *  n + 0 = n\r\n *  n + N = N\r\n *  n + I = I\r\n *  0 + n = n\r\n *  0 + 0 = 0\r\n *  0 + N = N\r\n *  0 + I = I\r\n *  N + n = N\r\n *  N + 0 = N\r\n *  N + N = N\r\n *  N + I = N\r\n *  I + n = I\r\n *  I + 0 = I\r\n *  I + N = N\r\n *  I + I = I\r\n *\r\n * Return a new Decimal whose value is the value of this Decimal plus `y`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.plus = P.add = function (y) {\r\n  var carry, d, e, i, k, len, pr, rm, xd, yd,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  y = new Ctor(y);\r\n\r\n  // If either is not finite...\r\n  if (!x.d || !y.d) {\r\n\r\n    // Return NaN if either is NaN.\r\n    if (!x.s || !y.s) y = new Ctor(NaN);\r\n\r\n    // Return x if y is finite and x is ±Infinity.\r\n    // Return x if both are ±Infinity with the same sign.\r\n    // Return NaN if both are ±Infinity with different signs.\r\n    // Return y if x is finite and y is ±Infinity.\r\n    else if (!x.d) y = new Ctor(y.d || x.s === y.s ? x : NaN);\r\n\r\n    return y;\r\n  }\r\n\r\n   // If signs differ...\r\n  if (x.s != y.s) {\r\n    y.s = -y.s;\r\n    return x.minus(y);\r\n  }\r\n\r\n  xd = x.d;\r\n  yd = y.d;\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  // If either is zero...\r\n  if (!xd[0] || !yd[0]) {\r\n\r\n    // Return x if y is zero.\r\n    // Return y if y is non-zero.\r\n    if (!yd[0]) y = new Ctor(x);\r\n\r\n    return external ? finalise(y, pr, rm) : y;\r\n  }\r\n\r\n  // x and y are finite, non-zero numbers with the same sign.\r\n\r\n  // Calculate base 1e7 exponents.\r\n  k = mathfloor(x.e / LOG_BASE);\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  xd = xd.slice();\r\n  i = k - e;\r\n\r\n  // If base 1e7 exponents differ...\r\n  if (i) {\r\n\r\n    if (i < 0) {\r\n      d = xd;\r\n      i = -i;\r\n      len = yd.length;\r\n    } else {\r\n      d = yd;\r\n      e = k;\r\n      len = xd.length;\r\n    }\r\n\r\n    // Limit number of zeros prepended to max(ceil(pr / LOG_BASE), len) + 1.\r\n    k = Math.ceil(pr / LOG_BASE);\r\n    len = k > len ? k + 1 : len + 1;\r\n\r\n    if (i > len) {\r\n      i = len;\r\n      d.length = 1;\r\n    }\r\n\r\n    // Prepend zeros to equalise exponents. Note: Faster to use reverse then do unshifts.\r\n    d.reverse();\r\n    for (; i--;) d.push(0);\r\n    d.reverse();\r\n  }\r\n\r\n  len = xd.length;\r\n  i = yd.length;\r\n\r\n  // If yd is longer than xd, swap xd and yd so xd points to the longer array.\r\n  if (len - i < 0) {\r\n    i = len;\r\n    d = yd;\r\n    yd = xd;\r\n    xd = d;\r\n  }\r\n\r\n  // Only start adding at yd.length - 1 as the further digits of xd can be left as they are.\r\n  for (carry = 0; i;) {\r\n    carry = (xd[--i] = xd[i] + yd[i] + carry) / BASE | 0;\r\n    xd[i] %= BASE;\r\n  }\r\n\r\n  if (carry) {\r\n    xd.unshift(carry);\r\n    ++e;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  // No need to check for zero, as +x + +y != 0 && -x + -y != 0\r\n  for (len = xd.length; xd[--len] == 0;) xd.pop();\r\n\r\n  y.d = xd;\r\n  y.e = getBase10Exponent(xd, e);\r\n\r\n  return external ? finalise(y, pr, rm) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return the number of significant digits of the value of this Decimal.\r\n *\r\n * [z] {boolean|number} Whether to count integer-part trailing zeros: true, false, 1 or 0.\r\n *\r\n */\r\nP.precision = P.sd = function (z) {\r\n  var k,\r\n    x = this;\r\n\r\n  if (z !== void 0 && z !== !!z && z !== 1 && z !== 0) throw Error(invalidArgument + z);\r\n\r\n  if (x.d) {\r\n    k = getPrecision(x.d);\r\n    if (z && x.e + 1 > k) k = x.e + 1;\r\n  } else {\r\n    k = NaN;\r\n  }\r\n\r\n  return k;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a whole number using\r\n * rounding mode `rounding`.\r\n *\r\n */\r\nP.round = function () {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  return finalise(new Ctor(x), x.e + 1, Ctor.rounding);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-1, 1]\r\n *\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n *\r\n * sin(0)         = 0\r\n * sin(-0)        = -0\r\n * sin(Infinity)  = NaN\r\n * sin(-Infinity) = NaN\r\n * sin(NaN)       = NaN\r\n *\r\n */\r\nP.sine = P.sin = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + Math.max(x.e, x.sd()) + LOG_BASE;\r\n  Ctor.rounding = 1;\r\n\r\n  x = sine(Ctor, toLessThanHalfPi(Ctor, x));\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant > 2 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of this Decimal, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n *  sqrt(-n) =  N\r\n *  sqrt(N)  =  N\r\n *  sqrt(-I) =  N\r\n *  sqrt(I)  =  I\r\n *  sqrt(0)  =  0\r\n *  sqrt(-0) = -0\r\n *\r\n */\r\nP.squareRoot = P.sqrt = function () {\r\n  var m, n, sd, r, rep, t,\r\n    x = this,\r\n    d = x.d,\r\n    e = x.e,\r\n    s = x.s,\r\n    Ctor = x.constructor;\r\n\r\n  // Negative/NaN/Infinity/zero?\r\n  if (s !== 1 || !d || !d[0]) {\r\n    return new Ctor(!s || s < 0 && (!d || d[0]) ? NaN : d ? x : 1 / 0);\r\n  }\r\n\r\n  external = false;\r\n\r\n  // Initial estimate.\r\n  s = Math.sqrt(+x);\r\n\r\n  // Math.sqrt underflow/overflow?\r\n  // Pass x to Math.sqrt as integer, then adjust the exponent of the result.\r\n  if (s == 0 || s == 1 / 0) {\r\n    n = digitsToString(d);\r\n\r\n    if ((n.length + e) % 2 == 0) n += '0';\r\n    s = Math.sqrt(n);\r\n    e = mathfloor((e + 1) / 2) - (e < 0 || e % 2);\r\n\r\n    if (s == 1 / 0) {\r\n      n = '5e' + e;\r\n    } else {\r\n      n = s.toExponential();\r\n      n = n.slice(0, n.indexOf('e') + 1) + e;\r\n    }\r\n\r\n    r = new Ctor(n);\r\n  } else {\r\n    r = new Ctor(s.toString());\r\n  }\r\n\r\n  sd = (e = Ctor.precision) + 3;\r\n\r\n  // Newton-Raphson iteration.\r\n  for (;;) {\r\n    t = r;\r\n    r = t.plus(divide(x, t, sd + 2, 1)).times(0.5);\r\n\r\n    // TODO? Replace with for-loop and checkRoundingDigits.\r\n    if (digitsToString(t.d).slice(0, sd) === (n = digitsToString(r.d)).slice(0, sd)) {\r\n      n = n.slice(sd - 3, sd + 1);\r\n\r\n      // The 4th rounding digit may be in error by -1 so if the 4 rounding digits are 9999 or\r\n      // 4999, i.e. approaching a rounding boundary, continue the iteration.\r\n      if (n == '9999' || !rep && n == '4999') {\r\n\r\n        // On the first iteration only, check to see if rounding up gives the exact result as the\r\n        // nines may infinitely repeat.\r\n        if (!rep) {\r\n          finalise(t, e + 1, 0);\r\n\r\n          if (t.times(t).eq(x)) {\r\n            r = t;\r\n            break;\r\n          }\r\n        }\r\n\r\n        sd += 4;\r\n        rep = 1;\r\n      } else {\r\n\r\n        // If the rounding digits are null, 0{0,4} or 50{0,3}, check for an exact result.\r\n        // If not, then there are further digits and m will be truthy.\r\n        if (!+n || !+n.slice(1) && n.charAt(0) == '5') {\r\n\r\n          // Truncate to the first rounding digit.\r\n          finalise(r, e + 1, 1);\r\n          m = !r.times(r).eq(x);\r\n        }\r\n\r\n        break;\r\n      }\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return finalise(r, e, Ctor.rounding, m);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of the value in radians of this Decimal.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-Infinity, Infinity]\r\n *\r\n * tan(0)         = 0\r\n * tan(-0)        = -0\r\n * tan(Infinity)  = NaN\r\n * tan(-Infinity) = NaN\r\n * tan(NaN)       = NaN\r\n *\r\n */\r\nP.tangent = P.tan = function () {\r\n  var pr, rm,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (!x.isFinite()) return new Ctor(NaN);\r\n  if (x.isZero()) return new Ctor(x);\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n  Ctor.precision = pr + 10;\r\n  Ctor.rounding = 1;\r\n\r\n  x = x.sin();\r\n  x.s = 1;\r\n  x = divide(x, new Ctor(1).minus(x.times(x)).sqrt(), pr + 10, 0);\r\n\r\n  Ctor.precision = pr;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(quadrant == 2 || quadrant == 4 ? x.neg() : x, pr, rm, true);\r\n};\r\n\r\n\r\n/*\r\n *  n * 0 = 0\r\n *  n * N = N\r\n *  n * I = I\r\n *  0 * n = 0\r\n *  0 * 0 = 0\r\n *  0 * N = N\r\n *  0 * I = N\r\n *  N * n = N\r\n *  N * 0 = N\r\n *  N * N = N\r\n *  N * I = N\r\n *  I * n = I\r\n *  I * 0 = N\r\n *  I * N = N\r\n *  I * I = I\r\n *\r\n * Return a new Decimal whose value is this Decimal times `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n */\r\nP.times = P.mul = function (y) {\r\n  var carry, e, i, k, r, rL, t, xdL, ydL,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    xd = x.d,\r\n    yd = (y = new Ctor(y)).d;\r\n\r\n  y.s *= x.s;\r\n\r\n   // If either is NaN, ±Infinity or ±0...\r\n  if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n    return new Ctor(!y.s || xd && !xd[0] && !yd || yd && !yd[0] && !xd\r\n\r\n      // Return NaN if either is NaN.\r\n      // Return NaN if x is ±0 and y is ±Infinity, or y is ±0 and x is ±Infinity.\r\n      ? NaN\r\n\r\n      // Return ±Infinity if either is ±Infinity.\r\n      // Return ±0 if either is ±0.\r\n      : !xd || !yd ? y.s / 0 : y.s * 0);\r\n  }\r\n\r\n  e = mathfloor(x.e / LOG_BASE) + mathfloor(y.e / LOG_BASE);\r\n  xdL = xd.length;\r\n  ydL = yd.length;\r\n\r\n  // Ensure xd points to the longer array.\r\n  if (xdL < ydL) {\r\n    r = xd;\r\n    xd = yd;\r\n    yd = r;\r\n    rL = xdL;\r\n    xdL = ydL;\r\n    ydL = rL;\r\n  }\r\n\r\n  // Initialise the result array with zeros.\r\n  r = [];\r\n  rL = xdL + ydL;\r\n  for (i = rL; i--;) r.push(0);\r\n\r\n  // Multiply!\r\n  for (i = ydL; --i >= 0;) {\r\n    carry = 0;\r\n    for (k = xdL + i; k > i;) {\r\n      t = r[k] + yd[i] * xd[k - i - 1] + carry;\r\n      r[k--] = t % BASE | 0;\r\n      carry = t / BASE | 0;\r\n    }\r\n\r\n    r[k] = (r[k] + carry) % BASE | 0;\r\n  }\r\n\r\n  // Remove trailing zeros.\r\n  for (; !r[--rL];) r.pop();\r\n\r\n  if (carry) ++e;\r\n  else r.shift();\r\n\r\n  y.d = r;\r\n  y.e = getBase10Exponent(r, e);\r\n\r\n  return external ? finalise(y, Ctor.precision, Ctor.rounding) : y;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 2, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toBinary = function (sd, rm) {\r\n  return toStringBinary(this, 2, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `dp`\r\n * decimal places using rounding mode `rm` or `rounding` if `rm` is omitted.\r\n *\r\n * If `dp` is omitted, return a new Decimal whose value is the value of this Decimal.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toDecimalPlaces = P.toDP = function (dp, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n  if (dp === void 0) return x;\r\n\r\n  checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n  if (rm === void 0) rm = Ctor.rounding;\r\n  else checkInt32(rm, 0, 8);\r\n\r\n  return finalise(x, dp + x.e + 1, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in exponential notation rounded to\r\n * `dp` fixed decimal places using rounding mode `rounding`.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toExponential = function (dp, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x, true);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), dp + 1, rm);\r\n    str = finiteToString(x, true, dp + 1);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in normal (fixed-point) notation to\r\n * `dp` fixed decimal places and rounded using rounding mode `rm` or `rounding` if `rm` is\r\n * omitted.\r\n *\r\n * As with JavaScript numbers, (-0).toFixed(0) is '0', but e.g. (-0.00001).toFixed(0) is '-0'.\r\n *\r\n * [dp] {number} Decimal places. Integer, 0 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * (-0).toFixed(0) is '0', but (-0.1).toFixed(0) is '-0'.\r\n * (-0).toFixed(1) is '0.0', but (-0.01).toFixed(1) is '-0.0'.\r\n * (-0).toFixed(3) is '0.000'.\r\n * (-0.5).toFixed(0) is '-0'.\r\n *\r\n */\r\nP.toFixed = function (dp, rm) {\r\n  var str, y,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (dp === void 0) {\r\n    str = finiteToString(x);\r\n  } else {\r\n    checkInt32(dp, 0, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    y = finalise(new Ctor(x), dp + x.e + 1, rm);\r\n    str = finiteToString(y, false, dp + y.e + 1);\r\n  }\r\n\r\n  // To determine whether to add the minus sign look at the value before it was rounded,\r\n  // i.e. look at `x` rather than `y`.\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return an array representing the value of this Decimal as a simple fraction with an integer\r\n * numerator and an integer denominator.\r\n *\r\n * The denominator will be a positive non-zero value less than or equal to the specified maximum\r\n * denominator. If a maximum denominator is not specified, the denominator will be the lowest\r\n * value necessary to represent the number exactly.\r\n *\r\n * [maxD] {number|string|bigint|Decimal} Maximum denominator. Integer >= 1 and < Infinity.\r\n *\r\n */\r\nP.toFraction = function (maxD) {\r\n  var d, d0, d1, d2, e, k, n, n0, n1, pr, q, r,\r\n    x = this,\r\n    xd = x.d,\r\n    Ctor = x.constructor;\r\n\r\n  if (!xd) return new Ctor(x);\r\n\r\n  n1 = d0 = new Ctor(1);\r\n  d1 = n0 = new Ctor(0);\r\n\r\n  d = new Ctor(d1);\r\n  e = d.e = getPrecision(xd) - x.e - 1;\r\n  k = e % LOG_BASE;\r\n  d.d[0] = mathpow(10, k < 0 ? LOG_BASE + k : k);\r\n\r\n  if (maxD == null) {\r\n\r\n    // d is 10**e, the minimum max-denominator needed.\r\n    maxD = e > 0 ? d : n1;\r\n  } else {\r\n    n = new Ctor(maxD);\r\n    if (!n.isInt() || n.lt(n1)) throw Error(invalidArgument + n);\r\n    maxD = n.gt(d) ? (e > 0 ? d : n1) : n;\r\n  }\r\n\r\n  external = false;\r\n  n = new Ctor(digitsToString(xd));\r\n  pr = Ctor.precision;\r\n  Ctor.precision = e = xd.length * LOG_BASE * 2;\r\n\r\n  for (;;)  {\r\n    q = divide(n, d, 0, 1, 1);\r\n    d2 = d0.plus(q.times(d1));\r\n    if (d2.cmp(maxD) == 1) break;\r\n    d0 = d1;\r\n    d1 = d2;\r\n    d2 = n1;\r\n    n1 = n0.plus(q.times(d2));\r\n    n0 = d2;\r\n    d2 = d;\r\n    d = n.minus(q.times(d2));\r\n    n = d2;\r\n  }\r\n\r\n  d2 = divide(maxD.minus(d0), d1, 0, 1, 1);\r\n  n0 = n0.plus(d2.times(n1));\r\n  d0 = d0.plus(d2.times(d1));\r\n  n0.s = n1.s = x.s;\r\n\r\n  // Determine which fraction is closer to x, n0/d0 or n1/d1?\r\n  r = divide(n1, d1, e, 1).minus(x).abs().cmp(divide(n0, d0, e, 1).minus(x).abs()) < 1\r\n      ? [n1, d1] : [n0, d0];\r\n\r\n  Ctor.precision = pr;\r\n  external = true;\r\n\r\n  return r;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 16, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toHexadecimal = P.toHex = function (sd, rm) {\r\n  return toStringBinary(this, 16, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Returns a new Decimal whose value is the nearest multiple of `y` in the direction of rounding\r\n * mode `rm`, or `Decimal.rounding` if `rm` is omitted, to the value of this Decimal.\r\n *\r\n * The return value will always have the same sign as this Decimal, unless either this Decimal\r\n * or `y` is NaN, in which case the return value will be also be NaN.\r\n *\r\n * The return value is not affected by the value of `precision`.\r\n *\r\n * y {number|string|bigint|Decimal} The magnitude to round to a multiple of.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toNearest() rounding mode not an integer: {rm}'\r\n * 'toNearest() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toNearest = function (y, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (y == null) {\r\n\r\n    // If x is not finite, return x.\r\n    if (!x.d) return x;\r\n\r\n    y = new Ctor(1);\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    y = new Ctor(y);\r\n    if (rm === void 0) {\r\n      rm = Ctor.rounding;\r\n    } else {\r\n      checkInt32(rm, 0, 8);\r\n    }\r\n\r\n    // If x is not finite, return x if y is not NaN, else NaN.\r\n    if (!x.d) return y.s ? x : y;\r\n\r\n    // If y is not finite, return Infinity with the sign of x if y is Infinity, else NaN.\r\n    if (!y.d) {\r\n      if (y.s) y.s = x.s;\r\n      return y;\r\n    }\r\n  }\r\n\r\n  // If y is not zero, calculate the nearest multiple of y to x.\r\n  if (y.d[0]) {\r\n    external = false;\r\n    x = divide(x, y, 0, rm, 1).times(y);\r\n    external = true;\r\n    finalise(x);\r\n\r\n  // If y is zero, return zero with the sign of x.\r\n  } else {\r\n    y.s = x.s;\r\n    x = y;\r\n  }\r\n\r\n  return x;\r\n};\r\n\r\n\r\n/*\r\n * Return the value of this Decimal converted to a number primitive.\r\n * Zero keeps its sign.\r\n *\r\n */\r\nP.toNumber = function () {\r\n  return +this;\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal in base 8, round to `sd` significant\r\n * digits using rounding mode `rm`.\r\n *\r\n * If the optional `sd` argument is present then return binary exponential notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toOctal = function (sd, rm) {\r\n  return toStringBinary(this, 8, sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal raised to the power `y`, rounded\r\n * to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * ECMAScript compliant.\r\n *\r\n *   pow(x, NaN)                           = NaN\r\n *   pow(x, ±0)                            = 1\r\n\r\n *   pow(NaN, non-zero)                    = NaN\r\n *   pow(abs(x) > 1, +Infinity)            = +Infinity\r\n *   pow(abs(x) > 1, -Infinity)            = +0\r\n *   pow(abs(x) == 1, ±Infinity)           = NaN\r\n *   pow(abs(x) < 1, +Infinity)            = +0\r\n *   pow(abs(x) < 1, -Infinity)            = +Infinity\r\n *   pow(+Infinity, y > 0)                 = +Infinity\r\n *   pow(+Infinity, y < 0)                 = +0\r\n *   pow(-Infinity, odd integer > 0)       = -Infinity\r\n *   pow(-Infinity, even integer > 0)      = +Infinity\r\n *   pow(-Infinity, odd integer < 0)       = -0\r\n *   pow(-Infinity, even integer < 0)      = +0\r\n *   pow(+0, y > 0)                        = +0\r\n *   pow(+0, y < 0)                        = +Infinity\r\n *   pow(-0, odd integer > 0)              = -0\r\n *   pow(-0, even integer > 0)             = +0\r\n *   pow(-0, odd integer < 0)              = -Infinity\r\n *   pow(-0, even integer < 0)             = +Infinity\r\n *   pow(finite x < 0, finite non-integer) = NaN\r\n *\r\n * For non-integer or very large exponents pow(x, y) is calculated using\r\n *\r\n *   x^y = exp(y*ln(x))\r\n *\r\n * Assuming the first 15 rounding digits are each equally likely to be any digit 0-9, the\r\n * probability of an incorrectly rounded result\r\n * P([49]9{14} | [50]0{14}) = 2 * 0.2 * 10^-14 = 4e-15 = 1/2.5e+14\r\n * i.e. 1 in 250,000,000,000,000\r\n *\r\n * If a result is incorrectly rounded the maximum error will be 1 ulp (unit in last place).\r\n *\r\n * y {number|string|bigint|Decimal} The power to which to raise this Decimal.\r\n *\r\n */\r\nP.toPower = P.pow = function (y) {\r\n  var e, k, pr, r, rm, s,\r\n    x = this,\r\n    Ctor = x.constructor,\r\n    yn = +(y = new Ctor(y));\r\n\r\n  // Either ±Infinity, NaN or ±0?\r\n  if (!x.d || !y.d || !x.d[0] || !y.d[0]) return new Ctor(mathpow(+x, yn));\r\n\r\n  x = new Ctor(x);\r\n\r\n  if (x.eq(1)) return x;\r\n\r\n  pr = Ctor.precision;\r\n  rm = Ctor.rounding;\r\n\r\n  if (y.eq(1)) return finalise(x, pr, rm);\r\n\r\n  // y exponent\r\n  e = mathfloor(y.e / LOG_BASE);\r\n\r\n  // If y is a small integer use the 'exponentiation by squaring' algorithm.\r\n  if (e >= y.d.length - 1 && (k = yn < 0 ? -yn : yn) <= MAX_SAFE_INTEGER) {\r\n    r = intPow(Ctor, x, k, pr);\r\n    return y.s < 0 ? new Ctor(1).div(r) : finalise(r, pr, rm);\r\n  }\r\n\r\n  s = x.s;\r\n\r\n  // if x is negative\r\n  if (s < 0) {\r\n\r\n    // if y is not an integer\r\n    if (e < y.d.length - 1) return new Ctor(NaN);\r\n\r\n    // Result is positive if x is negative and the last digit of integer y is even.\r\n    if ((y.d[e] & 1) == 0) s = 1;\r\n\r\n    // if x.eq(-1)\r\n    if (x.e == 0 && x.d[0] == 1 && x.d.length == 1) {\r\n      x.s = s;\r\n      return x;\r\n    }\r\n  }\r\n\r\n  // Estimate result exponent.\r\n  // x^y = 10^e,  where e = y * log10(x)\r\n  // log10(x) = log10(x_significand) + x_exponent\r\n  // log10(x_significand) = ln(x_significand) / ln(10)\r\n  k = mathpow(+x, yn);\r\n  e = k == 0 || !isFinite(k)\r\n    ? mathfloor(yn * (Math.log('0.' + digitsToString(x.d)) / Math.LN10 + x.e + 1))\r\n    : new Ctor(k + '').e;\r\n\r\n  // Exponent estimate may be incorrect e.g. x: 0.999999999999999999, y: 2.29, e: 0, r.e: -1.\r\n\r\n  // Overflow/underflow?\r\n  if (e > Ctor.maxE + 1 || e < Ctor.minE - 1) return new Ctor(e > 0 ? s / 0 : 0);\r\n\r\n  external = false;\r\n  Ctor.rounding = x.s = 1;\r\n\r\n  // Estimate the extra guard digits needed to ensure five correct rounding digits from\r\n  // naturalLogarithm(x). Example of failure without these extra digits (precision: 10):\r\n  // new Decimal(2.32456).pow('2087987436534566.46411')\r\n  // should be 1.162377823e+764914905173815, but is 1.162355823e+764914905173815\r\n  k = Math.min(12, (e + '').length);\r\n\r\n  // r = x^y = exp(y*ln(x))\r\n  r = naturalExponential(y.times(naturalLogarithm(x, pr + k)), pr);\r\n\r\n  // r may be Infinity, e.g. (0.9999999999999999).pow(-1e+40)\r\n  if (r.d) {\r\n\r\n    // Truncate to the required precision plus five rounding digits.\r\n    r = finalise(r, pr + 5, 1);\r\n\r\n    // If the rounding digits are [49]9999 or [50]0000 increase the precision by 10 and recalculate\r\n    // the result.\r\n    if (checkRoundingDigits(r.d, pr, rm)) {\r\n      e = pr + 10;\r\n\r\n      // Truncate to the increased precision plus five rounding digits.\r\n      r = finalise(naturalExponential(y.times(naturalLogarithm(x, e + k)), e), e + 5, 1);\r\n\r\n      // Check for 14 nines from the 2nd rounding digit (the first rounding digit may be 4 or 9).\r\n      if (+digitsToString(r.d).slice(pr + 1, pr + 15) + 1 == 1e14) {\r\n        r = finalise(r, pr + 1, 0);\r\n      }\r\n    }\r\n  }\r\n\r\n  r.s = s;\r\n  external = true;\r\n  Ctor.rounding = rm;\r\n\r\n  return finalise(r, pr, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal rounded to `sd` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * Return exponential notation if `sd` is less than the number of digits necessary to represent\r\n * the integer part of the value in normal notation.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n */\r\nP.toPrecision = function (sd, rm) {\r\n  var str,\r\n    x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n\r\n    x = finalise(new Ctor(x), sd, rm);\r\n    str = finiteToString(x, sd <= x.e || x.e <= Ctor.toExpNeg, sd);\r\n  }\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal rounded to a maximum of `sd`\r\n * significant digits using rounding mode `rm`, or to `precision` and `rounding` respectively if\r\n * omitted.\r\n *\r\n * [sd] {number} Significant digits. Integer, 1 to MAX_DIGITS inclusive.\r\n * [rm] {number} Rounding mode. Integer, 0 to 8 inclusive.\r\n *\r\n * 'toSD() digits out of range: {sd}'\r\n * 'toSD() digits not an integer: {sd}'\r\n * 'toSD() rounding mode not an integer: {rm}'\r\n * 'toSD() rounding mode out of range: {rm}'\r\n *\r\n */\r\nP.toSignificantDigits = P.toSD = function (sd, rm) {\r\n  var x = this,\r\n    Ctor = x.constructor;\r\n\r\n  if (sd === void 0) {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  } else {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  }\r\n\r\n  return finalise(new Ctor(x), sd, rm);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n *\r\n * Return exponential notation if this Decimal has a positive exponent equal to or greater than\r\n * `toExpPos`, or a negative exponent equal to or less than `toExpNeg`.\r\n *\r\n */\r\nP.toString = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() && !x.isZero() ? '-' + str : str;\r\n};\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of this Decimal truncated to a whole number.\r\n *\r\n */\r\nP.truncated = P.trunc = function () {\r\n  return finalise(new this.constructor(this), this.e + 1, 1);\r\n};\r\n\r\n\r\n/*\r\n * Return a string representing the value of this Decimal.\r\n * Unlike `toString`, negative zero will include the minus sign.\r\n *\r\n */\r\nP.valueOf = P.toJSON = function () {\r\n  var x = this,\r\n    Ctor = x.constructor,\r\n    str = finiteToString(x, x.e <= Ctor.toExpNeg || x.e >= Ctor.toExpPos);\r\n\r\n  return x.isNeg() ? '-' + str : str;\r\n};\r\n\r\n\r\n// Helper functions for Decimal.prototype (P) and/or Decimal methods, and their callers.\r\n\r\n\r\n/*\r\n *  digitsToString           P.cubeRoot, P.logarithm, P.squareRoot, P.toFraction, P.toPower,\r\n *                           finiteToString, naturalExponential, naturalLogarithm\r\n *  checkInt32               P.toDecimalPlaces, P.toExponential, P.toFixed, P.toNearest,\r\n *                           P.toPrecision, P.toSignificantDigits, toStringBinary, random\r\n *  checkRoundingDigits      P.logarithm, P.toPower, naturalExponential, naturalLogarithm\r\n *  convertBase              toStringBinary, parseOther\r\n *  cos                      P.cos\r\n *  divide                   P.atanh, P.cubeRoot, P.dividedBy, P.dividedToIntegerBy,\r\n *                           P.logarithm, P.modulo, P.squareRoot, P.tan, P.tanh, P.toFraction,\r\n *                           P.toNearest, toStringBinary, naturalExponential, naturalLogarithm,\r\n *                           taylorSeries, atan2, parseOther\r\n *  finalise                 P.absoluteValue, P.atan, P.atanh, P.ceil, P.cos, P.cosh,\r\n *                           P.cubeRoot, P.dividedToIntegerBy, P.floor, P.logarithm, P.minus,\r\n *                           P.modulo, P.negated, P.plus, P.round, P.sin, P.sinh, P.squareRoot,\r\n *                           P.tan, P.times, P.toDecimalPlaces, P.toExponential, P.toFixed,\r\n *                           P.toNearest, P.toPower, P.toPrecision, P.toSignificantDigits,\r\n *                           P.truncated, divide, getLn10, getPi, naturalExponential,\r\n *                           naturalLogarithm, ceil, floor, round, trunc\r\n *  finiteToString           P.toExponential, P.toFixed, P.toPrecision, P.toString, P.valueOf,\r\n *                           toStringBinary\r\n *  getBase10Exponent        P.minus, P.plus, P.times, parseOther\r\n *  getLn10                  P.logarithm, naturalLogarithm\r\n *  getPi                    P.acos, P.asin, P.atan, toLessThanHalfPi, atan2\r\n *  getPrecision             P.precision, P.toFraction\r\n *  getZeroString            digitsToString, finiteToString\r\n *  intPow                   P.toPower, parseOther\r\n *  isOdd                    toLessThanHalfPi\r\n *  maxOrMin                 max, min\r\n *  naturalExponential       P.naturalExponential, P.toPower\r\n *  naturalLogarithm         P.acosh, P.asinh, P.atanh, P.logarithm, P.naturalLogarithm,\r\n *                           P.toPower, naturalExponential\r\n *  nonFiniteToString        finiteToString, toStringBinary\r\n *  parseDecimal             Decimal\r\n *  parseOther               Decimal\r\n *  sin                      P.sin\r\n *  taylorSeries             P.cosh, P.sinh, cos, sin\r\n *  toLessThanHalfPi         P.cos, P.sin\r\n *  toStringBinary           P.toBinary, P.toHexadecimal, P.toOctal\r\n *  truncate                 intPow\r\n *\r\n *  Throws:                  P.logarithm, P.precision, P.toFraction, checkInt32, getLn10, getPi,\r\n *                           naturalLogarithm, config, parseOther, random, Decimal\r\n */\r\n\r\n\r\nfunction digitsToString(d) {\r\n  var i, k, ws,\r\n    indexOfLastWord = d.length - 1,\r\n    str = '',\r\n    w = d[0];\r\n\r\n  if (indexOfLastWord > 0) {\r\n    str += w;\r\n    for (i = 1; i < indexOfLastWord; i++) {\r\n      ws = d[i] + '';\r\n      k = LOG_BASE - ws.length;\r\n      if (k) str += getZeroString(k);\r\n      str += ws;\r\n    }\r\n\r\n    w = d[i];\r\n    ws = w + '';\r\n    k = LOG_BASE - ws.length;\r\n    if (k) str += getZeroString(k);\r\n  } else if (w === 0) {\r\n    return '0';\r\n  }\r\n\r\n  // Remove trailing zeros of last w.\r\n  for (; w % 10 === 0;) w /= 10;\r\n\r\n  return str + w;\r\n}\r\n\r\n\r\nfunction checkInt32(i, min, max) {\r\n  if (i !== ~~i || i < min || i > max) {\r\n    throw Error(invalidArgument + i);\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Check 5 rounding digits if `repeating` is null, 4 otherwise.\r\n * `repeating == null` if caller is `log` or `pow`,\r\n * `repeating != null` if caller is `naturalLogarithm` or `naturalExponential`.\r\n */\r\nfunction checkRoundingDigits(d, i, rm, repeating) {\r\n  var di, k, r, rd;\r\n\r\n  // Get the length of the first word of the array d.\r\n  for (k = d[0]; k >= 10; k /= 10) --i;\r\n\r\n  // Is the rounding digit in the first word of d?\r\n  if (--i < 0) {\r\n    i += LOG_BASE;\r\n    di = 0;\r\n  } else {\r\n    di = Math.ceil((i + 1) / LOG_BASE);\r\n    i %= LOG_BASE;\r\n  }\r\n\r\n  // i is the index (0 - 6) of the rounding digit.\r\n  // E.g. if within the word 3487563 the first rounding digit is 5,\r\n  // then i = 4, k = 1000, rd = 3487563 % 1000 = 563\r\n  k = mathpow(10, LOG_BASE - i);\r\n  rd = d[di] % k | 0;\r\n\r\n  if (repeating == null) {\r\n    if (i < 3) {\r\n      if (i == 0) rd = rd / 100 | 0;\r\n      else if (i == 1) rd = rd / 10 | 0;\r\n      r = rm < 4 && rd == 99999 || rm > 3 && rd == 49999 || rd == 50000 || rd == 0;\r\n    } else {\r\n      r = (rm < 4 && rd + 1 == k || rm > 3 && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 100 | 0) == mathpow(10, i - 2) - 1 ||\r\n          (rd == k / 2 || rd == 0) && (d[di + 1] / k / 100 | 0) == 0;\r\n    }\r\n  } else {\r\n    if (i < 4) {\r\n      if (i == 0) rd = rd / 1000 | 0;\r\n      else if (i == 1) rd = rd / 100 | 0;\r\n      else if (i == 2) rd = rd / 10 | 0;\r\n      r = (repeating || rm < 4) && rd == 9999 || !repeating && rm > 3 && rd == 4999;\r\n    } else {\r\n      r = ((repeating || rm < 4) && rd + 1 == k ||\r\n      (!repeating && rm > 3) && rd + 1 == k / 2) &&\r\n        (d[di + 1] / k / 1000 | 0) == mathpow(10, i - 3) - 1;\r\n    }\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n// Convert string of `baseIn` to an array of numbers of `baseOut`.\r\n// Eg. convertBase('255', 10, 16) returns [15, 15].\r\n// Eg. convertBase('ff', 16, 10) returns [2, 5, 5].\r\nfunction convertBase(str, baseIn, baseOut) {\r\n  var j,\r\n    arr = [0],\r\n    arrL,\r\n    i = 0,\r\n    strL = str.length;\r\n\r\n  for (; i < strL;) {\r\n    for (arrL = arr.length; arrL--;) arr[arrL] *= baseIn;\r\n    arr[0] += NUMERALS.indexOf(str.charAt(i++));\r\n    for (j = 0; j < arr.length; j++) {\r\n      if (arr[j] > baseOut - 1) {\r\n        if (arr[j + 1] === void 0) arr[j + 1] = 0;\r\n        arr[j + 1] += arr[j] / baseOut | 0;\r\n        arr[j] %= baseOut;\r\n      }\r\n    }\r\n  }\r\n\r\n  return arr.reverse();\r\n}\r\n\r\n\r\n/*\r\n * cos(x) = 1 - x^2/2! + x^4/4! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction cosine(Ctor, x) {\r\n  var k, len, y;\r\n\r\n  if (x.isZero()) return x;\r\n\r\n  // Argument reduction: cos(4x) = 8*(cos^4(x) - cos^2(x)) + 1\r\n  // i.e. cos(x) = 8*(cos^4(x/4) - cos^2(x/4)) + 1\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  len = x.d.length;\r\n  if (len < 32) {\r\n    k = Math.ceil(len / 3);\r\n    y = (1 / tinyPow(4, k)).toString();\r\n  } else {\r\n    k = 16;\r\n    y = '2.3283064365386962890625e-10';\r\n  }\r\n\r\n  Ctor.precision += k;\r\n\r\n  x = taylorSeries(Ctor, 1, x.times(y), new Ctor(1));\r\n\r\n  // Reverse argument reduction\r\n  for (var i = k; i--;) {\r\n    var cos2x = x.times(x);\r\n    x = cos2x.times(cos2x).minus(cos2x).times(8).plus(1);\r\n  }\r\n\r\n  Ctor.precision -= k;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Perform division in the specified base.\r\n */\r\nvar divide = (function () {\r\n\r\n  // Assumes non-zero x and k, and hence non-zero result.\r\n  function multiplyInteger(x, k, base) {\r\n    var temp,\r\n      carry = 0,\r\n      i = x.length;\r\n\r\n    for (x = x.slice(); i--;) {\r\n      temp = x[i] * k + carry;\r\n      x[i] = temp % base | 0;\r\n      carry = temp / base | 0;\r\n    }\r\n\r\n    if (carry) x.unshift(carry);\r\n\r\n    return x;\r\n  }\r\n\r\n  function compare(a, b, aL, bL) {\r\n    var i, r;\r\n\r\n    if (aL != bL) {\r\n      r = aL > bL ? 1 : -1;\r\n    } else {\r\n      for (i = r = 0; i < aL; i++) {\r\n        if (a[i] != b[i]) {\r\n          r = a[i] > b[i] ? 1 : -1;\r\n          break;\r\n        }\r\n      }\r\n    }\r\n\r\n    return r;\r\n  }\r\n\r\n  function subtract(a, b, aL, base) {\r\n    var i = 0;\r\n\r\n    // Subtract b from a.\r\n    for (; aL--;) {\r\n      a[aL] -= i;\r\n      i = a[aL] < b[aL] ? 1 : 0;\r\n      a[aL] = i * base + a[aL] - b[aL];\r\n    }\r\n\r\n    // Remove leading zeros.\r\n    for (; !a[0] && a.length > 1;) a.shift();\r\n  }\r\n\r\n  return function (x, y, pr, rm, dp, base) {\r\n    var cmp, e, i, k, logBase, more, prod, prodL, q, qd, rem, remL, rem0, sd, t, xi, xL, yd0,\r\n      yL, yz,\r\n      Ctor = x.constructor,\r\n      sign = x.s == y.s ? 1 : -1,\r\n      xd = x.d,\r\n      yd = y.d;\r\n\r\n    // Either NaN, Infinity or 0?\r\n    if (!xd || !xd[0] || !yd || !yd[0]) {\r\n\r\n      return new Ctor(// Return NaN if either NaN, or both Infinity or 0.\r\n        !x.s || !y.s || (xd ? yd && xd[0] == yd[0] : !yd) ? NaN :\r\n\r\n        // Return ±0 if x is 0 or y is ±Infinity, or return ±Infinity as y is 0.\r\n        xd && xd[0] == 0 || !yd ? sign * 0 : sign / 0);\r\n    }\r\n\r\n    if (base) {\r\n      logBase = 1;\r\n      e = x.e - y.e;\r\n    } else {\r\n      base = BASE;\r\n      logBase = LOG_BASE;\r\n      e = mathfloor(x.e / logBase) - mathfloor(y.e / logBase);\r\n    }\r\n\r\n    yL = yd.length;\r\n    xL = xd.length;\r\n    q = new Ctor(sign);\r\n    qd = q.d = [];\r\n\r\n    // Result exponent may be one less than e.\r\n    // The digit array of a Decimal from toStringBinary may have trailing zeros.\r\n    for (i = 0; yd[i] == (xd[i] || 0); i++);\r\n\r\n    if (yd[i] > (xd[i] || 0)) e--;\r\n\r\n    if (pr == null) {\r\n      sd = pr = Ctor.precision;\r\n      rm = Ctor.rounding;\r\n    } else if (dp) {\r\n      sd = pr + (x.e - y.e) + 1;\r\n    } else {\r\n      sd = pr;\r\n    }\r\n\r\n    if (sd < 0) {\r\n      qd.push(1);\r\n      more = true;\r\n    } else {\r\n\r\n      // Convert precision in number of base 10 digits to base 1e7 digits.\r\n      sd = sd / logBase + 2 | 0;\r\n      i = 0;\r\n\r\n      // divisor < 1e7\r\n      if (yL == 1) {\r\n        k = 0;\r\n        yd = yd[0];\r\n        sd++;\r\n\r\n        // k is the carry.\r\n        for (; (i < xL || k) && sd--; i++) {\r\n          t = k * base + (xd[i] || 0);\r\n          qd[i] = t / yd | 0;\r\n          k = t % yd | 0;\r\n        }\r\n\r\n        more = k || i < xL;\r\n\r\n      // divisor >= 1e7\r\n      } else {\r\n\r\n        // Normalise xd and yd so highest order digit of yd is >= base/2\r\n        k = base / (yd[0] + 1) | 0;\r\n\r\n        if (k > 1) {\r\n          yd = multiplyInteger(yd, k, base);\r\n          xd = multiplyInteger(xd, k, base);\r\n          yL = yd.length;\r\n          xL = xd.length;\r\n        }\r\n\r\n        xi = yL;\r\n        rem = xd.slice(0, yL);\r\n        remL = rem.length;\r\n\r\n        // Add zeros to make remainder as long as divisor.\r\n        for (; remL < yL;) rem[remL++] = 0;\r\n\r\n        yz = yd.slice();\r\n        yz.unshift(0);\r\n        yd0 = yd[0];\r\n\r\n        if (yd[1] >= base / 2) ++yd0;\r\n\r\n        do {\r\n          k = 0;\r\n\r\n          // Compare divisor and remainder.\r\n          cmp = compare(yd, rem, yL, remL);\r\n\r\n          // If divisor < remainder.\r\n          if (cmp < 0) {\r\n\r\n            // Calculate trial digit, k.\r\n            rem0 = rem[0];\r\n            if (yL != remL) rem0 = rem0 * base + (rem[1] || 0);\r\n\r\n            // k will be how many times the divisor goes into the current remainder.\r\n            k = rem0 / yd0 | 0;\r\n\r\n            //  Algorithm:\r\n            //  1. product = divisor * trial digit (k)\r\n            //  2. if product > remainder: product -= divisor, k--\r\n            //  3. remainder -= product\r\n            //  4. if product was < remainder at 2:\r\n            //    5. compare new remainder and divisor\r\n            //    6. If remainder > divisor: remainder -= divisor, k++\r\n\r\n            if (k > 1) {\r\n              if (k >= base) k = base - 1;\r\n\r\n              // product = divisor * trial digit.\r\n              prod = multiplyInteger(yd, k, base);\r\n              prodL = prod.length;\r\n              remL = rem.length;\r\n\r\n              // Compare product and remainder.\r\n              cmp = compare(prod, rem, prodL, remL);\r\n\r\n              // product > remainder.\r\n              if (cmp == 1) {\r\n                k--;\r\n\r\n                // Subtract divisor from product.\r\n                subtract(prod, yL < prodL ? yz : yd, prodL, base);\r\n              }\r\n            } else {\r\n\r\n              // cmp is -1.\r\n              // If k is 0, there is no need to compare yd and rem again below, so change cmp to 1\r\n              // to avoid it. If k is 1 there is a need to compare yd and rem again below.\r\n              if (k == 0) cmp = k = 1;\r\n              prod = yd.slice();\r\n            }\r\n\r\n            prodL = prod.length;\r\n            if (prodL < remL) prod.unshift(0);\r\n\r\n            // Subtract product from remainder.\r\n            subtract(rem, prod, remL, base);\r\n\r\n            // If product was < previous remainder.\r\n            if (cmp == -1) {\r\n              remL = rem.length;\r\n\r\n              // Compare divisor and new remainder.\r\n              cmp = compare(yd, rem, yL, remL);\r\n\r\n              // If divisor < new remainder, subtract divisor from remainder.\r\n              if (cmp < 1) {\r\n                k++;\r\n\r\n                // Subtract divisor from remainder.\r\n                subtract(rem, yL < remL ? yz : yd, remL, base);\r\n              }\r\n            }\r\n\r\n            remL = rem.length;\r\n          } else if (cmp === 0) {\r\n            k++;\r\n            rem = [0];\r\n          }    // if cmp === 1, k will be 0\r\n\r\n          // Add the next digit, k, to the result array.\r\n          qd[i++] = k;\r\n\r\n          // Update the remainder.\r\n          if (cmp && rem[0]) {\r\n            rem[remL++] = xd[xi] || 0;\r\n          } else {\r\n            rem = [xd[xi]];\r\n            remL = 1;\r\n          }\r\n\r\n        } while ((xi++ < xL || rem[0] !== void 0) && sd--);\r\n\r\n        more = rem[0] !== void 0;\r\n      }\r\n\r\n      // Leading zero?\r\n      if (!qd[0]) qd.shift();\r\n    }\r\n\r\n    // logBase is 1 when divide is being used for base conversion.\r\n    if (logBase == 1) {\r\n      q.e = e;\r\n      inexact = more;\r\n    } else {\r\n\r\n      // To calculate q.e, first get the number of digits of qd[0].\r\n      for (i = 1, k = qd[0]; k >= 10; k /= 10) i++;\r\n      q.e = i + e * logBase - 1;\r\n\r\n      finalise(q, dp ? pr + q.e + 1 : pr, rm, more);\r\n    }\r\n\r\n    return q;\r\n  };\r\n})();\r\n\r\n\r\n/*\r\n * Round `x` to `sd` significant digits using rounding mode `rm`.\r\n * Check for over/under-flow.\r\n */\r\n function finalise(x, sd, rm, isTruncated) {\r\n  var digits, i, j, k, rd, roundUp, w, xd, xdi,\r\n    Ctor = x.constructor;\r\n\r\n  // Don't round if sd is null or undefined.\r\n  out: if (sd != null) {\r\n    xd = x.d;\r\n\r\n    // Infinity/NaN.\r\n    if (!xd) return x;\r\n\r\n    // rd: the rounding digit, i.e. the digit after the digit that may be rounded up.\r\n    // w: the word of xd containing rd, a base 1e7 number.\r\n    // xdi: the index of w within xd.\r\n    // digits: the number of digits of w.\r\n    // i: what would be the index of rd within w if all the numbers were 7 digits long (i.e. if\r\n    // they had leading zeros)\r\n    // j: if > 0, the actual index of rd within w (if < 0, rd is a leading zero).\r\n\r\n    // Get the length of the first word of the digits array xd.\r\n    for (digits = 1, k = xd[0]; k >= 10; k /= 10) digits++;\r\n    i = sd - digits;\r\n\r\n    // Is the rounding digit in the first word of xd?\r\n    if (i < 0) {\r\n      i += LOG_BASE;\r\n      j = sd;\r\n      w = xd[xdi = 0];\r\n\r\n      // Get the rounding digit at index j of w.\r\n      rd = w / mathpow(10, digits - j - 1) % 10 | 0;\r\n    } else {\r\n      xdi = Math.ceil((i + 1) / LOG_BASE);\r\n      k = xd.length;\r\n      if (xdi >= k) {\r\n        if (isTruncated) {\r\n\r\n          // Needed by `naturalExponential`, `naturalLogarithm` and `squareRoot`.\r\n          for (; k++ <= xdi;) xd.push(0);\r\n          w = rd = 0;\r\n          digits = 1;\r\n          i %= LOG_BASE;\r\n          j = i - LOG_BASE + 1;\r\n        } else {\r\n          break out;\r\n        }\r\n      } else {\r\n        w = k = xd[xdi];\r\n\r\n        // Get the number of digits of w.\r\n        for (digits = 1; k >= 10; k /= 10) digits++;\r\n\r\n        // Get the index of rd within w.\r\n        i %= LOG_BASE;\r\n\r\n        // Get the index of rd within w, adjusted for leading zeros.\r\n        // The number of leading zeros of w is given by LOG_BASE - digits.\r\n        j = i - LOG_BASE + digits;\r\n\r\n        // Get the rounding digit at index j of w.\r\n        rd = j < 0 ? 0 : w / mathpow(10, digits - j - 1) % 10 | 0;\r\n      }\r\n    }\r\n\r\n    // Are there any non-zero digits after the rounding digit?\r\n    isTruncated = isTruncated || sd < 0 ||\r\n      xd[xdi + 1] !== void 0 || (j < 0 ? w : w % mathpow(10, digits - j - 1));\r\n\r\n    // The expression `w % mathpow(10, digits - j - 1)` returns all the digits of w to the right\r\n    // of the digit at (left-to-right) index j, e.g. if w is 908714 and j is 2, the expression\r\n    // will give 714.\r\n\r\n    roundUp = rm < 4\r\n      ? (rd || isTruncated) && (rm == 0 || rm == (x.s < 0 ? 3 : 2))\r\n      : rd > 5 || rd == 5 && (rm == 4 || isTruncated || rm == 6 &&\r\n\r\n        // Check whether the digit to the left of the rounding digit is odd.\r\n        ((i > 0 ? j > 0 ? w / mathpow(10, digits - j) : 0 : xd[xdi - 1]) % 10) & 1 ||\r\n          rm == (x.s < 0 ? 8 : 7));\r\n\r\n    if (sd < 1 || !xd[0]) {\r\n      xd.length = 0;\r\n      if (roundUp) {\r\n\r\n        // Convert sd to decimal places.\r\n        sd -= x.e + 1;\r\n\r\n        // 1, 0.1, 0.01, 0.001, 0.0001 etc.\r\n        xd[0] = mathpow(10, (LOG_BASE - sd % LOG_BASE) % LOG_BASE);\r\n        x.e = -sd || 0;\r\n      } else {\r\n\r\n        // Zero.\r\n        xd[0] = x.e = 0;\r\n      }\r\n\r\n      return x;\r\n    }\r\n\r\n    // Remove excess digits.\r\n    if (i == 0) {\r\n      xd.length = xdi;\r\n      k = 1;\r\n      xdi--;\r\n    } else {\r\n      xd.length = xdi + 1;\r\n      k = mathpow(10, LOG_BASE - i);\r\n\r\n      // E.g. 56700 becomes 56000 if 7 is the rounding digit.\r\n      // j > 0 means i > number of leading zeros of w.\r\n      xd[xdi] = j > 0 ? (w / mathpow(10, digits - j) % mathpow(10, j) | 0) * k : 0;\r\n    }\r\n\r\n    if (roundUp) {\r\n      for (;;) {\r\n\r\n        // Is the digit to be rounded up in the first word of xd?\r\n        if (xdi == 0) {\r\n\r\n          // i will be the length of xd[0] before k is added.\r\n          for (i = 1, j = xd[0]; j >= 10; j /= 10) i++;\r\n          j = xd[0] += k;\r\n          for (k = 1; j >= 10; j /= 10) k++;\r\n\r\n          // if i != k the length has increased.\r\n          if (i != k) {\r\n            x.e++;\r\n            if (xd[0] == BASE) xd[0] = 1;\r\n          }\r\n\r\n          break;\r\n        } else {\r\n          xd[xdi] += k;\r\n          if (xd[xdi] != BASE) break;\r\n          xd[xdi--] = 0;\r\n          k = 1;\r\n        }\r\n      }\r\n    }\r\n\r\n    // Remove trailing zeros.\r\n    for (i = xd.length; xd[--i] === 0;) xd.pop();\r\n  }\r\n\r\n  if (external) {\r\n\r\n    // Overflow?\r\n    if (x.e > Ctor.maxE) {\r\n\r\n      // Infinity.\r\n      x.d = null;\r\n      x.e = NaN;\r\n\r\n    // Underflow?\r\n    } else if (x.e < Ctor.minE) {\r\n\r\n      // Zero.\r\n      x.e = 0;\r\n      x.d = [0];\r\n      // Ctor.underflow = true;\r\n    } // else Ctor.underflow = false;\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\nfunction finiteToString(x, isExp, sd) {\r\n  if (!x.isFinite()) return nonFiniteToString(x);\r\n  var k,\r\n    e = x.e,\r\n    str = digitsToString(x.d),\r\n    len = str.length;\r\n\r\n  if (isExp) {\r\n    if (sd && (k = sd - len) > 0) {\r\n      str = str.charAt(0) + '.' + str.slice(1) + getZeroString(k);\r\n    } else if (len > 1) {\r\n      str = str.charAt(0) + '.' + str.slice(1);\r\n    }\r\n\r\n    str = str + (x.e < 0 ? 'e' : 'e+') + x.e;\r\n  } else if (e < 0) {\r\n    str = '0.' + getZeroString(-e - 1) + str;\r\n    if (sd && (k = sd - len) > 0) str += getZeroString(k);\r\n  } else if (e >= len) {\r\n    str += getZeroString(e + 1 - len);\r\n    if (sd && (k = sd - e - 1) > 0) str = str + '.' + getZeroString(k);\r\n  } else {\r\n    if ((k = e + 1) < len) str = str.slice(0, k) + '.' + str.slice(k);\r\n    if (sd && (k = sd - len) > 0) {\r\n      if (e + 1 === len) str += '.';\r\n      str += getZeroString(k);\r\n    }\r\n  }\r\n\r\n  return str;\r\n}\r\n\r\n\r\n// Calculate the base 10 exponent from the base 1e7 exponent.\r\nfunction getBase10Exponent(digits, e) {\r\n  var w = digits[0];\r\n\r\n  // Add the number of digits of the first word of the digits array.\r\n  for ( e *= LOG_BASE; w >= 10; w /= 10) e++;\r\n  return e;\r\n}\r\n\r\n\r\nfunction getLn10(Ctor, sd, pr) {\r\n  if (sd > LN10_PRECISION) {\r\n\r\n    // Reset global state in case the exception is caught.\r\n    external = true;\r\n    if (pr) Ctor.precision = pr;\r\n    throw Error(precisionLimitExceeded);\r\n  }\r\n  return finalise(new Ctor(LN10), sd, 1, true);\r\n}\r\n\r\n\r\nfunction getPi(Ctor, sd, rm) {\r\n  if (sd > PI_PRECISION) throw Error(precisionLimitExceeded);\r\n  return finalise(new Ctor(PI), sd, rm, true);\r\n}\r\n\r\n\r\nfunction getPrecision(digits) {\r\n  var w = digits.length - 1,\r\n    len = w * LOG_BASE + 1;\r\n\r\n  w = digits[w];\r\n\r\n  // If non-zero...\r\n  if (w) {\r\n\r\n    // Subtract the number of trailing zeros of the last word.\r\n    for (; w % 10 == 0; w /= 10) len--;\r\n\r\n    // Add the number of digits of the first word.\r\n    for (w = digits[0]; w >= 10; w /= 10) len++;\r\n  }\r\n\r\n  return len;\r\n}\r\n\r\n\r\nfunction getZeroString(k) {\r\n  var zs = '';\r\n  for (; k--;) zs += '0';\r\n  return zs;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the value of Decimal `x` to the power `n`, where `n` is an\r\n * integer of type number.\r\n *\r\n * Implements 'exponentiation by squaring'. Called by `pow` and `parseOther`.\r\n *\r\n */\r\nfunction intPow(Ctor, x, n, pr) {\r\n  var isTruncated,\r\n    r = new Ctor(1),\r\n\r\n    // Max n of 9007199254740991 takes 53 loop iterations.\r\n    // Maximum digits array length; leaves [28, 34] guard digits.\r\n    k = Math.ceil(pr / LOG_BASE + 4);\r\n\r\n  external = false;\r\n\r\n  for (;;) {\r\n    if (n % 2) {\r\n      r = r.times(x);\r\n      if (truncate(r.d, k)) isTruncated = true;\r\n    }\r\n\r\n    n = mathfloor(n / 2);\r\n    if (n === 0) {\r\n\r\n      // To ensure correct rounding when r.d is truncated, increment the last word if it is zero.\r\n      n = r.d.length - 1;\r\n      if (isTruncated && r.d[n] === 0) ++r.d[n];\r\n      break;\r\n    }\r\n\r\n    x = x.times(x);\r\n    truncate(x.d, k);\r\n  }\r\n\r\n  external = true;\r\n\r\n  return r;\r\n}\r\n\r\n\r\nfunction isOdd(n) {\r\n  return n.d[n.d.length - 1] & 1;\r\n}\r\n\r\n\r\n/*\r\n * Handle `max` (`n` is -1) and `min` (`n` is 1).\r\n */\r\nfunction maxOrMin(Ctor, args, n) {\r\n  var k, y,\r\n    x = new Ctor(args[0]),\r\n    i = 0;\r\n\r\n  for (; ++i < args.length;) {\r\n    y = new Ctor(args[i]);\r\n\r\n    // NaN?\r\n    if (!y.s) {\r\n      x = y;\r\n      break;\r\n    }\r\n\r\n    k = x.cmp(y);\r\n\r\n    if (k === n || k === 0 && x.s === n) {\r\n      x = y;\r\n    }\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n * Taylor/Maclaurin series.\r\n *\r\n * exp(x) = x^0/0! + x^1/1! + x^2/2! + x^3/3! + ...\r\n *\r\n * Argument reduction:\r\n *   Repeat x = x / 32, k += 5, until |x| < 0.1\r\n *   exp(x) = exp(x / 2^k)^(2^k)\r\n *\r\n * Previously, the argument was initially reduced by\r\n * exp(x) = exp(r) * 10^k  where r = x - k * ln10, k = floor(x / ln10)\r\n * to first put r in the range [0, ln10], before dividing by 32 until |x| < 0.1, but this was\r\n * found to be slower than just dividing repeatedly by 32 as above.\r\n *\r\n * Max integer argument: exp('20723265836946413') = 6.3e+9000000000000000\r\n * Min integer argument: exp('-20723265836946411') = 1.2e-9000000000000000\r\n * (Math object integer min/max: Math.exp(709) = 8.2e+307, Math.exp(-745) = 5e-324)\r\n *\r\n *  exp(Infinity)  = Infinity\r\n *  exp(-Infinity) = 0\r\n *  exp(NaN)       = NaN\r\n *  exp(±0)        = 1\r\n *\r\n *  exp(x) is non-terminating for any finite, non-zero x.\r\n *\r\n *  The result will always be correctly rounded.\r\n *\r\n */\r\nfunction naturalExponential(x, sd) {\r\n  var denominator, guard, j, pow, sum, t, wpr,\r\n    rep = 0,\r\n    i = 0,\r\n    k = 0,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // 0/NaN/Infinity?\r\n  if (!x.d || !x.d[0] || x.e > 17) {\r\n\r\n    return new Ctor(x.d\r\n      ? !x.d[0] ? 1 : x.s < 0 ? 0 : 1 / 0\r\n      : x.s ? x.s < 0 ? 0 : x : 0 / 0);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  t = new Ctor(0.03125);\r\n\r\n  // while abs(x) >= 0.1\r\n  while (x.e > -2) {\r\n\r\n    // x = x / 2^5\r\n    x = x.times(t);\r\n    k += 5;\r\n  }\r\n\r\n  // Use 2 * log10(2^k) + 5 (empirically derived) to estimate the increase in precision\r\n  // necessary to ensure the first 4 rounding digits are correct.\r\n  guard = Math.log(mathpow(2, k)) / Math.LN10 * 2 + 5 | 0;\r\n  wpr += guard;\r\n  denominator = pow = sum = new Ctor(1);\r\n  Ctor.precision = wpr;\r\n\r\n  for (;;) {\r\n    pow = finalise(pow.times(x), wpr, 1);\r\n    denominator = denominator.times(++i);\r\n    t = sum.plus(divide(pow, denominator, wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      j = k;\r\n      while (j--) sum = finalise(sum.times(sum), wpr, 1);\r\n\r\n      // Check to see if the first 4 rounding digits are [49]999.\r\n      // If so, repeat the summation with a higher precision, otherwise\r\n      // e.g. with precision: 18, rounding: 1\r\n      // exp(18.404272462595034083567793919843761) = 98372560.1229999999 (should be 98372560.123)\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n\r\n        if (rep < 3 && checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += 10;\r\n          denominator = pow = t = new Ctor(1);\r\n          i = 0;\r\n          rep++;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n  }\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x` rounded to `sd` significant\r\n * digits.\r\n *\r\n *  ln(-n)        = NaN\r\n *  ln(0)         = -Infinity\r\n *  ln(-0)        = -Infinity\r\n *  ln(1)         = 0\r\n *  ln(Infinity)  = Infinity\r\n *  ln(-Infinity) = NaN\r\n *  ln(NaN)       = NaN\r\n *\r\n *  ln(n) (n != 1) is non-terminating.\r\n *\r\n */\r\nfunction naturalLogarithm(y, sd) {\r\n  var c, c0, denominator, e, numerator, rep, sum, t, wpr, x1, x2,\r\n    n = 1,\r\n    guard = 10,\r\n    x = y,\r\n    xd = x.d,\r\n    Ctor = x.constructor,\r\n    rm = Ctor.rounding,\r\n    pr = Ctor.precision;\r\n\r\n  // Is x negative or Infinity, NaN, 0 or 1?\r\n  if (x.s < 0 || !xd || !xd[0] || !x.e && xd[0] == 1 && xd.length == 1) {\r\n    return new Ctor(xd && !xd[0] ? -1 / 0 : x.s != 1 ? NaN : xd ? 0 : x);\r\n  }\r\n\r\n  if (sd == null) {\r\n    external = false;\r\n    wpr = pr;\r\n  } else {\r\n    wpr = sd;\r\n  }\r\n\r\n  Ctor.precision = wpr += guard;\r\n  c = digitsToString(xd);\r\n  c0 = c.charAt(0);\r\n\r\n  if (Math.abs(e = x.e) < 1.5e15) {\r\n\r\n    // Argument reduction.\r\n    // The series converges faster the closer the argument is to 1, so using\r\n    // ln(a^b) = b * ln(a),   ln(a) = ln(a^b) / b\r\n    // multiply the argument by itself until the leading digits of the significand are 7, 8, 9,\r\n    // 10, 11, 12 or 13, recording the number of multiplications so the sum of the series can\r\n    // later be divided by this number, then separate out the power of 10 using\r\n    // ln(a*10^b) = ln(a) + b*ln(10).\r\n\r\n    // max n is 21 (gives 0.9, 1.0 or 1.1) (9e15 / 21 = 4.2e14).\r\n    //while (c0 < 9 && c0 != 1 || c0 == 1 && c.charAt(1) > 1) {\r\n    // max n is 6 (gives 0.7 - 1.3)\r\n    while (c0 < 7 && c0 != 1 || c0 == 1 && c.charAt(1) > 3) {\r\n      x = x.times(y);\r\n      c = digitsToString(x.d);\r\n      c0 = c.charAt(0);\r\n      n++;\r\n    }\r\n\r\n    e = x.e;\r\n\r\n    if (c0 > 1) {\r\n      x = new Ctor('0.' + c);\r\n      e++;\r\n    } else {\r\n      x = new Ctor(c0 + '.' + c.slice(1));\r\n    }\r\n  } else {\r\n\r\n    // The argument reduction method above may result in overflow if the argument y is a massive\r\n    // number with exponent >= 1500000000000000 (9e15 / 6 = 1.5e15), so instead recall this\r\n    // function using ln(x*10^e) = ln(x) + e*ln(10).\r\n    t = getLn10(Ctor, wpr + 2, pr).times(e + '');\r\n    x = naturalLogarithm(new Ctor(c0 + '.' + c.slice(1)), wpr - guard).plus(t);\r\n    Ctor.precision = pr;\r\n\r\n    return sd == null ? finalise(x, pr, rm, external = true) : x;\r\n  }\r\n\r\n  // x1 is x reduced to a value near 1.\r\n  x1 = x;\r\n\r\n  // Taylor series.\r\n  // ln(y) = ln((1 + x)/(1 - x)) = 2(x + x^3/3 + x^5/5 + x^7/7 + ...)\r\n  // where x = (y - 1)/(y + 1)    (|x| < 1)\r\n  sum = numerator = x = divide(x.minus(1), x.plus(1), wpr, 1);\r\n  x2 = finalise(x.times(x), wpr, 1);\r\n  denominator = 3;\r\n\r\n  for (;;) {\r\n    numerator = finalise(numerator.times(x2), wpr, 1);\r\n    t = sum.plus(divide(numerator, new Ctor(denominator), wpr, 1));\r\n\r\n    if (digitsToString(t.d).slice(0, wpr) === digitsToString(sum.d).slice(0, wpr)) {\r\n      sum = sum.times(2);\r\n\r\n      // Reverse the argument reduction. Check that e is not 0 because, besides preventing an\r\n      // unnecessary calculation, -0 + 0 = +0 and to ensure correct rounding -0 needs to stay -0.\r\n      if (e !== 0) sum = sum.plus(getLn10(Ctor, wpr + 2, pr).times(e + ''));\r\n      sum = divide(sum, new Ctor(n), wpr, 1);\r\n\r\n      // Is rm > 3 and the first 4 rounding digits 4999, or rm < 4 (or the summation has\r\n      // been repeated previously) and the first 4 rounding digits 9999?\r\n      // If so, restart the summation with a higher precision, otherwise\r\n      // e.g. with precision: 12, rounding: 1\r\n      // ln(135520028.6126091714265381533) = 18.7246299999 when it should be 18.72463.\r\n      // `wpr - guard` is the index of first rounding digit.\r\n      if (sd == null) {\r\n        if (checkRoundingDigits(sum.d, wpr - guard, rm, rep)) {\r\n          Ctor.precision = wpr += guard;\r\n          t = numerator = x = divide(x1.minus(1), x1.plus(1), wpr, 1);\r\n          x2 = finalise(x.times(x), wpr, 1);\r\n          denominator = rep = 1;\r\n        } else {\r\n          return finalise(sum, Ctor.precision = pr, rm, external = true);\r\n        }\r\n      } else {\r\n        Ctor.precision = pr;\r\n        return sum;\r\n      }\r\n    }\r\n\r\n    sum = t;\r\n    denominator += 2;\r\n  }\r\n}\r\n\r\n\r\n// ±Infinity, NaN.\r\nfunction nonFiniteToString(x) {\r\n  // Unsigned.\r\n  return String(x.s * x.s / 0);\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from string `str`.\r\n */\r\nfunction parseDecimal(x, str) {\r\n  var e, i, len;\r\n\r\n  // TODO BigInt str: no need to check for decimal point, exponential form or leading zeros.\r\n  // Decimal point?\r\n  if ((e = str.indexOf('.')) > -1) str = str.replace('.', '');\r\n\r\n  // Exponential form?\r\n  if ((i = str.search(/e/i)) > 0) {\r\n\r\n    // Determine exponent.\r\n    if (e < 0) e = i;\r\n    e += +str.slice(i + 1);\r\n    str = str.substring(0, i);\r\n  } else if (e < 0) {\r\n\r\n    // Integer.\r\n    e = str.length;\r\n  }\r\n\r\n  // Determine leading zeros.\r\n  for (i = 0; str.charCodeAt(i) === 48; i++);\r\n\r\n  // Determine trailing zeros.\r\n  for (len = str.length; str.charCodeAt(len - 1) === 48; --len);\r\n  str = str.slice(i, len);\r\n\r\n  if (str) {\r\n    len -= i;\r\n    x.e = e = e - i - 1;\r\n    x.d = [];\r\n\r\n    // Transform base\r\n\r\n    // e is the base 10 exponent.\r\n    // i is where to slice str to get the first word of the digits array.\r\n    i = (e + 1) % LOG_BASE;\r\n    if (e < 0) i += LOG_BASE;\r\n\r\n    if (i < len) {\r\n      if (i) x.d.push(+str.slice(0, i));\r\n      for (len -= LOG_BASE; i < len;) x.d.push(+str.slice(i, i += LOG_BASE));\r\n      str = str.slice(i);\r\n      i = LOG_BASE - str.length;\r\n    } else {\r\n      i -= len;\r\n    }\r\n\r\n    for (; i--;) str += '0';\r\n    x.d.push(+str);\r\n\r\n    if (external) {\r\n\r\n      // Overflow?\r\n      if (x.e > x.constructor.maxE) {\r\n\r\n        // Infinity.\r\n        x.d = null;\r\n        x.e = NaN;\r\n\r\n      // Underflow?\r\n      } else if (x.e < x.constructor.minE) {\r\n\r\n        // Zero.\r\n        x.e = 0;\r\n        x.d = [0];\r\n        // x.constructor.underflow = true;\r\n      } // else x.constructor.underflow = false;\r\n    }\r\n  } else {\r\n\r\n    // Zero.\r\n    x.e = 0;\r\n    x.d = [0];\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * Parse the value of a new Decimal `x` from a string `str`, which is not a decimal value.\r\n */\r\nfunction parseOther(x, str) {\r\n  var base, Ctor, divisor, i, isFloat, len, p, xd, xe;\r\n\r\n  if (str.indexOf('_') > -1) {\r\n    str = str.replace(/(\\d)_(?=\\d)/g, '$1');\r\n    if (isDecimal.test(str)) return parseDecimal(x, str);\r\n  } else if (str === 'Infinity' || str === 'NaN') {\r\n    if (!+str) x.s = NaN;\r\n    x.e = NaN;\r\n    x.d = null;\r\n    return x;\r\n  }\r\n\r\n  if (isHex.test(str))  {\r\n    base = 16;\r\n    str = str.toLowerCase();\r\n  } else if (isBinary.test(str))  {\r\n    base = 2;\r\n  } else if (isOctal.test(str))  {\r\n    base = 8;\r\n  } else {\r\n    throw Error(invalidArgument + str);\r\n  }\r\n\r\n  // Is there a binary exponent part?\r\n  i = str.search(/p/i);\r\n\r\n  if (i > 0) {\r\n    p = +str.slice(i + 1);\r\n    str = str.substring(2, i);\r\n  } else {\r\n    str = str.slice(2);\r\n  }\r\n\r\n  // Convert `str` as an integer then divide the result by `base` raised to a power such that the\r\n  // fraction part will be restored.\r\n  i = str.indexOf('.');\r\n  isFloat = i >= 0;\r\n  Ctor = x.constructor;\r\n\r\n  if (isFloat) {\r\n    str = str.replace('.', '');\r\n    len = str.length;\r\n    i = len - i;\r\n\r\n    // log[10](16) = 1.2041... , log[10](88) = 1.9444....\r\n    divisor = intPow(Ctor, new Ctor(base), i, i * 2);\r\n  }\r\n\r\n  xd = convertBase(str, base, BASE);\r\n  xe = xd.length - 1;\r\n\r\n  // Remove trailing zeros.\r\n  for (i = xe; xd[i] === 0; --i) xd.pop();\r\n  if (i < 0) return new Ctor(x.s * 0);\r\n  x.e = getBase10Exponent(xd, xe);\r\n  x.d = xd;\r\n  external = false;\r\n\r\n  // At what precision to perform the division to ensure exact conversion?\r\n  // maxDecimalIntegerPartDigitCount = ceil(log[10](b) * otherBaseIntegerPartDigitCount)\r\n  // log[10](2) = 0.30103, log[10](8) = 0.90309, log[10](16) = 1.20412\r\n  // E.g. ceil(1.2 * 3) = 4, so up to 4 decimal digits are needed to represent 3 hex int digits.\r\n  // maxDecimalFractionPartDigitCount = {Hex:4|Oct:3|Bin:1} * otherBaseFractionPartDigitCount\r\n  // Therefore using 4 * the number of digits of str will always be enough.\r\n  if (isFloat) x = divide(x, divisor, len * 4);\r\n\r\n  // Multiply by the binary exponent part if present.\r\n  if (p) x = x.times(Math.abs(p) < 54 ? mathpow(2, p) : Decimal.pow(2, p));\r\n  external = true;\r\n\r\n  return x;\r\n}\r\n\r\n\r\n/*\r\n * sin(x) = x - x^3/3! + x^5/5! - ...\r\n * |x| < pi/2\r\n *\r\n */\r\nfunction sine(Ctor, x) {\r\n  var k,\r\n    len = x.d.length;\r\n\r\n  if (len < 3) {\r\n    return x.isZero() ? x : taylorSeries(Ctor, 2, x, x);\r\n  }\r\n\r\n  // Argument reduction: sin(5x) = 16*sin^5(x) - 20*sin^3(x) + 5*sin(x)\r\n  // i.e. sin(x) = 16*sin^5(x/5) - 20*sin^3(x/5) + 5*sin(x/5)\r\n  // and  sin(x) = sin(x/5)(5 + sin^2(x/5)(16sin^2(x/5) - 20))\r\n\r\n  // Estimate the optimum number of times to use the argument reduction.\r\n  k = 1.4 * Math.sqrt(len);\r\n  k = k > 16 ? 16 : k | 0;\r\n\r\n  x = x.times(1 / tinyPow(5, k));\r\n  x = taylorSeries(Ctor, 2, x, x);\r\n\r\n  // Reverse argument reduction\r\n  var sin2_x,\r\n    d5 = new Ctor(5),\r\n    d16 = new Ctor(16),\r\n    d20 = new Ctor(20);\r\n  for (; k--;) {\r\n    sin2_x = x.times(x);\r\n    x = x.times(d5.plus(sin2_x.times(d16.times(sin2_x).minus(d20))));\r\n  }\r\n\r\n  return x;\r\n}\r\n\r\n\r\n// Calculate Taylor series for `cos`, `cosh`, `sin` and `sinh`.\r\nfunction taylorSeries(Ctor, n, x, y, isHyperbolic) {\r\n  var j, t, u, x2,\r\n    i = 1,\r\n    pr = Ctor.precision,\r\n    k = Math.ceil(pr / LOG_BASE);\r\n\r\n  external = false;\r\n  x2 = x.times(x);\r\n  u = new Ctor(y);\r\n\r\n  for (;;) {\r\n    t = divide(u.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    u = isHyperbolic ? y.plus(t) : y.minus(t);\r\n    y = divide(t.times(x2), new Ctor(n++ * n++), pr, 1);\r\n    t = u.plus(y);\r\n\r\n    if (t.d[k] !== void 0) {\r\n      for (j = k; t.d[j] === u.d[j] && j--;);\r\n      if (j == -1) break;\r\n    }\r\n\r\n    j = u;\r\n    u = y;\r\n    y = t;\r\n    t = j;\r\n    i++;\r\n  }\r\n\r\n  external = true;\r\n  t.d.length = k + 1;\r\n\r\n  return t;\r\n}\r\n\r\n\r\n// Exponent e must be positive and non-zero.\r\nfunction tinyPow(b, e) {\r\n  var n = b;\r\n  while (--e) n *= b;\r\n  return n;\r\n}\r\n\r\n\r\n// Return the absolute value of `x` reduced to less than or equal to half pi.\r\nfunction toLessThanHalfPi(Ctor, x) {\r\n  var t,\r\n    isNeg = x.s < 0,\r\n    pi = getPi(Ctor, Ctor.precision, 1),\r\n    halfPi = pi.times(0.5);\r\n\r\n  x = x.abs();\r\n\r\n  if (x.lte(halfPi)) {\r\n    quadrant = isNeg ? 4 : 1;\r\n    return x;\r\n  }\r\n\r\n  t = x.divToInt(pi);\r\n\r\n  if (t.isZero()) {\r\n    quadrant = isNeg ? 3 : 2;\r\n  } else {\r\n    x = x.minus(t.times(pi));\r\n\r\n    // 0 <= x < pi\r\n    if (x.lte(halfPi)) {\r\n      quadrant = isOdd(t) ? (isNeg ? 2 : 3) : (isNeg ? 4 : 1);\r\n      return x;\r\n    }\r\n\r\n    quadrant = isOdd(t) ? (isNeg ? 1 : 4) : (isNeg ? 3 : 2);\r\n  }\r\n\r\n  return x.minus(pi).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return the value of Decimal `x` as a string in base `baseOut`.\r\n *\r\n * If the optional `sd` argument is present include a binary exponent suffix.\r\n */\r\nfunction toStringBinary(x, baseOut, sd, rm) {\r\n  var base, e, i, k, len, roundUp, str, xd, y,\r\n    Ctor = x.constructor,\r\n    isExp = sd !== void 0;\r\n\r\n  if (isExp) {\r\n    checkInt32(sd, 1, MAX_DIGITS);\r\n    if (rm === void 0) rm = Ctor.rounding;\r\n    else checkInt32(rm, 0, 8);\r\n  } else {\r\n    sd = Ctor.precision;\r\n    rm = Ctor.rounding;\r\n  }\r\n\r\n  if (!x.isFinite()) {\r\n    str = nonFiniteToString(x);\r\n  } else {\r\n    str = finiteToString(x);\r\n    i = str.indexOf('.');\r\n\r\n    // Use exponential notation according to `toExpPos` and `toExpNeg`? No, but if required:\r\n    // maxBinaryExponent = floor((decimalExponent + 1) * log[2](10))\r\n    // minBinaryExponent = floor(decimalExponent * log[2](10))\r\n    // log[2](10) = 3.321928094887362347870319429489390175864\r\n\r\n    if (isExp) {\r\n      base = 2;\r\n      if (baseOut == 16) {\r\n        sd = sd * 4 - 3;\r\n      } else if (baseOut == 8) {\r\n        sd = sd * 3 - 2;\r\n      }\r\n    } else {\r\n      base = baseOut;\r\n    }\r\n\r\n    // Convert the number as an integer then divide the result by its base raised to a power such\r\n    // that the fraction part will be restored.\r\n\r\n    // Non-integer.\r\n    if (i >= 0) {\r\n      str = str.replace('.', '');\r\n      y = new Ctor(1);\r\n      y.e = str.length - i;\r\n      y.d = convertBase(finiteToString(y), 10, base);\r\n      y.e = y.d.length;\r\n    }\r\n\r\n    xd = convertBase(str, 10, base);\r\n    e = len = xd.length;\r\n\r\n    // Remove trailing zeros.\r\n    for (; xd[--len] == 0;) xd.pop();\r\n\r\n    if (!xd[0]) {\r\n      str = isExp ? '0p+0' : '0';\r\n    } else {\r\n      if (i < 0) {\r\n        e--;\r\n      } else {\r\n        x = new Ctor(x);\r\n        x.d = xd;\r\n        x.e = e;\r\n        x = divide(x, y, sd, rm, 0, base);\r\n        xd = x.d;\r\n        e = x.e;\r\n        roundUp = inexact;\r\n      }\r\n\r\n      // The rounding digit, i.e. the digit after the digit that may be rounded up.\r\n      i = xd[sd];\r\n      k = base / 2;\r\n      roundUp = roundUp || xd[sd + 1] !== void 0;\r\n\r\n      roundUp = rm < 4\r\n        ? (i !== void 0 || roundUp) && (rm === 0 || rm === (x.s < 0 ? 3 : 2))\r\n        : i > k || i === k && (rm === 4 || roundUp || rm === 6 && xd[sd - 1] & 1 ||\r\n          rm === (x.s < 0 ? 8 : 7));\r\n\r\n      xd.length = sd;\r\n\r\n      if (roundUp) {\r\n\r\n        // Rounding up may mean the previous digit has to be rounded up and so on.\r\n        for (; ++xd[--sd] > base - 1;) {\r\n          xd[sd] = 0;\r\n          if (!sd) {\r\n            ++e;\r\n            xd.unshift(1);\r\n          }\r\n        }\r\n      }\r\n\r\n      // Determine trailing zeros.\r\n      for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n      // E.g. [4, 11, 15] becomes 4bf.\r\n      for (i = 0, str = ''; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n\r\n      // Add binary exponent suffix?\r\n      if (isExp) {\r\n        if (len > 1) {\r\n          if (baseOut == 16 || baseOut == 8) {\r\n            i = baseOut == 16 ? 4 : 3;\r\n            for (--len; len % i; len++) str += '0';\r\n            xd = convertBase(str, base, baseOut);\r\n            for (len = xd.length; !xd[len - 1]; --len);\r\n\r\n            // xd[0] will always be be 1\r\n            for (i = 1, str = '1.'; i < len; i++) str += NUMERALS.charAt(xd[i]);\r\n          } else {\r\n            str = str.charAt(0) + '.' + str.slice(1);\r\n          }\r\n        }\r\n\r\n        str =  str + (e < 0 ? 'p' : 'p+') + e;\r\n      } else if (e < 0) {\r\n        for (; ++e;) str = '0' + str;\r\n        str = '0.' + str;\r\n      } else {\r\n        if (++e > len) for (e -= len; e-- ;) str += '0';\r\n        else if (e < len) str = str.slice(0, e) + '.' + str.slice(e);\r\n      }\r\n    }\r\n\r\n    str = (baseOut == 16 ? '0x' : baseOut == 2 ? '0b' : baseOut == 8 ? '0o' : '') + str;\r\n  }\r\n\r\n  return x.s < 0 ? '-' + str : str;\r\n}\r\n\r\n\r\n// Does not strip trailing zeros.\r\nfunction truncate(arr, len) {\r\n  if (arr.length > len) {\r\n    arr.length = len;\r\n    return true;\r\n  }\r\n}\r\n\r\n\r\n// Decimal methods\r\n\r\n\r\n/*\r\n *  abs\r\n *  acos\r\n *  acosh\r\n *  add\r\n *  asin\r\n *  asinh\r\n *  atan\r\n *  atanh\r\n *  atan2\r\n *  cbrt\r\n *  ceil\r\n *  clamp\r\n *  clone\r\n *  config\r\n *  cos\r\n *  cosh\r\n *  div\r\n *  exp\r\n *  floor\r\n *  hypot\r\n *  ln\r\n *  log\r\n *  log2\r\n *  log10\r\n *  max\r\n *  min\r\n *  mod\r\n *  mul\r\n *  pow\r\n *  random\r\n *  round\r\n *  set\r\n *  sign\r\n *  sin\r\n *  sinh\r\n *  sqrt\r\n *  sub\r\n *  sum\r\n *  tan\r\n *  tanh\r\n *  trunc\r\n */\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the absolute value of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction abs(x) {\r\n  return new this(x).abs();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arccosine in radians of `x`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction acos(x) {\r\n  return new this(x).acos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic cosine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction acosh(x) {\r\n  return new this(x).acosh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of `x` and `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction add(x, y) {\r\n  return new this(x).plus(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arcsine in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction asin(x) {\r\n  return new this(x).asin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic sine of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction asinh(x) {\r\n  return new this(x).asinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction atan(x) {\r\n  return new this(x).atan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the inverse of the hyperbolic tangent of `x`, rounded to\r\n * `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction atanh(x) {\r\n  return new this(x).atanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the arctangent in radians of `y/x` in the range -pi to pi\r\n * (inclusive), rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * Domain: [-Infinity, Infinity]\r\n * Range: [-pi, pi]\r\n *\r\n * y {number|string|bigint|Decimal} The y-coordinate.\r\n * x {number|string|bigint|Decimal} The x-coordinate.\r\n *\r\n * atan2(±0, -0)               = ±pi\r\n * atan2(±0, +0)               = ±0\r\n * atan2(±0, -x)               = ±pi for x > 0\r\n * atan2(±0, x)                = ±0 for x > 0\r\n * atan2(-y, ±0)               = -pi/2 for y > 0\r\n * atan2(y, ±0)                = pi/2 for y > 0\r\n * atan2(±y, -Infinity)        = ±pi for finite y > 0\r\n * atan2(±y, +Infinity)        = ±0 for finite y > 0\r\n * atan2(±Infinity, x)         = ±pi/2 for finite x\r\n * atan2(±Infinity, -Infinity) = ±3*pi/4\r\n * atan2(±Infinity, +Infinity) = ±pi/4\r\n * atan2(NaN, x) = NaN\r\n * atan2(y, NaN) = NaN\r\n *\r\n */\r\nfunction atan2(y, x) {\r\n  y = new this(y);\r\n  x = new this(x);\r\n  var r,\r\n    pr = this.precision,\r\n    rm = this.rounding,\r\n    wpr = pr + 4;\r\n\r\n  // Either NaN\r\n  if (!y.s || !x.s) {\r\n    r = new this(NaN);\r\n\r\n  // Both ±Infinity\r\n  } else if (!y.d && !x.d) {\r\n    r = getPi(this, wpr, 1).times(x.s > 0 ? 0.25 : 0.75);\r\n    r.s = y.s;\r\n\r\n  // x is ±Infinity or y is ±0\r\n  } else if (!x.d || y.isZero()) {\r\n    r = x.s < 0 ? getPi(this, pr, rm) : new this(0);\r\n    r.s = y.s;\r\n\r\n  // y is ±Infinity or x is ±0\r\n  } else if (!y.d || x.isZero()) {\r\n    r = getPi(this, wpr, 1).times(0.5);\r\n    r.s = y.s;\r\n\r\n  // Both non-zero and finite\r\n  } else if (x.s < 0) {\r\n    this.precision = wpr;\r\n    this.rounding = 1;\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n    x = getPi(this, wpr, 1);\r\n    this.precision = pr;\r\n    this.rounding = rm;\r\n    r = y.s < 0 ? r.minus(x) : r.plus(x);\r\n  } else {\r\n    r = this.atan(divide(y, x, wpr, 1));\r\n  }\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cube root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction cbrt(x) {\r\n  return new this(x).cbrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using `ROUND_CEIL`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ceil(x) {\r\n  return finalise(x = new this(x), x.e + 1, 2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` clamped to the range delineated by `min` and `max`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * min {number|string|bigint|Decimal}\r\n * max {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction clamp(x, min, max) {\r\n  return new this(x).clamp(min, max);\r\n}\r\n\r\n\r\n/*\r\n * Configure global settings for a Decimal constructor.\r\n *\r\n * `obj` is an object with one or more of the following properties,\r\n *\r\n *   precision  {number}\r\n *   rounding   {number}\r\n *   toExpNeg   {number}\r\n *   toExpPos   {number}\r\n *   maxE       {number}\r\n *   minE       {number}\r\n *   modulo     {number}\r\n *   crypto     {boolean|number}\r\n *   defaults   {true}\r\n *\r\n * E.g. Decimal.config({ precision: 20, rounding: 4 })\r\n *\r\n */\r\nfunction config(obj) {\r\n  if (!obj || typeof obj !== 'object') throw Error(decimalError + 'Object expected');\r\n  var i, p, v,\r\n    useDefaults = obj.defaults === true,\r\n    ps = [\r\n      'precision', 1, MAX_DIGITS,\r\n      'rounding', 0, 8,\r\n      'toExpNeg', -EXP_LIMIT, 0,\r\n      'toExpPos', 0, EXP_LIMIT,\r\n      'maxE', 0, EXP_LIMIT,\r\n      'minE', -EXP_LIMIT, 0,\r\n      'modulo', 0, 9\r\n    ];\r\n\r\n  for (i = 0; i < ps.length; i += 3) {\r\n    if (p = ps[i], useDefaults) this[p] = DEFAULTS[p];\r\n    if ((v = obj[p]) !== void 0) {\r\n      if (mathfloor(v) === v && v >= ps[i + 1] && v <= ps[i + 2]) this[p] = v;\r\n      else throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  if (p = 'crypto', useDefaults) this[p] = DEFAULTS[p];\r\n  if ((v = obj[p]) !== void 0) {\r\n    if (v === true || v === false || v === 0 || v === 1) {\r\n      if (v) {\r\n        if (typeof crypto != 'undefined' && crypto &&\r\n          (crypto.getRandomValues || crypto.randomBytes)) {\r\n          this[p] = true;\r\n        } else {\r\n          throw Error(cryptoUnavailable);\r\n        }\r\n      } else {\r\n        this[p] = false;\r\n      }\r\n    } else {\r\n      throw Error(invalidArgument + p + ': ' + v);\r\n    }\r\n  }\r\n\r\n  return this;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the cosine of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cos(x) {\r\n  return new this(x).cos();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic cosine of `x`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction cosh(x) {\r\n  return new this(x).cosh();\r\n}\r\n\r\n\r\n/*\r\n * Create and return a Decimal constructor with the same configuration properties as this Decimal\r\n * constructor.\r\n *\r\n */\r\nfunction clone(obj) {\r\n  var i, p, ps;\r\n\r\n  /*\r\n   * The Decimal constructor and exported function.\r\n   * Return a new Decimal instance.\r\n   *\r\n   * v {number|string|bigint|Decimal} A numeric value.\r\n   *\r\n   */\r\n  function Decimal(v) {\r\n    var e, i, t,\r\n      x = this;\r\n\r\n    // Decimal called without new.\r\n    if (!(x instanceof Decimal)) return new Decimal(v);\r\n\r\n    // Retain a reference to this Decimal constructor, and shadow Decimal.prototype.constructor\r\n    // which points to Object.\r\n    x.constructor = Decimal;\r\n\r\n    if (isDecimalInstance(v)) {\r\n      x.s = v.s;\r\n\r\n      if (external) {\r\n        if (!v.d || v.e > Decimal.maxE) {\r\n\r\n          // Infinity.\r\n          x.e = NaN;\r\n          x.d = null;\r\n        } else if (v.e < Decimal.minE) {\r\n\r\n          // Zero.\r\n          x.e = 0;\r\n          x.d = [0];\r\n        } else {\r\n          x.e = v.e;\r\n          x.d = v.d.slice();\r\n        }\r\n      } else {\r\n        x.e = v.e;\r\n        x.d = v.d ? v.d.slice() : v.d;\r\n      }\r\n\r\n      return;\r\n    }\r\n\r\n    t = typeof v;\r\n\r\n    if (t === 'number') {\r\n      if (v === 0) {\r\n        x.s = 1 / v < 0 ? -1 : 1;\r\n        x.e = 0;\r\n        x.d = [0];\r\n        return;\r\n      }\r\n\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      // Fast path for small integers.\r\n      if (v === ~~v && v < 1e7) {\r\n        for (e = 0, i = v; i >= 10; i /= 10) e++;\r\n\r\n        if (external) {\r\n          if (e > Decimal.maxE) {\r\n            x.e = NaN;\r\n            x.d = null;\r\n          } else if (e < Decimal.minE) {\r\n            x.e = 0;\r\n            x.d = [0];\r\n          } else {\r\n            x.e = e;\r\n            x.d = [v];\r\n          }\r\n        } else {\r\n          x.e = e;\r\n          x.d = [v];\r\n        }\r\n\r\n        return;\r\n      }\r\n\r\n      // Infinity or NaN?\r\n      if (v * 0 !== 0) {\r\n        if (!v) x.s = NaN;\r\n        x.e = NaN;\r\n        x.d = null;\r\n        return;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    if (t === 'string') {\r\n      if ((i = v.charCodeAt(0)) === 45) {  // minus sign\r\n        v = v.slice(1);\r\n        x.s = -1;\r\n      } else {\r\n        if (i === 43) v = v.slice(1);  // plus sign\r\n        x.s = 1;\r\n      }\r\n\r\n      return isDecimal.test(v) ? parseDecimal(x, v) : parseOther(x, v);\r\n    }\r\n\r\n    if (t === 'bigint') {\r\n      if (v < 0) {\r\n        v = -v;\r\n        x.s = -1;\r\n      } else {\r\n        x.s = 1;\r\n      }\r\n\r\n      return parseDecimal(x, v.toString());\r\n    }\r\n\r\n    throw Error(invalidArgument + v);\r\n  }\r\n\r\n  Decimal.prototype = P;\r\n\r\n  Decimal.ROUND_UP = 0;\r\n  Decimal.ROUND_DOWN = 1;\r\n  Decimal.ROUND_CEIL = 2;\r\n  Decimal.ROUND_FLOOR = 3;\r\n  Decimal.ROUND_HALF_UP = 4;\r\n  Decimal.ROUND_HALF_DOWN = 5;\r\n  Decimal.ROUND_HALF_EVEN = 6;\r\n  Decimal.ROUND_HALF_CEIL = 7;\r\n  Decimal.ROUND_HALF_FLOOR = 8;\r\n  Decimal.EUCLID = 9;\r\n\r\n  Decimal.config = Decimal.set = config;\r\n  Decimal.clone = clone;\r\n  Decimal.isDecimal = isDecimalInstance;\r\n\r\n  Decimal.abs = abs;\r\n  Decimal.acos = acos;\r\n  Decimal.acosh = acosh;        // ES6\r\n  Decimal.add = add;\r\n  Decimal.asin = asin;\r\n  Decimal.asinh = asinh;        // ES6\r\n  Decimal.atan = atan;\r\n  Decimal.atanh = atanh;        // ES6\r\n  Decimal.atan2 = atan2;\r\n  Decimal.cbrt = cbrt;          // ES6\r\n  Decimal.ceil = ceil;\r\n  Decimal.clamp = clamp;\r\n  Decimal.cos = cos;\r\n  Decimal.cosh = cosh;          // ES6\r\n  Decimal.div = div;\r\n  Decimal.exp = exp;\r\n  Decimal.floor = floor;\r\n  Decimal.hypot = hypot;        // ES6\r\n  Decimal.ln = ln;\r\n  Decimal.log = log;\r\n  Decimal.log10 = log10;        // ES6\r\n  Decimal.log2 = log2;          // ES6\r\n  Decimal.max = max;\r\n  Decimal.min = min;\r\n  Decimal.mod = mod;\r\n  Decimal.mul = mul;\r\n  Decimal.pow = pow;\r\n  Decimal.random = random;\r\n  Decimal.round = round;\r\n  Decimal.sign = sign;          // ES6\r\n  Decimal.sin = sin;\r\n  Decimal.sinh = sinh;          // ES6\r\n  Decimal.sqrt = sqrt;\r\n  Decimal.sub = sub;\r\n  Decimal.sum = sum;\r\n  Decimal.tan = tan;\r\n  Decimal.tanh = tanh;          // ES6\r\n  Decimal.trunc = trunc;        // ES6\r\n\r\n  if (obj === void 0) obj = {};\r\n  if (obj) {\r\n    if (obj.defaults !== true) {\r\n      ps = ['precision', 'rounding', 'toExpNeg', 'toExpPos', 'maxE', 'minE', 'modulo', 'crypto'];\r\n      for (i = 0; i < ps.length;) if (!obj.hasOwnProperty(p = ps[i++])) obj[p] = this[p];\r\n    }\r\n  }\r\n\r\n  Decimal.config(obj);\r\n\r\n  return Decimal;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` divided by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction div(x, y) {\r\n  return new this(x).div(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural exponential of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The power to which to raise the base of the natural log.\r\n *\r\n */\r\nfunction exp(x) {\r\n  return new this(x).exp();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` round to an integer using `ROUND_FLOOR`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction floor(x) {\r\n  return finalise(x = new this(x), x.e + 1, 3);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of the sum of the squares of the arguments,\r\n * rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * hypot(a, b, ...) = sqrt(a^2 + b^2 + ...)\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction hypot() {\r\n  var i, n,\r\n    t = new this(0);\r\n\r\n  external = false;\r\n\r\n  for (i = 0; i < arguments.length;) {\r\n    n = new this(arguments[i++]);\r\n    if (!n.d) {\r\n      if (n.s) {\r\n        external = true;\r\n        return new this(1 / 0);\r\n      }\r\n      t = n;\r\n    } else if (t.d) {\r\n      t = t.plus(n.times(n));\r\n    }\r\n  }\r\n\r\n  external = true;\r\n\r\n  return t.sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return true if object is a Decimal instance (where Decimal is any Decimal constructor),\r\n * otherwise return false.\r\n *\r\n */\r\nfunction isDecimalInstance(obj) {\r\n  return obj instanceof Decimal || obj && obj.toStringTag === tag || false;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the natural logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction ln(x) {\r\n  return new this(x).ln();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the log of `x` to the base `y`, or to base 10 if no base\r\n * is specified, rounded to `precision` significant digits using rounding mode `rounding`.\r\n *\r\n * log[y](x)\r\n *\r\n * x {number|string|bigint|Decimal} The argument of the logarithm.\r\n * y {number|string|bigint|Decimal} The base of the logarithm.\r\n *\r\n */\r\nfunction log(x, y) {\r\n  return new this(x).log(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 2 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log2(x) {\r\n  return new this(x).log(2);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the base 10 logarithm of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction log10(x) {\r\n  return new this(x).log(10);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the maximum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction max() {\r\n  return maxOrMin(this, arguments, -1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the minimum of the arguments.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction min() {\r\n  return maxOrMin(this, arguments, 1);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` modulo `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mod(x, y) {\r\n  return new this(x).mod(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` multiplied by `y`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction mul(x, y) {\r\n  return new this(x).mul(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` raised to the power `y`, rounded to precision\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} The base.\r\n * y {number|string|bigint|Decimal} The exponent.\r\n *\r\n */\r\nfunction pow(x, y) {\r\n  return new this(x).pow(y);\r\n}\r\n\r\n\r\n/*\r\n * Returns a new Decimal with a random value equal to or greater than 0 and less than 1, and with\r\n * `sd`, or `Decimal.precision` if `sd` is omitted, significant digits (or less if trailing zeros\r\n * are produced).\r\n *\r\n * [sd] {number} Significant digits. Integer, 0 to MAX_DIGITS inclusive.\r\n *\r\n */\r\nfunction random(sd) {\r\n  var d, e, k, n,\r\n    i = 0,\r\n    r = new this(1),\r\n    rd = [];\r\n\r\n  if (sd === void 0) sd = this.precision;\r\n  else checkInt32(sd, 1, MAX_DIGITS);\r\n\r\n  k = Math.ceil(sd / LOG_BASE);\r\n\r\n  if (!this.crypto) {\r\n    for (; i < k;) rd[i++] = Math.random() * 1e7 | 0;\r\n\r\n  // Browsers supporting crypto.getRandomValues.\r\n  } else if (crypto.getRandomValues) {\r\n    d = crypto.getRandomValues(new Uint32Array(k));\r\n\r\n    for (; i < k;) {\r\n      n = d[i];\r\n\r\n      // 0 <= n < 4294967296\r\n      // Probability n >= 4.29e9, is 4967296 / 4294967296 = 0.00116 (1 in 865).\r\n      if (n >= 4.29e9) {\r\n        d[i] = crypto.getRandomValues(new Uint32Array(1))[0];\r\n      } else {\r\n\r\n        // 0 <= n <= 4289999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd[i++] = n % 1e7;\r\n      }\r\n    }\r\n\r\n  // Node.js supporting crypto.randomBytes.\r\n  } else if (crypto.randomBytes) {\r\n\r\n    // buffer\r\n    d = crypto.randomBytes(k *= 4);\r\n\r\n    for (; i < k;) {\r\n\r\n      // 0 <= n < 2147483648\r\n      n = d[i] + (d[i + 1] << 8) + (d[i + 2] << 16) + ((d[i + 3] & 0x7f) << 24);\r\n\r\n      // Probability n >= 2.14e9, is 7483648 / 2147483648 = 0.0035 (1 in 286).\r\n      if (n >= 2.14e9) {\r\n        crypto.randomBytes(4).copy(d, i);\r\n      } else {\r\n\r\n        // 0 <= n <= 2139999999\r\n        // 0 <= (n % 1e7) <= 9999999\r\n        rd.push(n % 1e7);\r\n        i += 4;\r\n      }\r\n    }\r\n\r\n    i = k / 4;\r\n  } else {\r\n    throw Error(cryptoUnavailable);\r\n  }\r\n\r\n  k = rd[--i];\r\n  sd %= LOG_BASE;\r\n\r\n  // Convert trailing digits to zeros according to sd.\r\n  if (k && sd) {\r\n    n = mathpow(10, LOG_BASE - sd);\r\n    rd[i] = (k / n | 0) * n;\r\n  }\r\n\r\n  // Remove trailing words which are zero.\r\n  for (; rd[i] === 0; i--) rd.pop();\r\n\r\n  // Zero?\r\n  if (i < 0) {\r\n    e = 0;\r\n    rd = [0];\r\n  } else {\r\n    e = -1;\r\n\r\n    // Remove leading words which are zero and adjust exponent accordingly.\r\n    for (; rd[0] === 0; e -= LOG_BASE) rd.shift();\r\n\r\n    // Count the digits of the first word of rd to determine leading zeros.\r\n    for (k = 1, n = rd[0]; n >= 10; n /= 10) k++;\r\n\r\n    // Adjust the exponent for leading zeros of the first word of rd.\r\n    if (k < LOG_BASE) e -= LOG_BASE - k;\r\n  }\r\n\r\n  r.e = e;\r\n  r.d = rd;\r\n\r\n  return r;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` rounded to an integer using rounding mode `rounding`.\r\n *\r\n * To emulate `Math.round`, set rounding to 7 (ROUND_HALF_CEIL).\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction round(x) {\r\n  return finalise(x = new this(x), x.e + 1, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return\r\n *   1    if x > 0,\r\n *  -1    if x < 0,\r\n *   0    if x is 0,\r\n *  -0    if x is -0,\r\n *   NaN  otherwise\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sign(x) {\r\n  x = new this(x);\r\n  return x.d ? (x.d[0] ? x.s : 0 * x.s) : x.s || NaN;\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sine of `x`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sin(x) {\r\n  return new this(x).sin();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic sine of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction sinh(x) {\r\n  return new this(x).sinh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the square root of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sqrt(x) {\r\n  return new this(x).sqrt();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` minus `y`, rounded to `precision` significant digits\r\n * using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n * y {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sub(x, y) {\r\n  return new this(x).sub(y);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the sum of the arguments, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * Only the result is rounded, not the intermediate calculations.\r\n *\r\n * arguments {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction sum() {\r\n  var i = 0,\r\n    args = arguments,\r\n    x = new this(args[i]);\r\n\r\n  external = false;\r\n  for (; x.s && ++i < args.length;) x = x.plus(args[i]);\r\n  external = true;\r\n\r\n  return finalise(x, this.precision, this.rounding);\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the tangent of `x`, rounded to `precision` significant\r\n * digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tan(x) {\r\n  return new this(x).tan();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is the hyperbolic tangent of `x`, rounded to `precision`\r\n * significant digits using rounding mode `rounding`.\r\n *\r\n * x {number|string|bigint|Decimal} A value in radians.\r\n *\r\n */\r\nfunction tanh(x) {\r\n  return new this(x).tanh();\r\n}\r\n\r\n\r\n/*\r\n * Return a new Decimal whose value is `x` truncated to an integer.\r\n *\r\n * x {number|string|bigint|Decimal}\r\n *\r\n */\r\nfunction trunc(x) {\r\n  return finalise(x = new this(x), x.e + 1, 1);\r\n}\r\n\r\n\r\nP[Symbol.for('nodejs.util.inspect.custom')] = P.toString;\r\nP[Symbol.toStringTag] = 'Decimal';\r\n\r\n// Create and configure initial Decimal constructor.\r\nexport var Decimal = P.constructor = clone(DEFAULTS);\r\n\r\n// Create the internal constants from their string values.\r\nLN10 = new Decimal(LN10);\r\nPI = new Decimal(PI);\r\n\r\nexport default Decimal;\r\n"], "mappings": "sPAAA,IAAAA,GAAA,GAAAC,GAAAD,GAAA,eAAAE,KCeO,SAASC,MAAaC,EAAc,CACzC,OAAQC,GAAcA,CACxB,CCbA,IAAMC,GAAS,OAAO,EAUhBC,GAAkB,IAAI,QAKNC,GAAf,KAA+B,CACpC,YAAYC,EAAc,CACpBA,IAAQH,GACVC,GAAgB,IAAI,KAAM,UAAU,YAAK,SAAS,EAAG,EAErDA,GAAgB,IAAI,KAAM,cAAc,YAAK,cAAc,EAAC,KAAI,YAAK,SAAS,EAAC,KAAI,CAEvF,CAIA,UAAW,CACT,OAAO,KAAK,YAAY,IAC1B,CAEA,UAAW,CACT,OAAOA,GAAgB,IAAI,IAAI,CACjC,CACF,EAEMG,EAAN,cAAiCF,EAAgB,CACtC,eAAgB,CACvB,MAAO,WACT,CACF,EA3CAG,GA6CMC,EAAN,cAAqBF,CAAmB,CAAxC,kCAGEG,GAAA,KAASF,IACX,EADWA,GAAA,YAEXG,GAAaF,EAAQ,QAAQ,EAlD7B,IAAAG,GAoDMC,EAAN,cAAuBN,CAAmB,CAA1C,kCAGEG,GAAA,KAASE,IACX,EADWA,GAAA,YAEXD,GAAaE,EAAU,UAAU,EAzDjC,IAAAC,GA2DMC,EAAN,cAAsBR,CAAmB,CAAzC,kCAGEG,GAAA,KAASI,IACX,EADWA,GAAA,YAEXH,GAAaI,EAAS,SAAS,EAExB,IAAMC,GAAmB,CAC9B,QAAS,CACP,OAAAP,EACA,SAAAI,EACA,QAAAE,CACF,EACA,UAAW,CACT,OAAQ,IAAIN,EAAON,EAAM,EACzB,SAAU,IAAIU,EAASV,EAAM,EAC7B,QAAS,IAAIY,EAAQZ,EAAM,CAC7B,CACF,EASA,SAASQ,GAAaM,EAAuBC,EAAc,CACzD,OAAO,eAAeD,EAAa,OAAQ,CACzC,MAAOC,EACP,aAAc,EAChB,CAAC,CACH,CCxFA,IAAMC,GAAY,IAAI,IAAI,CACxB,SACA,WACA,kBACA,OAAO,SACP,OAAO,YACP,OAAO,mBACP,OAAO,WACT,CAAC,EAiBM,SAASC,GAA+DC,EAAkB,CAC/F,OAAO,IAAI,MAAMA,EAAY,CAC3B,IAAIC,EAAQC,EAAU,CACpB,GAAIA,KAAYD,EACd,OAAOA,EAAOC,CAAQ,EAExB,GAAI,CAAAJ,GAAU,IAAII,CAAQ,EAG1B,MAAM,IAAI,UAAU,uBAAuB,cAAOA,CAAQ,EAAG,CAC/D,CACF,CAAC,CACH,CChCA,IAAMC,GAAS,IAAG,CARlB,IAAAC,EAAAC,EAQqB,QAAAA,GAAAD,EAAA,WAAW,UAAX,YAAAA,EAAoB,UAApB,YAAAC,EAA6B,QAAS,QAKrDC,GAAQ,IAAG,CAbjB,IAAAF,EAAAC,EAaoB,OAAC,CAAC,WAAW,KAAO,CAAC,GAACA,GAAAD,EAAA,WAAW,UAAX,YAAAA,EAAoB,WAApB,MAAAC,EAA8B,MAKlEE,GAAS,IAAM,CAAC,CAAC,WAAW,KAK5BC,GAAY,IAAM,OAAO,WAAW,SAAY,SAKhDC,GAAc,IAAM,OAAO,WAAW,aAAgB,SAMtDC,GAAY,IAAG,CAlCrB,IAAAN,EAkCwB,QAAAA,EAAA,WAAW,YAAX,YAAAA,EAAsB,aAAc,sBAE5D,SAASO,IAA6B,CApCtC,IAAAP,EAyDE,OATEA,EAVoB,CACpB,CAACI,GAAW,SAAS,EACrB,CAACC,GAAa,YAAY,EAC1B,CAACC,GAAW,SAAS,EACrB,CAACH,GAAQ,MAAM,EACf,CAACD,GAAO,KAAK,EACb,CAACH,GAAQ,MAAM,CACjB,EASK,QAASS,GAAWA,EAAM,CAAC,EAAE,EAAI,CAACA,EAAM,CAAC,CAAC,EAAI,CAAC,CAAE,EACjD,GAAG,CAAC,IAPP,KAAAR,EAOY,EAGhB,CAEA,IAAMS,GAAsB,CAC1B,KAAM,UACN,QAAS,qBACT,KAAM,uBACN,QAAS,yBACT,aACE,sKACJ,EAQO,SAASC,IAA+B,CAC7C,IAAMC,EAAYJ,GAAc,EAEhC,MAAO,CACL,GAAII,EAEJ,WAAYF,GAAoBE,CAAS,GAAKA,EAC9C,OAAQ,CAAC,UAAW,OAAQ,UAAW,YAAY,EAAE,SAASA,CAAS,CACzE,CACF,CCtEA,IAAIC,EAAY,KAIdC,EAAa,IAGbC,GAAW,mBAGXC,GAAO,qgCAGPC,GAAK,qgCAILC,GAAW,CAOT,UAAW,GAiBX,SAAU,EAeV,OAAQ,EAIR,SAAU,GAIV,SAAW,GAIX,KAAM,CAACL,EAIP,KAAMA,EAGN,OAAQ,EACV,EAMAM,GAASC,EACTC,EAAW,GAEXC,GAAe,kBACfC,EAAkBD,GAAe,qBACjCE,GAAyBF,GAAe,2BACxCG,GAAoBH,GAAe,qBACnCI,GAAM,mBAENC,EAAY,KAAK,MACjBC,EAAU,KAAK,IAEfC,GAAW,6CACXC,GAAQ,yDACRC,GAAU,gDACVC,GAAY,qCAEZC,EAAO,IACPC,EAAW,EACXC,GAAmB,iBAEnBC,GAAiBpB,GAAK,OAAS,EAC/BqB,GAAepB,GAAG,OAAS,EAG3BqB,EAAI,CAAE,YAAaZ,EAAI,EA0EzBY,EAAE,cAAgBA,EAAE,IAAM,UAAY,CACpC,IAAIC,EAAI,IAAI,KAAK,YAAY,IAAI,EACjC,OAAIA,EAAE,EAAI,IAAGA,EAAE,EAAI,GACZC,EAASD,CAAC,CACnB,EAQAD,EAAE,KAAO,UAAY,CACnB,OAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,EAAG,KAAK,EAAI,EAAG,CAAC,CAC3D,EAWAF,EAAE,UAAYA,EAAE,MAAQ,SAAUG,EAAKC,EAAK,CAC1C,IAAIC,EACFJ,EAAI,KACJK,EAAOL,EAAE,YAGX,GAFAE,EAAM,IAAIG,EAAKH,CAAG,EAClBC,EAAM,IAAIE,EAAKF,CAAG,EACd,CAACD,EAAI,GAAK,CAACC,EAAI,EAAG,OAAO,IAAIE,EAAK,GAAG,EACzC,GAAIH,EAAI,GAAGC,CAAG,EAAG,MAAM,MAAMnB,EAAkBmB,CAAG,EAClD,OAAAC,EAAIJ,EAAE,IAAIE,CAAG,EACNE,EAAI,EAAIF,EAAMF,EAAE,IAAIG,CAAG,EAAI,EAAIA,EAAM,IAAIE,EAAKL,CAAC,CACxD,EAWAD,EAAE,WAAaA,EAAE,IAAM,SAAUO,EAAG,CAClC,IAAIC,EAAGC,EAAGC,EAAKC,EACbV,EAAI,KACJW,EAAKX,EAAE,EACPY,GAAMN,EAAI,IAAIN,EAAE,YAAYM,CAAC,GAAG,EAChCO,EAAKb,EAAE,EACPc,EAAKR,EAAE,EAGT,GAAI,CAACK,GAAM,CAACC,EACV,MAAO,CAACC,GAAM,CAACC,EAAK,IAAMD,IAAOC,EAAKD,EAAKF,IAAOC,EAAK,EAAI,CAACD,EAAKE,EAAK,EAAI,EAAI,GAIhF,GAAI,CAACF,EAAG,CAAC,GAAK,CAACC,EAAG,CAAC,EAAG,OAAOD,EAAG,CAAC,EAAIE,EAAKD,EAAG,CAAC,EAAI,CAACE,EAAK,EAGxD,GAAID,IAAOC,EAAI,OAAOD,EAGtB,GAAIb,EAAE,IAAMM,EAAE,EAAG,OAAON,EAAE,EAAIM,EAAE,EAAIO,EAAK,EAAI,EAAI,GAMjD,IAJAJ,EAAME,EAAG,OACTD,EAAME,EAAG,OAGJL,EAAI,EAAGC,EAAIC,EAAMC,EAAMD,EAAMC,EAAKH,EAAIC,EAAG,EAAED,EAC9C,GAAII,EAAGJ,CAAC,IAAMK,EAAGL,CAAC,EAAG,OAAOI,EAAGJ,CAAC,EAAIK,EAAGL,CAAC,EAAIM,EAAK,EAAI,EAAI,GAI3D,OAAOJ,IAAQC,EAAM,EAAID,EAAMC,EAAMG,EAAK,EAAI,EAAI,EACpD,EAgBAd,EAAE,OAASA,EAAE,IAAM,UAAY,CAC7B,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAKA,EAAE,EAGFA,EAAE,EAAE,CAAC,GAEVe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,KAAK,IAAIf,EAAE,EAAGA,EAAE,GAAG,CAAC,EAAIL,EAC9CU,EAAK,SAAW,EAEhBL,EAAIiB,GAAOZ,EAAMa,GAAiBb,EAAML,CAAC,CAAC,EAE1CK,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAETf,EAASpB,GAAY,GAAKA,GAAY,EAAImB,EAAE,IAAI,EAAIA,EAAGe,EAAIC,EAAI,EAAI,GAZtD,IAAIX,EAAK,CAAC,EAHb,IAAIA,EAAK,GAAG,CAgB/B,EAmBAN,EAAE,SAAWA,EAAE,KAAO,UAAY,CAChC,IAAI,EAAGoB,EAAGC,EAAGC,EAAGC,EAAKC,EAAGC,EAAIC,EAAGC,EAAIC,EACjC3B,EAAI,KACJK,EAAOL,EAAE,YAEX,GAAI,CAACA,EAAE,SAAS,GAAKA,EAAE,OAAO,EAAG,OAAO,IAAIK,EAAKL,CAAC,EAoClD,IAnCAlB,EAAW,GAGXyC,EAAIvB,EAAE,EAAIX,EAAQW,EAAE,EAAIA,EAAG,EAAI,CAAC,EAI5B,CAACuB,GAAK,KAAK,IAAIA,CAAC,GAAK,KACvBH,EAAIQ,EAAe5B,EAAE,CAAC,EACtB,EAAIA,EAAE,GAGFuB,GAAK,EAAIH,EAAE,OAAS,GAAK,KAAGA,GAAMG,GAAK,GAAKA,GAAK,GAAK,IAAM,MAChEA,EAAIlC,EAAQ+B,EAAG,EAAI,CAAC,EAGpB,EAAIhC,GAAW,EAAI,GAAK,CAAC,GAAK,EAAI,IAAM,EAAI,EAAI,GAAK,IAEjDmC,GAAK,IACPH,EAAI,KAAO,GAEXA,EAAIG,EAAE,cAAc,EACpBH,EAAIA,EAAE,MAAM,EAAGA,EAAE,QAAQ,GAAG,EAAI,CAAC,EAAI,GAGvCC,EAAI,IAAIhB,EAAKe,CAAC,EACdC,EAAE,EAAIrB,EAAE,GAERqB,EAAI,IAAIhB,EAAKkB,EAAE,SAAS,CAAC,EAG3BC,GAAM,EAAInB,EAAK,WAAa,IAW1B,GANAoB,EAAIJ,EACJK,EAAKD,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,EACvBE,EAAUD,EAAG,KAAK1B,CAAC,EACnBqB,EAAIQ,EAAOF,EAAQ,KAAK3B,CAAC,EAAE,MAAMyB,CAAC,EAAGE,EAAQ,KAAKD,CAAE,EAAGF,EAAK,EAAG,CAAC,EAG5DI,EAAeH,EAAE,CAAC,EAAE,MAAM,EAAGD,CAAE,KAAOJ,EAAIQ,EAAeP,EAAE,CAAC,GAAG,MAAM,EAAGG,CAAE,EAK5E,GAJAJ,EAAIA,EAAE,MAAMI,EAAK,EAAGA,EAAK,CAAC,EAItBJ,GAAK,QAAU,CAACE,GAAOF,GAAK,OAAQ,CAItC,GAAI,CAACE,IACHrB,EAASwB,EAAG,EAAI,EAAG,CAAC,EAEhBA,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,EAAE,GAAGzB,CAAC,GAAG,CAC7BqB,EAAII,EACJ,KACF,CAGFD,GAAM,EACNF,EAAM,CACR,KAAO,EAID,CAAC,CAACF,GAAK,CAAC,CAACA,EAAE,MAAM,CAAC,GAAKA,EAAE,OAAO,CAAC,GAAK,OAGxCnB,EAASoB,EAAG,EAAI,EAAG,CAAC,EACpBF,EAAI,CAACE,EAAE,MAAMA,CAAC,EAAE,MAAMA,CAAC,EAAE,GAAGrB,CAAC,GAG/B,KACF,CAIJ,OAAAlB,EAAW,GAEJmB,EAASoB,EAAG,EAAGhB,EAAK,SAAUc,CAAC,CACxC,EAOApB,EAAE,cAAgBA,EAAE,GAAK,UAAY,CACnC,IAAI+B,EACFC,EAAI,KAAK,EACTX,EAAI,IAEN,GAAIW,EAAG,CAML,GALAD,EAAIC,EAAE,OAAS,EACfX,GAAKU,EAAI1C,EAAU,KAAK,EAAIO,CAAQ,GAAKA,EAGzCmC,EAAIC,EAAED,CAAC,EACHA,EAAG,KAAOA,EAAI,IAAM,EAAGA,GAAK,GAAIV,IAChCA,EAAI,IAAGA,EAAI,EACjB,CAEA,OAAOA,CACT,EAwBArB,EAAE,UAAYA,EAAE,IAAM,SAAUO,EAAG,CACjC,OAAOuB,EAAO,KAAM,IAAI,KAAK,YAAYvB,CAAC,CAAC,CAC7C,EAQAP,EAAE,mBAAqBA,EAAE,SAAW,SAAUO,EAAG,CAC/C,IAAIN,EAAI,KACNK,EAAOL,EAAE,YACX,OAAOC,EAAS4B,EAAO7B,EAAG,IAAIK,EAAKC,CAAC,EAAG,EAAG,EAAG,CAAC,EAAGD,EAAK,UAAWA,EAAK,QAAQ,CAChF,EAOAN,EAAE,OAASA,EAAE,GAAK,SAAUO,EAAG,CAC7B,OAAO,KAAK,IAAIA,CAAC,IAAM,CACzB,EAQAP,EAAE,MAAQ,UAAY,CACpB,OAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,EAAG,KAAK,EAAI,EAAG,CAAC,CAC3D,EAQAF,EAAE,YAAcA,EAAE,GAAK,SAAUO,EAAG,CAClC,OAAO,KAAK,IAAIA,CAAC,EAAI,CACvB,EAQAP,EAAE,qBAAuBA,EAAE,IAAM,SAAUO,EAAG,CAC5C,IAAIF,EAAI,KAAK,IAAIE,CAAC,EAClB,OAAOF,GAAK,GAAKA,IAAM,CACzB,EA4BAL,EAAE,iBAAmBA,EAAE,KAAO,UAAY,CACxC,IAAIK,EAAG,EAAGW,EAAIC,EAAIgB,EAChBhC,EAAI,KACJK,EAAOL,EAAE,YACTiC,EAAM,IAAI5B,EAAK,CAAC,EAElB,GAAI,CAACL,EAAE,SAAS,EAAG,OAAO,IAAIK,EAAKL,EAAE,EAAI,IAAQ,GAAG,EACpD,GAAIA,EAAE,OAAO,EAAG,OAAOiC,EAEvBlB,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,KAAK,IAAIf,EAAE,EAAGA,EAAE,GAAG,CAAC,EAAI,EAC9CK,EAAK,SAAW,EAChB2B,EAAMhC,EAAE,EAAE,OAONgC,EAAM,IACR5B,EAAI,KAAK,KAAK4B,EAAM,CAAC,EACrB,GAAK,EAAIE,GAAQ,EAAG9B,CAAC,GAAG,SAAS,IAEjCA,EAAI,GACJ,EAAI,gCAGNJ,EAAImC,EAAa9B,EAAM,EAAGL,EAAE,MAAM,CAAC,EAAG,IAAIK,EAAK,CAAC,EAAG,EAAI,EAMvD,QAHI+B,EACF7B,EAAIH,EACJiC,EAAK,IAAIhC,EAAK,CAAC,EACVE,KACL6B,EAAUpC,EAAE,MAAMA,CAAC,EACnBA,EAAIiC,EAAI,MAAMG,EAAQ,MAAMC,EAAG,MAAMD,EAAQ,MAAMC,CAAE,CAAC,CAAC,CAAC,EAG1D,OAAOpC,EAASD,EAAGK,EAAK,UAAYU,EAAIV,EAAK,SAAWW,EAAI,EAAI,CAClE,EAiCAjB,EAAE,eAAiBA,EAAE,KAAO,UAAY,CACtC,IAAIK,EAAGW,EAAIC,EAAIgB,EACbhC,EAAI,KACJK,EAAOL,EAAE,YAEX,GAAI,CAACA,EAAE,SAAS,GAAKA,EAAE,OAAO,EAAG,OAAO,IAAIK,EAAKL,CAAC,EAQlD,GANAe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,KAAK,IAAIf,EAAE,EAAGA,EAAE,GAAG,CAAC,EAAI,EAC9CK,EAAK,SAAW,EAChB2B,EAAMhC,EAAE,EAAE,OAENgC,EAAM,EACRhC,EAAImC,EAAa9B,EAAM,EAAGL,EAAGA,EAAG,EAAI,MAC/B,CAWLI,EAAI,IAAM,KAAK,KAAK4B,CAAG,EACvB5B,EAAIA,EAAI,GAAK,GAAKA,EAAI,EAEtBJ,EAAIA,EAAE,MAAM,EAAIkC,GAAQ,EAAG9B,CAAC,CAAC,EAC7BJ,EAAImC,EAAa9B,EAAM,EAAGL,EAAGA,EAAG,EAAI,EAOpC,QAJIsC,EACFC,EAAK,IAAIlC,EAAK,CAAC,EACfmC,EAAM,IAAInC,EAAK,EAAE,EACjBoC,EAAM,IAAIpC,EAAK,EAAE,EACZD,KACLkC,EAAUtC,EAAE,MAAMA,CAAC,EACnBA,EAAIA,EAAE,MAAMuC,EAAG,KAAKD,EAAQ,MAAME,EAAI,MAAMF,CAAO,EAAE,KAAKG,CAAG,CAAC,CAAC,CAAC,CAEpE,CAEA,OAAApC,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAETf,EAASD,EAAGe,EAAIC,EAAI,EAAI,CACjC,EAmBAjB,EAAE,kBAAoBA,EAAE,KAAO,UAAY,CACzC,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAKA,EAAE,SAAS,EACZA,EAAE,OAAO,EAAU,IAAIK,EAAKL,CAAC,GAEjCe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,EACtBV,EAAK,SAAW,EAETwB,EAAO7B,EAAE,KAAK,EAAGA,EAAE,KAAK,EAAGK,EAAK,UAAYU,EAAIV,EAAK,SAAWW,CAAE,GAR/C,IAAIX,EAAKL,EAAE,CAAC,CASxC,EAsBAD,EAAE,cAAgBA,EAAE,KAAO,UAAY,CACrC,IAAIC,EAAI,KACNK,EAAOL,EAAE,YACTI,EAAIJ,EAAE,IAAI,EAAE,IAAI,CAAC,EACjBe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAEZ,OAAID,IAAM,GACDA,IAAM,EAETJ,EAAE,MAAM,EAAI0C,EAAMrC,EAAMU,EAAIC,CAAE,EAAI,IAAIX,EAAK,CAAC,EAE5C,IAAIA,EAAK,GAAG,EAGdL,EAAE,OAAO,EAAU0C,EAAMrC,EAAMU,EAAK,EAAGC,CAAE,EAAE,MAAM,EAAG,GAIxDX,EAAK,UAAYU,EAAK,EACtBV,EAAK,SAAW,EAGhBL,EAAI,IAAIK,EAAK,CAAC,EAAE,MAAML,CAAC,EAAE,IAAIA,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,EAEpDK,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAEThB,EAAE,MAAM,CAAC,EAClB,EAsBAD,EAAE,wBAA0BA,EAAE,MAAQ,UAAY,CAChD,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAIA,EAAE,IAAI,CAAC,EAAU,IAAIK,EAAKL,EAAE,GAAG,CAAC,EAAI,EAAI,GAAG,EAC1CA,EAAE,SAAS,GAEhBe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,KAAK,IAAI,KAAK,IAAIf,EAAE,CAAC,EAAGA,EAAE,GAAG,CAAC,EAAI,EACxDK,EAAK,SAAW,EAChBvB,EAAW,GAEXkB,EAAIA,EAAE,MAAMA,CAAC,EAAE,MAAM,CAAC,EAAE,KAAK,EAAE,KAAKA,CAAC,EAErClB,EAAW,GACXuB,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAEThB,EAAE,GAAG,GAdc,IAAIK,EAAKL,CAAC,CAetC,EAmBAD,EAAE,sBAAwBA,EAAE,MAAQ,UAAY,CAC9C,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,MAAI,CAACA,EAAE,SAAS,GAAKA,EAAE,OAAO,EAAU,IAAIK,EAAKL,CAAC,GAElDe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,EAAI,KAAK,IAAI,KAAK,IAAIf,EAAE,CAAC,EAAGA,EAAE,GAAG,CAAC,EAAI,EAC5DK,EAAK,SAAW,EAChBvB,EAAW,GAEXkB,EAAIA,EAAE,MAAMA,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAKA,CAAC,EAEpClB,EAAW,GACXuB,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAEThB,EAAE,GAAG,EACd,EAsBAD,EAAE,yBAA2BA,EAAE,MAAQ,UAAY,CACjD,IAAIgB,EAAIC,EAAI2B,EAAKC,EACf5C,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAKA,EAAE,SAAS,EACZA,EAAE,GAAK,EAAU,IAAIK,EAAKL,EAAE,IAAI,EAAE,GAAG,CAAC,EAAIA,EAAE,EAAI,EAAIA,EAAE,OAAO,EAAIA,EAAI,GAAG,GAE5Ee,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVuC,EAAM5C,EAAE,GAAG,EAEP,KAAK,IAAI4C,EAAK7B,CAAE,EAAI,EAAI,CAACf,EAAE,EAAI,EAAUC,EAAS,IAAII,EAAKL,CAAC,EAAGe,EAAIC,EAAI,EAAI,GAE/EX,EAAK,UAAYsC,EAAMC,EAAM5C,EAAE,EAE/BA,EAAI6B,EAAO7B,EAAE,KAAK,CAAC,EAAG,IAAIK,EAAK,CAAC,EAAE,MAAML,CAAC,EAAG2C,EAAM5B,EAAI,CAAC,EAEvDV,EAAK,UAAYU,EAAK,EACtBV,EAAK,SAAW,EAEhBL,EAAIA,EAAE,GAAG,EAETK,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAEThB,EAAE,MAAM,EAAG,IArBQ,IAAIK,EAAK,GAAG,CAsBxC,EAwBAN,EAAE,YAAcA,EAAE,KAAO,UAAY,CACnC,IAAI8C,EAAQzC,EACVW,EAAIC,EACJhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAIA,EAAE,OAAO,EAAU,IAAIK,EAAKL,CAAC,GAEjCI,EAAIJ,EAAE,IAAI,EAAE,IAAI,CAAC,EACjBe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAEND,IAAM,GAGJA,IAAM,GACRyC,EAASH,EAAMrC,EAAMU,EAAK,EAAGC,CAAE,EAAE,MAAM,EAAG,EAC1C6B,EAAO,EAAI7C,EAAE,EACN6C,GAIF,IAAIxC,EAAK,GAAG,GAKrBA,EAAK,UAAYU,EAAK,EACtBV,EAAK,SAAW,EAEhBL,EAAIA,EAAE,IAAI,IAAIK,EAAK,CAAC,EAAE,MAAML,EAAE,MAAMA,CAAC,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAAE,KAAK,EAE7DK,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAEThB,EAAE,MAAM,CAAC,GAClB,EAqBAD,EAAE,eAAiBA,EAAE,KAAO,UAAY,CACtC,IAAIQ,EAAGC,EAAGJ,EAAGgB,EAAG0B,EAAIrB,EAAGJ,EAAGsB,EAAKI,EAC7B/C,EAAI,KACJK,EAAOL,EAAE,YACTe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAEZ,GAAKL,EAAE,SAAS,EAOT,IAAIA,EAAE,OAAO,EAClB,OAAO,IAAIK,EAAKL,CAAC,EACZ,GAAIA,EAAE,IAAI,EAAE,GAAG,CAAC,GAAKe,EAAK,GAAKjB,GACpC,OAAAuB,EAAIqB,EAAMrC,EAAMU,EAAK,EAAGC,CAAE,EAAE,MAAM,GAAI,EACtCK,EAAE,EAAIrB,EAAE,EACDqB,MAZU,CACjB,GAAI,CAACrB,EAAE,EAAG,OAAO,IAAIK,EAAK,GAAG,EAC7B,GAAIU,EAAK,GAAKjB,GACZ,OAAAuB,EAAIqB,EAAMrC,EAAMU,EAAK,EAAGC,CAAE,EAAE,MAAM,EAAG,EACrCK,EAAE,EAAIrB,EAAE,EACDqB,CAEX,CAmBA,IAXAhB,EAAK,UAAYsC,EAAM5B,EAAK,GAC5BV,EAAK,SAAW,EAQhBD,EAAI,KAAK,IAAI,GAAIuC,EAAMhD,EAAW,EAAI,CAAC,EAElCY,EAAIH,EAAGG,EAAG,EAAEA,EAAGP,EAAIA,EAAE,IAAIA,EAAE,MAAMA,CAAC,EAAE,KAAK,CAAC,EAAE,KAAK,EAAE,KAAK,CAAC,CAAC,EAW/D,IATAlB,EAAW,GAEX0B,EAAI,KAAK,KAAKmC,EAAMhD,CAAQ,EAC5ByB,EAAI,EACJ2B,EAAK/C,EAAE,MAAMA,CAAC,EACdqB,EAAI,IAAIhB,EAAKL,CAAC,EACd8C,EAAK9C,EAGEO,IAAM,IAOX,GANAuC,EAAKA,EAAG,MAAMC,CAAE,EAChBtB,EAAIJ,EAAE,MAAMyB,EAAG,IAAI1B,GAAK,CAAC,CAAC,EAE1B0B,EAAKA,EAAG,MAAMC,CAAE,EAChB1B,EAAII,EAAE,KAAKqB,EAAG,IAAI1B,GAAK,CAAC,CAAC,EAErBC,EAAE,EAAEb,CAAC,IAAM,OAAQ,IAAKD,EAAIC,EAAGa,EAAE,EAAEd,CAAC,IAAMkB,EAAE,EAAElB,CAAC,GAAKA,KAAK,CAG/D,OAAIH,IAAGiB,EAAIA,EAAE,MAAM,GAAMjB,EAAI,CAAE,GAE/BtB,EAAW,GAEJmB,EAASoB,EAAGhB,EAAK,UAAYU,EAAIV,EAAK,SAAWW,EAAI,EAAI,CAClE,EAOAjB,EAAE,SAAW,UAAY,CACvB,MAAO,CAAC,CAAC,KAAK,CAChB,EAOAA,EAAE,UAAYA,EAAE,MAAQ,UAAY,CAClC,MAAO,CAAC,CAAC,KAAK,GAAKX,EAAU,KAAK,EAAIO,CAAQ,EAAI,KAAK,EAAE,OAAS,CACpE,EAOAI,EAAE,MAAQ,UAAY,CACpB,MAAO,CAAC,KAAK,CACf,EAOAA,EAAE,WAAaA,EAAE,MAAQ,UAAY,CACnC,OAAO,KAAK,EAAI,CAClB,EAOAA,EAAE,WAAaA,EAAE,MAAQ,UAAY,CACnC,OAAO,KAAK,EAAI,CAClB,EAOAA,EAAE,OAAS,UAAY,CACrB,MAAO,CAAC,CAAC,KAAK,GAAK,KAAK,EAAE,CAAC,IAAM,CACnC,EAOAA,EAAE,SAAWA,EAAE,GAAK,SAAUO,EAAG,CAC/B,OAAO,KAAK,IAAIA,CAAC,EAAI,CACvB,EAOAP,EAAE,kBAAoBA,EAAE,IAAM,SAAUO,EAAG,CACzC,OAAO,KAAK,IAAIA,CAAC,EAAI,CACvB,EAiCAP,EAAE,UAAYA,EAAE,IAAM,SAAUiD,EAAM,CACpC,IAAIC,EAAUlB,EAAGmB,EAAa9C,EAAG+C,EAAKC,EAAK5B,EAAIH,EAC7CgC,EAAM,KACNhD,EAAOgD,EAAI,YACXtC,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACViD,EAAQ,EAGV,GAAIN,GAAQ,KACVA,EAAO,IAAI3C,EAAK,EAAE,EAClB4C,EAAW,OACN,CAKL,GAJAD,EAAO,IAAI3C,EAAK2C,CAAI,EACpBjB,EAAIiB,EAAK,EAGLA,EAAK,EAAI,GAAK,CAACjB,GAAK,CAACA,EAAE,CAAC,GAAKiB,EAAK,GAAG,CAAC,EAAG,OAAO,IAAI3C,EAAK,GAAG,EAEhE4C,EAAWD,EAAK,GAAG,EAAE,CACvB,CAKA,GAHAjB,EAAIsB,EAAI,EAGJA,EAAI,EAAI,GAAK,CAACtB,GAAK,CAACA,EAAE,CAAC,GAAKsB,EAAI,GAAG,CAAC,EACtC,OAAO,IAAIhD,EAAK0B,GAAK,CAACA,EAAE,CAAC,EAAI,KAASsB,EAAI,GAAK,EAAI,IAAMtB,EAAI,EAAI,GAAK,EAKxE,GAAIkB,EACF,GAAIlB,EAAE,OAAS,EACboB,EAAM,OACD,CACL,IAAK/C,EAAI2B,EAAE,CAAC,EAAG3B,EAAI,KAAO,GAAIA,GAAK,GACnC+C,EAAM/C,IAAM,CACd,CAyBF,GAtBAtB,EAAW,GACX0C,EAAKT,EAAKuC,EACVF,EAAMG,EAAiBF,EAAK7B,CAAE,EAC9B0B,EAAcD,EAAWO,GAAQnD,EAAMmB,EAAK,EAAE,EAAI+B,EAAiBP,EAAMxB,CAAE,EAG3EH,EAAIQ,EAAOuB,EAAKF,EAAa1B,EAAI,CAAC,EAgB9BiC,EAAoBpC,EAAE,EAAGjB,EAAIW,EAAIC,CAAE,EAErC,EAME,IALAQ,GAAM,GACN4B,EAAMG,EAAiBF,EAAK7B,CAAE,EAC9B0B,EAAcD,EAAWO,GAAQnD,EAAMmB,EAAK,EAAE,EAAI+B,EAAiBP,EAAMxB,CAAE,EAC3EH,EAAIQ,EAAOuB,EAAKF,EAAa1B,EAAI,CAAC,EAE9B,CAAC2B,EAAK,CAGJ,CAACvB,EAAeP,EAAE,CAAC,EAAE,MAAMjB,EAAI,EAAGA,EAAI,EAAE,EAAI,GAAK,OACnDiB,EAAIpB,EAASoB,EAAGN,EAAK,EAAG,CAAC,GAG3B,KACF,OACO0C,EAAoBpC,EAAE,EAAGjB,GAAK,GAAIY,CAAE,GAG/C,OAAAlC,EAAW,GAEJmB,EAASoB,EAAGN,EAAIC,CAAE,CAC3B,EAgDAjB,EAAE,MAAQA,EAAE,IAAM,SAAUO,EAAG,CAC7B,IAAIyB,EAAG2B,EAAGnD,EAAGC,EAAGJ,EAAG4B,EAAKjB,EAAIC,EAAIL,EAAIgD,EAAIC,EAAMhD,EAC5CZ,EAAI,KACJK,EAAOL,EAAE,YAKX,GAHAM,EAAI,IAAID,EAAKC,CAAC,EAGV,CAACN,EAAE,GAAK,CAACM,EAAE,EAGb,MAAI,CAACN,EAAE,GAAK,CAACM,EAAE,EAAGA,EAAI,IAAID,EAAK,GAAG,EAGzBL,EAAE,EAAGM,EAAE,EAAI,CAACA,EAAE,EAKlBA,EAAI,IAAID,EAAKC,EAAE,GAAKN,EAAE,IAAMM,EAAE,EAAIN,EAAI,GAAG,EAEvCM,EAIT,GAAIN,EAAE,GAAKM,EAAE,EACX,OAAAA,EAAE,EAAI,CAACA,EAAE,EACFN,EAAE,KAAKM,CAAC,EASjB,GANAK,EAAKX,EAAE,EACPY,EAAKN,EAAE,EACPS,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAGN,CAACM,EAAG,CAAC,GAAK,CAACC,EAAG,CAAC,EAAG,CAGpB,GAAIA,EAAG,CAAC,EAAGN,EAAE,EAAI,CAACA,EAAE,UAGXK,EAAG,CAAC,EAAGL,EAAI,IAAID,EAAKL,CAAC,MAIzB,QAAO,IAAIK,EAAKW,IAAO,EAAI,GAAK,CAAC,EAEtC,OAAOlC,EAAWmB,EAASK,EAAGS,EAAIC,CAAE,EAAIV,CAC1C,CAYA,GAPAoD,EAAItE,EAAUkB,EAAE,EAAIX,CAAQ,EAC5BgE,EAAKvE,EAAUY,EAAE,EAAIL,CAAQ,EAE7BgB,EAAKA,EAAG,MAAM,EACdP,EAAIuD,EAAKD,EAGLtD,EAAG,CAyBL,IAxBAwD,EAAOxD,EAAI,EAEPwD,GACF7B,EAAIpB,EACJP,EAAI,CAACA,EACL4B,EAAMpB,EAAG,SAETmB,EAAInB,EACJ8C,EAAIC,EACJ3B,EAAMrB,EAAG,QAMXJ,EAAI,KAAK,IAAI,KAAK,KAAKQ,EAAKpB,CAAQ,EAAGqC,CAAG,EAAI,EAE1C5B,EAAIG,IACNH,EAAIG,EACJwB,EAAE,OAAS,GAIbA,EAAE,QAAQ,EACLxB,EAAIH,EAAGG,KAAMwB,EAAE,KAAK,CAAC,EAC1BA,EAAE,QAAQ,CAGZ,KAAO,CASL,IALAxB,EAAII,EAAG,OACPqB,EAAMpB,EAAG,OACTgD,EAAOrD,EAAIyB,EACP4B,IAAM5B,EAAMzB,GAEXA,EAAI,EAAGA,EAAIyB,EAAKzB,IACnB,GAAII,EAAGJ,CAAC,GAAKK,EAAGL,CAAC,EAAG,CAClBqD,EAAOjD,EAAGJ,CAAC,EAAIK,EAAGL,CAAC,EACnB,KACF,CAGFH,EAAI,CACN,CAaA,IAXIwD,IACF7B,EAAIpB,EACJA,EAAKC,EACLA,EAAKmB,EACLzB,EAAE,EAAI,CAACA,EAAE,GAGX0B,EAAMrB,EAAG,OAIJJ,EAAIK,EAAG,OAASoB,EAAKzB,EAAI,EAAG,EAAEA,EAAGI,EAAGqB,GAAK,EAAI,EAGlD,IAAKzB,EAAIK,EAAG,OAAQL,EAAIH,GAAI,CAE1B,GAAIO,EAAG,EAAEJ,CAAC,EAAIK,EAAGL,CAAC,EAAG,CACnB,IAAKC,EAAID,EAAGC,GAAKG,EAAG,EAAEH,CAAC,IAAM,GAAIG,EAAGH,CAAC,EAAId,EAAO,EAChD,EAAEiB,EAAGH,CAAC,EACNG,EAAGJ,CAAC,GAAKb,CACX,CAEAiB,EAAGJ,CAAC,GAAKK,EAAGL,CAAC,CACf,CAGA,KAAOI,EAAG,EAAEqB,CAAG,IAAM,GAAIrB,EAAG,IAAI,EAGhC,KAAOA,EAAG,CAAC,IAAM,EAAGA,EAAG,MAAM,EAAG,EAAE+C,EAGlC,OAAK/C,EAAG,CAAC,GAETL,EAAE,EAAIK,EACNL,EAAE,EAAIuD,GAAkBlD,EAAI+C,CAAC,EAEtB5E,EAAWmB,EAASK,EAAGS,EAAIC,CAAE,EAAIV,GALrB,IAAID,EAAKW,IAAO,EAAI,GAAK,CAAC,CAM/C,EA2BAjB,EAAE,OAASA,EAAE,IAAM,SAAUO,EAAG,CAC9B,IAAIwD,EACF9D,EAAI,KACJK,EAAOL,EAAE,YAKX,OAHAM,EAAI,IAAID,EAAKC,CAAC,EAGV,CAACN,EAAE,GAAK,CAACM,EAAE,GAAKA,EAAE,GAAK,CAACA,EAAE,EAAE,CAAC,EAAU,IAAID,EAAK,GAAG,EAGnD,CAACC,EAAE,GAAKN,EAAE,GAAK,CAACA,EAAE,EAAE,CAAC,EAChBC,EAAS,IAAII,EAAKL,CAAC,EAAGK,EAAK,UAAWA,EAAK,QAAQ,GAI5DvB,EAAW,GAEPuB,EAAK,QAAU,GAIjByD,EAAIjC,EAAO7B,EAAGM,EAAE,IAAI,EAAG,EAAG,EAAG,CAAC,EAC9BwD,EAAE,GAAKxD,EAAE,GAETwD,EAAIjC,EAAO7B,EAAGM,EAAG,EAAGD,EAAK,OAAQ,CAAC,EAGpCyD,EAAIA,EAAE,MAAMxD,CAAC,EAEbxB,EAAW,GAEJkB,EAAE,MAAM8D,CAAC,EAClB,EASA/D,EAAE,mBAAqBA,EAAE,IAAM,UAAY,CACzC,OAAOgE,GAAmB,IAAI,CAChC,EAQAhE,EAAE,iBAAmBA,EAAE,GAAK,UAAY,CACtC,OAAOwD,EAAiB,IAAI,CAC9B,EAQAxD,EAAE,QAAUA,EAAE,IAAM,UAAY,CAC9B,IAAIC,EAAI,IAAI,KAAK,YAAY,IAAI,EACjC,OAAAA,EAAE,EAAI,CAACA,EAAE,EACFC,EAASD,CAAC,CACnB,EAwBAD,EAAE,KAAOA,EAAE,IAAM,SAAUO,EAAG,CAC5B,IAAI0D,EAAOjC,EAAG2B,EAAGnD,EAAGH,EAAG4B,EAAKjB,EAAIC,EAAIL,EAAIC,EACtCZ,EAAI,KACJK,EAAOL,EAAE,YAKX,GAHAM,EAAI,IAAID,EAAKC,CAAC,EAGV,CAACN,EAAE,GAAK,CAACM,EAAE,EAGb,MAAI,CAACN,EAAE,GAAK,CAACM,EAAE,EAAGA,EAAI,IAAID,EAAK,GAAG,EAMxBL,EAAE,IAAGM,EAAI,IAAID,EAAKC,EAAE,GAAKN,EAAE,IAAMM,EAAE,EAAIN,EAAI,GAAG,GAEjDM,EAIT,GAAIN,EAAE,GAAKM,EAAE,EACX,OAAAA,EAAE,EAAI,CAACA,EAAE,EACFN,EAAE,MAAMM,CAAC,EASlB,GANAK,EAAKX,EAAE,EACPY,EAAKN,EAAE,EACPS,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAGN,CAACM,EAAG,CAAC,GAAK,CAACC,EAAG,CAAC,EAIjB,OAAKA,EAAG,CAAC,IAAGN,EAAI,IAAID,EAAKL,CAAC,GAEnBlB,EAAWmB,EAASK,EAAGS,EAAIC,CAAE,EAAIV,EAa1C,GAPAF,EAAIhB,EAAUY,EAAE,EAAIL,CAAQ,EAC5B+D,EAAItE,EAAUkB,EAAE,EAAIX,CAAQ,EAE5BgB,EAAKA,EAAG,MAAM,EACdJ,EAAIH,EAAIsD,EAGJnD,EAAG,CAuBL,IArBIA,EAAI,GACNwB,EAAIpB,EACJJ,EAAI,CAACA,EACLyB,EAAMpB,EAAG,SAETmB,EAAInB,EACJ8C,EAAItD,EACJ4B,EAAMrB,EAAG,QAIXP,EAAI,KAAK,KAAKW,EAAKpB,CAAQ,EAC3BqC,EAAM5B,EAAI4B,EAAM5B,EAAI,EAAI4B,EAAM,EAE1BzB,EAAIyB,IACNzB,EAAIyB,EACJD,EAAE,OAAS,GAIbA,EAAE,QAAQ,EACHxB,KAAMwB,EAAE,KAAK,CAAC,EACrBA,EAAE,QAAQ,CACZ,CAcA,IAZAC,EAAMrB,EAAG,OACTJ,EAAIK,EAAG,OAGHoB,EAAMzB,EAAI,IACZA,EAAIyB,EACJD,EAAInB,EACJA,EAAKD,EACLA,EAAKoB,GAIFiC,EAAQ,EAAGzD,GACdyD,GAASrD,EAAG,EAAEJ,CAAC,EAAII,EAAGJ,CAAC,EAAIK,EAAGL,CAAC,EAAIyD,GAAStE,EAAO,EACnDiB,EAAGJ,CAAC,GAAKb,EAUX,IAPIsE,IACFrD,EAAG,QAAQqD,CAAK,EAChB,EAAEN,GAKC1B,EAAMrB,EAAG,OAAQA,EAAG,EAAEqB,CAAG,GAAK,GAAIrB,EAAG,IAAI,EAE9C,OAAAL,EAAE,EAAIK,EACNL,EAAE,EAAIuD,GAAkBlD,EAAI+C,CAAC,EAEtB5E,EAAWmB,EAASK,EAAGS,EAAIC,CAAE,EAAIV,CAC1C,EASAP,EAAE,UAAYA,EAAE,GAAK,SAAUkE,EAAG,CAChC,IAAI7D,EACFJ,EAAI,KAEN,GAAIiE,IAAM,QAAUA,IAAM,CAAC,CAACA,GAAKA,IAAM,GAAKA,IAAM,EAAG,MAAM,MAAMjF,EAAkBiF,CAAC,EAEpF,OAAIjE,EAAE,GACJI,EAAI8D,GAAalE,EAAE,CAAC,EAChBiE,GAAKjE,EAAE,EAAI,EAAII,IAAGA,EAAIJ,EAAE,EAAI,IAEhCI,EAAI,IAGCA,CACT,EAQAL,EAAE,MAAQ,UAAY,CACpB,IAAIC,EAAI,KACNK,EAAOL,EAAE,YAEX,OAAOC,EAAS,IAAII,EAAKL,CAAC,EAAGA,EAAE,EAAI,EAAGK,EAAK,QAAQ,CACrD,EAkBAN,EAAE,KAAOA,EAAE,IAAM,UAAY,CAC3B,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAKA,EAAE,SAAS,EACZA,EAAE,OAAO,EAAU,IAAIK,EAAKL,CAAC,GAEjCe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,KAAK,IAAIf,EAAE,EAAGA,EAAE,GAAG,CAAC,EAAIL,EAC9CU,EAAK,SAAW,EAEhBL,EAAImE,GAAK9D,EAAMa,GAAiBb,EAAML,CAAC,CAAC,EAExCK,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAETf,EAASpB,EAAW,EAAImB,EAAE,IAAI,EAAIA,EAAGe,EAAIC,EAAI,EAAI,GAb9B,IAAIX,EAAK,GAAG,CAcxC,EAeAN,EAAE,WAAaA,EAAE,KAAO,UAAY,CAClC,IAAIoB,EAAG,EAAGK,EAAIH,EAAGC,EAAKG,EACpBzB,EAAI,KACJ+B,EAAI/B,EAAE,EACN0D,EAAI1D,EAAE,EACNuB,EAAIvB,EAAE,EACNK,EAAOL,EAAE,YAGX,GAAIuB,IAAM,GAAK,CAACQ,GAAK,CAACA,EAAE,CAAC,EACvB,OAAO,IAAI1B,EAAK,CAACkB,GAAKA,EAAI,IAAM,CAACQ,GAAKA,EAAE,CAAC,GAAK,IAAMA,EAAI/B,EAAI,GAAK,EAgCnE,IA7BAlB,EAAW,GAGXyC,EAAI,KAAK,KAAK,CAACvB,CAAC,EAIZuB,GAAK,GAAKA,GAAK,KACjB,EAAIK,EAAeG,CAAC,GAEf,EAAE,OAAS2B,GAAK,GAAK,IAAG,GAAK,KAClCnC,EAAI,KAAK,KAAK,CAAC,EACfmC,EAAItE,GAAWsE,EAAI,GAAK,CAAC,GAAKA,EAAI,GAAKA,EAAI,GAEvCnC,GAAK,IACP,EAAI,KAAOmC,GAEX,EAAInC,EAAE,cAAc,EACpB,EAAI,EAAE,MAAM,EAAG,EAAE,QAAQ,GAAG,EAAI,CAAC,EAAImC,GAGvCrC,EAAI,IAAIhB,EAAK,CAAC,GAEdgB,EAAI,IAAIhB,EAAKkB,EAAE,SAAS,CAAC,EAG3BC,GAAMkC,EAAIrD,EAAK,WAAa,IAQ1B,GAJAoB,EAAIJ,EACJA,EAAII,EAAE,KAAKI,EAAO7B,EAAGyB,EAAGD,EAAK,EAAG,CAAC,CAAC,EAAE,MAAM,EAAG,EAGzCI,EAAeH,EAAE,CAAC,EAAE,MAAM,EAAGD,CAAE,KAAO,EAAII,EAAeP,EAAE,CAAC,GAAG,MAAM,EAAGG,CAAE,EAK5E,GAJA,EAAI,EAAE,MAAMA,EAAK,EAAGA,EAAK,CAAC,EAItB,GAAK,QAAU,CAACF,GAAO,GAAK,OAAQ,CAItC,GAAI,CAACA,IACHrB,EAASwB,EAAGiC,EAAI,EAAG,CAAC,EAEhBjC,EAAE,MAAMA,CAAC,EAAE,GAAGzB,CAAC,GAAG,CACpBqB,EAAII,EACJ,KACF,CAGFD,GAAM,EACNF,EAAM,CACR,KAAO,EAID,CAAC,CAAC,GAAK,CAAC,CAAC,EAAE,MAAM,CAAC,GAAK,EAAE,OAAO,CAAC,GAAK,OAGxCrB,EAASoB,EAAGqC,EAAI,EAAG,CAAC,EACpBvC,EAAI,CAACE,EAAE,MAAMA,CAAC,EAAE,GAAGrB,CAAC,GAGtB,KACF,CAIJ,OAAAlB,EAAW,GAEJmB,EAASoB,EAAGqC,EAAGrD,EAAK,SAAUc,CAAC,CACxC,EAgBApB,EAAE,QAAUA,EAAE,IAAM,UAAY,CAC9B,IAAIgB,EAAIC,EACNhB,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAKA,EAAE,SAAS,EACZA,EAAE,OAAO,EAAU,IAAIK,EAAKL,CAAC,GAEjCe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SACVA,EAAK,UAAYU,EAAK,GACtBV,EAAK,SAAW,EAEhBL,EAAIA,EAAE,IAAI,EACVA,EAAE,EAAI,EACNA,EAAI6B,EAAO7B,EAAG,IAAIK,EAAK,CAAC,EAAE,MAAML,EAAE,MAAMA,CAAC,CAAC,EAAE,KAAK,EAAGe,EAAK,GAAI,CAAC,EAE9DV,EAAK,UAAYU,EACjBV,EAAK,SAAWW,EAETf,EAASpB,GAAY,GAAKA,GAAY,EAAImB,EAAE,IAAI,EAAIA,EAAGe,EAAIC,EAAI,EAAI,GAfhD,IAAIX,EAAK,GAAG,CAgBxC,EAwBAN,EAAE,MAAQA,EAAE,IAAM,SAAUO,EAAG,CAC7B,IAAI0D,EAAON,EAAGnD,EAAGH,EAAG,EAAGgE,EAAI3C,EAAGhB,EAAKC,EACjCV,EAAI,KACJK,EAAOL,EAAE,YACTW,EAAKX,EAAE,EACPY,GAAMN,EAAI,IAAID,EAAKC,CAAC,GAAG,EAKzB,GAHAA,EAAE,GAAKN,EAAE,EAGL,CAACW,GAAM,CAACA,EAAG,CAAC,GAAK,CAACC,GAAM,CAACA,EAAG,CAAC,EAE/B,OAAO,IAAIP,EAAK,CAACC,EAAE,GAAKK,GAAM,CAACA,EAAG,CAAC,GAAK,CAACC,GAAMA,GAAM,CAACA,EAAG,CAAC,GAAK,CAACD,EAI5D,IAIA,CAACA,GAAM,CAACC,EAAKN,EAAE,EAAI,EAAIA,EAAE,EAAI,CAAC,EAoBpC,IAjBAoD,EAAItE,EAAUY,EAAE,EAAIL,CAAQ,EAAIP,EAAUkB,EAAE,EAAIX,CAAQ,EACxDc,EAAME,EAAG,OACTD,EAAME,EAAG,OAGLH,EAAMC,IACR,EAAIC,EACJA,EAAKC,EACLA,EAAK,EACLwD,EAAK3D,EACLA,EAAMC,EACNA,EAAM0D,GAIR,EAAI,CAAC,EACLA,EAAK3D,EAAMC,EACNH,EAAI6D,EAAI7D,KAAM,EAAE,KAAK,CAAC,EAG3B,IAAKA,EAAIG,EAAK,EAAEH,GAAK,GAAI,CAEvB,IADAyD,EAAQ,EACH5D,EAAIK,EAAMF,EAAGH,EAAIG,GACpBkB,EAAI,EAAErB,CAAC,EAAIQ,EAAGL,CAAC,EAAII,EAAGP,EAAIG,EAAI,CAAC,EAAIyD,EACnC,EAAE5D,GAAG,EAAIqB,EAAI/B,EAAO,EACpBsE,EAAQvC,EAAI/B,EAAO,EAGrB,EAAEU,CAAC,GAAK,EAAEA,CAAC,EAAI4D,GAAStE,EAAO,CACjC,CAGA,KAAO,CAAC,EAAE,EAAE0E,CAAE,GAAI,EAAE,IAAI,EAExB,OAAIJ,EAAO,EAAEN,EACR,EAAE,MAAM,EAEbpD,EAAE,EAAI,EACNA,EAAE,EAAIuD,GAAkB,EAAGH,CAAC,EAErB5E,EAAWmB,EAASK,EAAGD,EAAK,UAAWA,EAAK,QAAQ,EAAIC,CACjE,EAaAP,EAAE,SAAW,SAAUyB,EAAIR,EAAI,CAC7B,OAAOqD,GAAe,KAAM,EAAG7C,EAAIR,CAAE,CACvC,EAaAjB,EAAE,gBAAkBA,EAAE,KAAO,SAAUuE,EAAItD,EAAI,CAC7C,IAAIhB,EAAI,KACNK,EAAOL,EAAE,YAGX,OADAA,EAAI,IAAIK,EAAKL,CAAC,EACVsE,IAAO,OAAetE,GAE1BuE,EAAWD,EAAI,EAAG/F,CAAU,EAExByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,EAEjBf,EAASD,EAAGsE,EAAKtE,EAAE,EAAI,EAAGgB,CAAE,EACrC,EAWAjB,EAAE,cAAgB,SAAUuE,EAAItD,EAAI,CAClC,IAAIwD,EACFxE,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAIsE,IAAO,OACTE,EAAMC,EAAezE,EAAG,EAAI,GAE5BuE,EAAWD,EAAI,EAAG/F,CAAU,EAExByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,EAExBhB,EAAIC,EAAS,IAAII,EAAKL,CAAC,EAAGsE,EAAK,EAAGtD,CAAE,EACpCwD,EAAMC,EAAezE,EAAG,GAAMsE,EAAK,CAAC,GAG/BtE,EAAE,MAAM,GAAK,CAACA,EAAE,OAAO,EAAI,IAAMwE,EAAMA,CAChD,EAmBAzE,EAAE,QAAU,SAAUuE,EAAItD,EAAI,CAC5B,IAAIwD,EAAKlE,EACPN,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAIsE,IAAO,OACTE,EAAMC,EAAezE,CAAC,GAEtBuE,EAAWD,EAAI,EAAG/F,CAAU,EAExByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,EAExBV,EAAIL,EAAS,IAAII,EAAKL,CAAC,EAAGsE,EAAKtE,EAAE,EAAI,EAAGgB,CAAE,EAC1CwD,EAAMC,EAAenE,EAAG,GAAOgE,EAAKhE,EAAE,EAAI,CAAC,GAKtCN,EAAE,MAAM,GAAK,CAACA,EAAE,OAAO,EAAI,IAAMwE,EAAMA,CAChD,EAcAzE,EAAE,WAAa,SAAU2E,EAAM,CAC7B,IAAI3C,EAAG4C,EAAIC,EAAIC,EAAInB,EAAGtD,EAAGgB,EAAG0D,EAAIC,EAAIhE,EAAI+C,EAAGzC,EACzCrB,EAAI,KACJW,EAAKX,EAAE,EACPK,EAAOL,EAAE,YAEX,GAAI,CAACW,EAAI,OAAO,IAAIN,EAAKL,CAAC,EAU1B,GARA+E,EAAKJ,EAAK,IAAItE,EAAK,CAAC,EACpBuE,EAAKE,EAAK,IAAIzE,EAAK,CAAC,EAEpB0B,EAAI,IAAI1B,EAAKuE,CAAE,EACflB,EAAI3B,EAAE,EAAImC,GAAavD,CAAE,EAAIX,EAAE,EAAI,EACnCI,EAAIsD,EAAI/D,EACRoC,EAAE,EAAE,CAAC,EAAI1C,EAAQ,GAAIe,EAAI,EAAIT,EAAWS,EAAIA,CAAC,EAEzCsE,GAAQ,KAGVA,EAAOhB,EAAI,EAAI3B,EAAIgD,MACd,CAEL,GADA3D,EAAI,IAAIf,EAAKqE,CAAI,EACb,CAACtD,EAAE,MAAM,GAAKA,EAAE,GAAG2D,CAAE,EAAG,MAAM,MAAM/F,EAAkBoC,CAAC,EAC3DsD,EAAOtD,EAAE,GAAGW,CAAC,EAAK2B,EAAI,EAAI3B,EAAIgD,EAAM3D,CACtC,CAOA,IALAtC,EAAW,GACXsC,EAAI,IAAIf,EAAKuB,EAAejB,CAAE,CAAC,EAC/BI,EAAKV,EAAK,UACVA,EAAK,UAAYqD,EAAI/C,EAAG,OAAShB,EAAW,EAG1CmE,EAAIjC,EAAOT,EAAGW,EAAG,EAAG,EAAG,CAAC,EACxB8C,EAAKF,EAAG,KAAKb,EAAE,MAAMc,CAAE,CAAC,EACpBC,EAAG,IAAIH,CAAI,GAAK,GACpBC,EAAKC,EACLA,EAAKC,EACLA,EAAKE,EACLA,EAAKD,EAAG,KAAKhB,EAAE,MAAMe,CAAE,CAAC,EACxBC,EAAKD,EACLA,EAAK9C,EACLA,EAAIX,EAAE,MAAM0C,EAAE,MAAMe,CAAE,CAAC,EACvBzD,EAAIyD,EAGN,OAAAA,EAAKhD,EAAO6C,EAAK,MAAMC,CAAE,EAAGC,EAAI,EAAG,EAAG,CAAC,EACvCE,EAAKA,EAAG,KAAKD,EAAG,MAAME,CAAE,CAAC,EACzBJ,EAAKA,EAAG,KAAKE,EAAG,MAAMD,CAAE,CAAC,EACzBE,EAAG,EAAIC,EAAG,EAAI/E,EAAE,EAGhBqB,EAAIQ,EAAOkD,EAAIH,EAAIlB,EAAG,CAAC,EAAE,MAAM1D,CAAC,EAAE,IAAI,EAAE,IAAI6B,EAAOiD,EAAIH,EAAIjB,EAAG,CAAC,EAAE,MAAM1D,CAAC,EAAE,IAAI,CAAC,EAAI,EAC7E,CAAC+E,EAAIH,CAAE,EAAI,CAACE,EAAIH,CAAE,EAExBtE,EAAK,UAAYU,EACjBjC,EAAW,GAEJuC,CACT,EAaAtB,EAAE,cAAgBA,EAAE,MAAQ,SAAUyB,EAAIR,EAAI,CAC5C,OAAOqD,GAAe,KAAM,GAAI7C,EAAIR,CAAE,CACxC,EAmBAjB,EAAE,UAAY,SAAUO,EAAGU,EAAI,CAC7B,IAAIhB,EAAI,KACNK,EAAOL,EAAE,YAIX,GAFAA,EAAI,IAAIK,EAAKL,CAAC,EAEVM,GAAK,KAAM,CAGb,GAAI,CAACN,EAAE,EAAG,OAAOA,EAEjBM,EAAI,IAAID,EAAK,CAAC,EACdW,EAAKX,EAAK,QACZ,KAAO,CASL,GARAC,EAAI,IAAID,EAAKC,CAAC,EACVU,IAAO,OACTA,EAAKX,EAAK,SAEVkE,EAAWvD,EAAI,EAAG,CAAC,EAIjB,CAAChB,EAAE,EAAG,OAAOM,EAAE,EAAIN,EAAIM,EAG3B,GAAI,CAACA,EAAE,EACL,OAAIA,EAAE,IAAGA,EAAE,EAAIN,EAAE,GACVM,CAEX,CAGA,OAAIA,EAAE,EAAE,CAAC,GACPxB,EAAW,GACXkB,EAAI6B,EAAO7B,EAAGM,EAAG,EAAGU,EAAI,CAAC,EAAE,MAAMV,CAAC,EAClCxB,EAAW,GACXmB,EAASD,CAAC,IAIVM,EAAE,EAAIN,EAAE,EACRA,EAAIM,GAGCN,CACT,EAQAD,EAAE,SAAW,UAAY,CACvB,MAAO,CAAC,IACV,EAaAA,EAAE,QAAU,SAAUyB,EAAIR,EAAI,CAC5B,OAAOqD,GAAe,KAAM,EAAG7C,EAAIR,CAAE,CACvC,EA8CAjB,EAAE,QAAUA,EAAE,IAAM,SAAUO,EAAG,CAC/B,IAAIoD,EAAGtD,EAAGW,EAAIM,EAAGL,EAAIO,EACnBvB,EAAI,KACJK,EAAOL,EAAE,YACTgF,EAAK,EAAE1E,EAAI,IAAID,EAAKC,CAAC,GAGvB,GAAI,CAACN,EAAE,GAAK,CAACM,EAAE,GAAK,CAACN,EAAE,EAAE,CAAC,GAAK,CAACM,EAAE,EAAE,CAAC,EAAG,OAAO,IAAID,EAAKhB,EAAQ,CAACW,EAAGgF,CAAE,CAAC,EAIvE,GAFAhF,EAAI,IAAIK,EAAKL,CAAC,EAEVA,EAAE,GAAG,CAAC,EAAG,OAAOA,EAKpB,GAHAe,EAAKV,EAAK,UACVW,EAAKX,EAAK,SAENC,EAAE,GAAG,CAAC,EAAG,OAAOL,EAASD,EAAGe,EAAIC,CAAE,EAMtC,GAHA0C,EAAItE,EAAUkB,EAAE,EAAIX,CAAQ,EAGxB+D,GAAKpD,EAAE,EAAE,OAAS,IAAMF,EAAI4E,EAAK,EAAI,CAACA,EAAKA,IAAOpF,GACpD,OAAAyB,EAAI4D,GAAO5E,EAAML,EAAGI,EAAGW,CAAE,EAClBT,EAAE,EAAI,EAAI,IAAID,EAAK,CAAC,EAAE,IAAIgB,CAAC,EAAIpB,EAASoB,EAAGN,EAAIC,CAAE,EAM1D,GAHAO,EAAIvB,EAAE,EAGFuB,EAAI,EAAG,CAGT,GAAImC,EAAIpD,EAAE,EAAE,OAAS,EAAG,OAAO,IAAID,EAAK,GAAG,EAM3C,IAHKC,EAAE,EAAEoD,CAAC,EAAI,IAAM,IAAGnC,EAAI,GAGvBvB,EAAE,GAAK,GAAKA,EAAE,EAAE,CAAC,GAAK,GAAKA,EAAE,EAAE,QAAU,EAC3C,OAAAA,EAAE,EAAIuB,EACCvB,CAEX,CAcA,OARAI,EAAIf,EAAQ,CAACW,EAAGgF,CAAE,EAClBtB,EAAItD,GAAK,GAAK,CAAC,SAASA,CAAC,EACrBhB,EAAU4F,GAAM,KAAK,IAAI,KAAOpD,EAAe5B,EAAE,CAAC,CAAC,EAAI,KAAK,KAAOA,EAAE,EAAI,EAAE,EAC3E,IAAIK,EAAKD,EAAI,EAAE,EAAE,EAKjBsD,EAAIrD,EAAK,KAAO,GAAKqD,EAAIrD,EAAK,KAAO,EAAU,IAAIA,EAAKqD,EAAI,EAAInC,EAAI,EAAI,CAAC,GAE7EzC,EAAW,GACXuB,EAAK,SAAWL,EAAE,EAAI,EAMtBI,EAAI,KAAK,IAAI,IAAKsD,EAAI,IAAI,MAAM,EAGhCrC,EAAI0C,GAAmBzD,EAAE,MAAMiD,EAAiBvD,EAAGe,EAAKX,CAAC,CAAC,EAAGW,CAAE,EAG3DM,EAAE,IAGJA,EAAIpB,EAASoB,EAAGN,EAAK,EAAG,CAAC,EAIrB0C,EAAoBpC,EAAE,EAAGN,EAAIC,CAAE,IACjC0C,EAAI3C,EAAK,GAGTM,EAAIpB,EAAS8D,GAAmBzD,EAAE,MAAMiD,EAAiBvD,EAAG0D,EAAItD,CAAC,CAAC,EAAGsD,CAAC,EAAGA,EAAI,EAAG,CAAC,EAG7E,CAAC9B,EAAeP,EAAE,CAAC,EAAE,MAAMN,EAAK,EAAGA,EAAK,EAAE,EAAI,GAAK,OACrDM,EAAIpB,EAASoB,EAAGN,EAAK,EAAG,CAAC,KAK/BM,EAAE,EAAIE,EACNzC,EAAW,GACXuB,EAAK,SAAWW,EAETf,EAASoB,EAAGN,EAAIC,CAAE,EAC3B,EAcAjB,EAAE,YAAc,SAAUyB,EAAIR,EAAI,CAChC,IAAIwD,EACFxE,EAAI,KACJK,EAAOL,EAAE,YAEX,OAAIwB,IAAO,OACTgD,EAAMC,EAAezE,EAAGA,EAAE,GAAKK,EAAK,UAAYL,EAAE,GAAKK,EAAK,QAAQ,GAEpEkE,EAAW/C,EAAI,EAAGjD,CAAU,EAExByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,EAExBhB,EAAIC,EAAS,IAAII,EAAKL,CAAC,EAAGwB,EAAIR,CAAE,EAChCwD,EAAMC,EAAezE,EAAGwB,GAAMxB,EAAE,GAAKA,EAAE,GAAKK,EAAK,SAAUmB,CAAE,GAGxDxB,EAAE,MAAM,GAAK,CAACA,EAAE,OAAO,EAAI,IAAMwE,EAAMA,CAChD,EAiBAzE,EAAE,oBAAsBA,EAAE,KAAO,SAAUyB,EAAIR,EAAI,CACjD,IAAIhB,EAAI,KACNK,EAAOL,EAAE,YAEX,OAAIwB,IAAO,QACTA,EAAKnB,EAAK,UACVW,EAAKX,EAAK,WAEVkE,EAAW/C,EAAI,EAAGjD,CAAU,EAExByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,GAGnBf,EAAS,IAAII,EAAKL,CAAC,EAAGwB,EAAIR,CAAE,CACrC,EAUAjB,EAAE,SAAW,UAAY,CACvB,IAAIC,EAAI,KACNK,EAAOL,EAAE,YACTwE,EAAMC,EAAezE,EAAGA,EAAE,GAAKK,EAAK,UAAYL,EAAE,GAAKK,EAAK,QAAQ,EAEtE,OAAOL,EAAE,MAAM,GAAK,CAACA,EAAE,OAAO,EAAI,IAAMwE,EAAMA,CAChD,EAOAzE,EAAE,UAAYA,EAAE,MAAQ,UAAY,CAClC,OAAOE,EAAS,IAAI,KAAK,YAAY,IAAI,EAAG,KAAK,EAAI,EAAG,CAAC,CAC3D,EAQAF,EAAE,QAAUA,EAAE,OAAS,UAAY,CACjC,IAAIC,EAAI,KACNK,EAAOL,EAAE,YACTwE,EAAMC,EAAezE,EAAGA,EAAE,GAAKK,EAAK,UAAYL,EAAE,GAAKK,EAAK,QAAQ,EAEtE,OAAOL,EAAE,MAAM,EAAI,IAAMwE,EAAMA,CACjC,EAoDA,SAAS5C,EAAeG,EAAG,CACzB,IAAIxB,EAAGH,EAAG8E,EACRC,EAAkBpD,EAAE,OAAS,EAC7ByC,EAAM,GACN1C,EAAIC,EAAE,CAAC,EAET,GAAIoD,EAAkB,EAAG,CAEvB,IADAX,GAAO1C,EACFvB,EAAI,EAAGA,EAAI4E,EAAiB5E,IAC/B2E,EAAKnD,EAAExB,CAAC,EAAI,GACZH,EAAIT,EAAWuF,EAAG,OACd9E,IAAGoE,GAAOY,EAAchF,CAAC,GAC7BoE,GAAOU,EAGTpD,EAAIC,EAAExB,CAAC,EACP2E,EAAKpD,EAAI,GACT1B,EAAIT,EAAWuF,EAAG,OACd9E,IAAGoE,GAAOY,EAAchF,CAAC,EAC/B,SAAW0B,IAAM,EACf,MAAO,IAIT,KAAOA,EAAI,KAAO,GAAIA,GAAK,GAE3B,OAAO0C,EAAM1C,CACf,CAGA,SAASyC,EAAWhE,EAAGL,EAAKC,EAAK,CAC/B,GAAII,IAAM,CAAC,CAACA,GAAKA,EAAIL,GAAOK,EAAIJ,EAC9B,MAAM,MAAMnB,EAAkBuB,CAAC,CAEnC,CAQA,SAASkD,EAAoB1B,EAAGxB,EAAGS,EAAIqE,EAAW,CAChD,IAAIC,EAAIlF,EAAGiB,EAAGkE,EAGd,IAAKnF,EAAI2B,EAAE,CAAC,EAAG3B,GAAK,GAAIA,GAAK,GAAI,EAAEG,EAGnC,MAAI,EAAEA,EAAI,GACRA,GAAKZ,EACL2F,EAAK,IAELA,EAAK,KAAK,MAAM/E,EAAI,GAAKZ,CAAQ,EACjCY,GAAKZ,GAMPS,EAAIf,EAAQ,GAAIM,EAAWY,CAAC,EAC5BgF,EAAKxD,EAAEuD,CAAE,EAAIlF,EAAI,EAEbiF,GAAa,KACX9E,EAAI,GACFA,GAAK,EAAGgF,EAAKA,EAAK,IAAM,EACnBhF,GAAK,IAAGgF,EAAKA,EAAK,GAAK,GAChClE,EAAIL,EAAK,GAAKuE,GAAM,OAASvE,EAAK,GAAKuE,GAAM,OAASA,GAAM,KAASA,GAAM,GAE3ElE,GAAKL,EAAK,GAAKuE,EAAK,GAAKnF,GAAKY,EAAK,GAAKuE,EAAK,GAAKnF,EAAI,KACnD2B,EAAEuD,EAAK,CAAC,EAAIlF,EAAI,IAAM,IAAMf,EAAQ,GAAIkB,EAAI,CAAC,EAAI,IAC/CgF,GAAMnF,EAAI,GAAKmF,GAAM,KAAOxD,EAAEuD,EAAK,CAAC,EAAIlF,EAAI,IAAM,IAAM,EAG3DG,EAAI,GACFA,GAAK,EAAGgF,EAAKA,EAAK,IAAO,EACpBhF,GAAK,EAAGgF,EAAKA,EAAK,IAAM,EACxBhF,GAAK,IAAGgF,EAAKA,EAAK,GAAK,GAChClE,GAAKgE,GAAarE,EAAK,IAAMuE,GAAM,MAAQ,CAACF,GAAarE,EAAK,GAAKuE,GAAM,MAEzElE,IAAMgE,GAAarE,EAAK,IAAMuE,EAAK,GAAKnF,GACvC,CAACiF,GAAarE,EAAK,GAAMuE,EAAK,GAAKnF,EAAI,KACrC2B,EAAEuD,EAAK,CAAC,EAAIlF,EAAI,IAAO,IAAMf,EAAQ,GAAIkB,EAAI,CAAC,EAAI,EAIlDc,CACT,CAMA,SAASmE,GAAYhB,EAAKiB,EAAQC,EAAS,CAOzC,QANIlF,EACFmF,EAAM,CAAC,CAAC,EACRC,EACArF,EAAI,EACJsF,EAAOrB,EAAI,OAENjE,EAAIsF,GAAO,CAChB,IAAKD,EAAOD,EAAI,OAAQC,KAASD,EAAIC,CAAI,GAAKH,EAE9C,IADAE,EAAI,CAAC,GAAKnH,GAAS,QAAQgG,EAAI,OAAOjE,GAAG,CAAC,EACrCC,EAAI,EAAGA,EAAImF,EAAI,OAAQnF,IACtBmF,EAAInF,CAAC,EAAIkF,EAAU,IACjBC,EAAInF,EAAI,CAAC,IAAM,SAAQmF,EAAInF,EAAI,CAAC,EAAI,GACxCmF,EAAInF,EAAI,CAAC,GAAKmF,EAAInF,CAAC,EAAIkF,EAAU,EACjCC,EAAInF,CAAC,GAAKkF,EAGhB,CAEA,OAAOC,EAAI,QAAQ,CACrB,CAQA,SAAS1E,GAAOZ,EAAML,EAAG,CACvB,IAAII,EAAG4B,EAAK1B,EAEZ,GAAIN,EAAE,OAAO,EAAG,OAAOA,EAMvBgC,EAAMhC,EAAE,EAAE,OACNgC,EAAM,IACR5B,EAAI,KAAK,KAAK4B,EAAM,CAAC,EACrB1B,GAAK,EAAI4B,GAAQ,EAAG9B,CAAC,GAAG,SAAS,IAEjCA,EAAI,GACJE,EAAI,gCAGND,EAAK,WAAaD,EAElBJ,EAAImC,EAAa9B,EAAM,EAAGL,EAAE,MAAMM,CAAC,EAAG,IAAID,EAAK,CAAC,CAAC,EAGjD,QAASE,EAAIH,EAAGG,KAAM,CACpB,IAAIuF,EAAQ9F,EAAE,MAAMA,CAAC,EACrBA,EAAI8F,EAAM,MAAMA,CAAK,EAAE,MAAMA,CAAK,EAAE,MAAM,CAAC,EAAE,KAAK,CAAC,CACrD,CAEA,OAAAzF,EAAK,WAAaD,EAEXJ,CACT,CAMA,IAAI6B,EAAU,UAAY,CAGxB,SAASkE,EAAgB/F,EAAGI,EAAG4C,EAAM,CACnC,IAAIgD,EACFhC,EAAQ,EACRzD,EAAIP,EAAE,OAER,IAAKA,EAAIA,EAAE,MAAM,EAAGO,KAClByF,EAAOhG,EAAEO,CAAC,EAAIH,EAAI4D,EAClBhE,EAAEO,CAAC,EAAIyF,EAAOhD,EAAO,EACrBgB,EAAQgC,EAAOhD,EAAO,EAGxB,OAAIgB,GAAOhE,EAAE,QAAQgE,CAAK,EAEnBhE,CACT,CAEA,SAASiG,EAAQC,EAAGC,EAAGC,EAAIC,EAAI,CAC7B,IAAI9F,EAAGc,EAEP,GAAI+E,GAAMC,EACRhF,EAAI+E,EAAKC,EAAK,EAAI,OAElB,KAAK9F,EAAIc,EAAI,EAAGd,EAAI6F,EAAI7F,IACtB,GAAI2F,EAAE3F,CAAC,GAAK4F,EAAE5F,CAAC,EAAG,CAChBc,EAAI6E,EAAE3F,CAAC,EAAI4F,EAAE5F,CAAC,EAAI,EAAI,GACtB,KACF,CAIJ,OAAOc,CACT,CAEA,SAASiF,EAASJ,EAAGC,EAAGC,EAAIpD,EAAM,CAIhC,QAHIzC,EAAI,EAGD6F,KACLF,EAAEE,CAAE,GAAK7F,EACTA,EAAI2F,EAAEE,CAAE,EAAID,EAAEC,CAAE,EAAI,EAAI,EACxBF,EAAEE,CAAE,EAAI7F,EAAIyC,EAAOkD,EAAEE,CAAE,EAAID,EAAEC,CAAE,EAIjC,KAAO,CAACF,EAAE,CAAC,GAAKA,EAAE,OAAS,GAAIA,EAAE,MAAM,CACzC,CAEA,OAAO,SAAUlG,EAAGM,EAAGS,EAAIC,EAAIsD,EAAItB,EAAM,CACvC,IAAIuD,EAAK7C,EAAGnD,EAAGH,EAAGoG,EAASC,EAAMC,EAAMC,EAAO7C,EAAG8C,EAAIC,EAAKC,EAAMC,EAAMvF,EAAIC,GAAGuF,EAAIC,EAAIC,GACnFC,EAAIC,EACJ/G,GAAOL,EAAE,YACTqH,GAAOrH,EAAE,GAAKM,EAAE,EAAI,EAAI,GACxBK,EAAKX,EAAE,EACPY,EAAKN,EAAE,EAGT,GAAI,CAACK,GAAM,CAACA,EAAG,CAAC,GAAK,CAACC,GAAM,CAACA,EAAG,CAAC,EAE/B,OAAO,IAAIP,GACT,CAACL,EAAE,GAAK,CAACM,EAAE,IAAMK,EAAKC,GAAMD,EAAG,CAAC,GAAKC,EAAG,CAAC,EAAI,CAACA,GAAM,IAGpDD,GAAMA,EAAG,CAAC,GAAK,GAAK,CAACC,EAAKyG,GAAO,EAAIA,GAAO,CAAC,EAmBjD,IAhBIrE,GACFwD,EAAU,EACV9C,EAAI1D,EAAE,EAAIM,EAAE,IAEZ0C,EAAOtD,EACP8G,EAAU7G,EACV+D,EAAItE,EAAUY,EAAE,EAAIwG,CAAO,EAAIpH,EAAUkB,EAAE,EAAIkG,CAAO,GAGxDW,EAAKvG,EAAG,OACRqG,EAAKtG,EAAG,OACRmD,EAAI,IAAIzD,GAAKgH,EAAI,EACjBT,EAAK9C,EAAE,EAAI,CAAC,EAIPvD,EAAI,EAAGK,EAAGL,CAAC,IAAMI,EAAGJ,CAAC,GAAK,GAAIA,IAAI,CAavC,GAXIK,EAAGL,CAAC,GAAKI,EAAGJ,CAAC,GAAK,IAAImD,IAEtB3C,GAAM,MACRS,EAAKT,EAAKV,GAAK,UACfW,EAAKX,GAAK,UACDiE,EACT9C,EAAKT,GAAMf,EAAE,EAAIM,EAAE,GAAK,EAExBkB,EAAKT,EAGHS,EAAK,EACPoF,EAAG,KAAK,CAAC,EACTH,EAAO,OACF,CAOL,GAJAjF,EAAKA,EAAKgF,EAAU,EAAI,EACxBjG,EAAI,EAGA4G,GAAM,EAAG,CAMX,IALA/G,EAAI,EACJQ,EAAKA,EAAG,CAAC,EACTY,KAGQjB,EAAI0G,GAAM7G,IAAMoB,IAAMjB,IAC5BkB,GAAIrB,EAAI4C,GAAQrC,EAAGJ,CAAC,GAAK,GACzBqG,EAAGrG,CAAC,EAAIkB,GAAIb,EAAK,EACjBR,EAAIqB,GAAIb,EAAK,EAGf6F,EAAOrG,GAAKG,EAAI0G,CAGlB,KAAO,CAiBL,IAdA7G,EAAI4C,GAAQpC,EAAG,CAAC,EAAI,GAAK,EAErBR,EAAI,IACNQ,EAAKmF,EAAgBnF,EAAIR,EAAG4C,CAAI,EAChCrC,EAAKoF,EAAgBpF,EAAIP,EAAG4C,CAAI,EAChCmE,EAAKvG,EAAG,OACRqG,EAAKtG,EAAG,QAGVqG,EAAKG,EACLN,EAAMlG,EAAG,MAAM,EAAGwG,CAAE,EACpBL,EAAOD,EAAI,OAGJC,EAAOK,GAAKN,EAAIC,GAAM,EAAI,EAEjCM,EAAKxG,EAAG,MAAM,EACdwG,EAAG,QAAQ,CAAC,EACZF,GAAMtG,EAAG,CAAC,EAENA,EAAG,CAAC,GAAKoC,EAAO,GAAG,EAAEkE,GAEzB,GACE9G,EAAI,EAGJmG,EAAMN,EAAQrF,EAAIiG,EAAKM,EAAIL,CAAI,EAG3BP,EAAM,GAGRQ,EAAOF,EAAI,CAAC,EACRM,GAAML,IAAMC,EAAOA,EAAO/D,GAAQ6D,EAAI,CAAC,GAAK,IAGhDzG,EAAI2G,EAAOG,GAAM,EAUb9G,EAAI,GACFA,GAAK4C,IAAM5C,EAAI4C,EAAO,GAG1B0D,EAAOX,EAAgBnF,EAAIR,EAAG4C,CAAI,EAClC2D,EAAQD,EAAK,OACbI,EAAOD,EAAI,OAGXN,EAAMN,EAAQS,EAAMG,EAAKF,EAAOG,CAAI,EAGhCP,GAAO,IACTnG,IAGAkG,EAASI,EAAMS,EAAKR,EAAQS,EAAKxG,EAAI+F,EAAO3D,CAAI,KAO9C5C,GAAK,IAAGmG,EAAMnG,EAAI,GACtBsG,EAAO9F,EAAG,MAAM,GAGlB+F,EAAQD,EAAK,OACTC,EAAQG,GAAMJ,EAAK,QAAQ,CAAC,EAGhCJ,EAASO,EAAKH,EAAMI,EAAM9D,CAAI,EAG1BuD,GAAO,KACTO,EAAOD,EAAI,OAGXN,EAAMN,EAAQrF,EAAIiG,EAAKM,EAAIL,CAAI,EAG3BP,EAAM,IACRnG,IAGAkG,EAASO,EAAKM,EAAKL,EAAOM,EAAKxG,EAAIkG,EAAM9D,CAAI,IAIjD8D,EAAOD,EAAI,QACFN,IAAQ,IACjBnG,IACAyG,EAAM,CAAC,CAAC,GAIVD,EAAGrG,GAAG,EAAIH,EAGNmG,GAAOM,EAAI,CAAC,EACdA,EAAIC,GAAM,EAAInG,EAAGqG,CAAE,GAAK,GAExBH,EAAM,CAAClG,EAAGqG,CAAE,CAAC,EACbF,EAAO,UAGDE,IAAOC,GAAMJ,EAAI,CAAC,IAAM,SAAWrF,KAE7CiF,EAAOI,EAAI,CAAC,IAAM,MACpB,CAGKD,EAAG,CAAC,GAAGA,EAAG,MAAM,CACvB,CAGA,GAAIJ,GAAW,EACb1C,EAAE,EAAIJ,EACN9E,GAAU6H,MACL,CAGL,IAAKlG,EAAI,EAAGH,EAAIwG,EAAG,CAAC,EAAGxG,GAAK,GAAIA,GAAK,GAAIG,IACzCuD,EAAE,EAAIvD,EAAImD,EAAI8C,EAAU,EAExBvG,EAAS6D,EAAGQ,EAAKvD,EAAK+C,EAAE,EAAI,EAAI/C,EAAIC,EAAIyF,CAAI,CAC9C,CAEA,OAAO3C,CACT,CACF,EAAG,EAOF,SAAS7D,EAASD,EAAGwB,EAAIR,EAAIsG,EAAa,CACzC,IAAIC,EAAQhH,EAAGC,EAAGJ,EAAGmF,EAAIiC,EAAS1F,EAAGnB,EAAI8G,EACvCpH,EAAOL,EAAE,YAGX0H,EAAK,GAAIlG,GAAM,KAAM,CAInB,GAHAb,EAAKX,EAAE,EAGH,CAACW,EAAI,OAAOX,EAWhB,IAAKuH,EAAS,EAAGnH,EAAIO,EAAG,CAAC,EAAGP,GAAK,GAAIA,GAAK,GAAImH,IAI9C,GAHAhH,EAAIiB,EAAK+F,EAGLhH,EAAI,EACNA,GAAKZ,EACLa,EAAIgB,EACJM,EAAInB,EAAG8G,EAAM,CAAC,EAGdlC,EAAKzD,EAAIzC,EAAQ,GAAIkI,EAAS/G,EAAI,CAAC,EAAI,GAAK,UAE5CiH,EAAM,KAAK,MAAMlH,EAAI,GAAKZ,CAAQ,EAClCS,EAAIO,EAAG,OACH8G,GAAOrH,EACT,GAAIkH,EAAa,CAGf,KAAOlH,KAAOqH,GAAM9G,EAAG,KAAK,CAAC,EAC7BmB,EAAIyD,EAAK,EACTgC,EAAS,EACThH,GAAKZ,EACLa,EAAID,EAAIZ,EAAW,CACrB,KACE,OAAM+H,MAEH,CAIL,IAHA5F,EAAI1B,EAAIO,EAAG8G,CAAG,EAGTF,EAAS,EAAGnH,GAAK,GAAIA,GAAK,GAAImH,IAGnChH,GAAKZ,EAILa,EAAID,EAAIZ,EAAW4H,EAGnBhC,EAAK/E,EAAI,EAAI,EAAIsB,EAAIzC,EAAQ,GAAIkI,EAAS/G,EAAI,CAAC,EAAI,GAAK,CAC1D,CAmBF,GAfA8G,EAAcA,GAAe9F,EAAK,GAChCb,EAAG8G,EAAM,CAAC,IAAM,SAAWjH,EAAI,EAAIsB,EAAIA,EAAIzC,EAAQ,GAAIkI,EAAS/G,EAAI,CAAC,GAMvEgH,EAAUxG,EAAK,GACVuE,GAAM+B,KAAiBtG,GAAM,GAAKA,IAAOhB,EAAE,EAAI,EAAI,EAAI,IACxDuF,EAAK,GAAKA,GAAM,IAAMvE,GAAM,GAAKsG,GAAetG,GAAM,IAGpDT,EAAI,EAAIC,EAAI,EAAIsB,EAAIzC,EAAQ,GAAIkI,EAAS/G,CAAC,EAAI,EAAIG,EAAG8G,EAAM,CAAC,GAAK,GAAM,GACvEzG,IAAOhB,EAAE,EAAI,EAAI,EAAI,IAEvBwB,EAAK,GAAK,CAACb,EAAG,CAAC,EACjB,OAAAA,EAAG,OAAS,EACR6G,GAGFhG,GAAMxB,EAAE,EAAI,EAGZW,EAAG,CAAC,EAAItB,EAAQ,IAAKM,EAAW6B,EAAK7B,GAAYA,CAAQ,EACzDK,EAAE,EAAI,CAACwB,GAAM,GAIbb,EAAG,CAAC,EAAIX,EAAE,EAAI,EAGTA,EAiBT,GAbIO,GAAK,GACPI,EAAG,OAAS8G,EACZrH,EAAI,EACJqH,MAEA9G,EAAG,OAAS8G,EAAM,EAClBrH,EAAIf,EAAQ,GAAIM,EAAWY,CAAC,EAI5BI,EAAG8G,CAAG,EAAIjH,EAAI,GAAKsB,EAAIzC,EAAQ,GAAIkI,EAAS/G,CAAC,EAAInB,EAAQ,GAAImB,CAAC,EAAI,GAAKJ,EAAI,GAGzEoH,EACF,OAGE,GAAIC,GAAO,EAAG,CAGZ,IAAKlH,EAAI,EAAGC,EAAIG,EAAG,CAAC,EAAGH,GAAK,GAAIA,GAAK,GAAID,IAEzC,IADAC,EAAIG,EAAG,CAAC,GAAKP,EACRA,EAAI,EAAGI,GAAK,GAAIA,GAAK,GAAIJ,IAG1BG,GAAKH,IACPJ,EAAE,IACEW,EAAG,CAAC,GAAKjB,IAAMiB,EAAG,CAAC,EAAI,IAG7B,KACF,KAAO,CAEL,GADAA,EAAG8G,CAAG,GAAKrH,EACPO,EAAG8G,CAAG,GAAK/H,EAAM,MACrBiB,EAAG8G,GAAK,EAAI,EACZrH,EAAI,CACN,CAKJ,IAAKG,EAAII,EAAG,OAAQA,EAAG,EAAEJ,CAAC,IAAM,GAAII,EAAG,IAAI,CAC7C,CAEA,OAAI7B,IAGEkB,EAAE,EAAIK,EAAK,MAGbL,EAAE,EAAI,KACNA,EAAE,EAAI,KAGGA,EAAE,EAAIK,EAAK,OAGpBL,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,IAKLA,CACT,CAGA,SAASyE,EAAezE,EAAG2H,EAAOnG,EAAI,CACpC,GAAI,CAACxB,EAAE,SAAS,EAAG,OAAO4H,GAAkB5H,CAAC,EAC7C,IAAII,EACFsD,EAAI1D,EAAE,EACNwE,EAAM5C,EAAe5B,EAAE,CAAC,EACxBgC,EAAMwC,EAAI,OAEZ,OAAImD,GACEnG,IAAOpB,EAAIoB,EAAKQ,GAAO,EACzBwC,EAAMA,EAAI,OAAO,CAAC,EAAI,IAAMA,EAAI,MAAM,CAAC,EAAIY,EAAchF,CAAC,EACjD4B,EAAM,IACfwC,EAAMA,EAAI,OAAO,CAAC,EAAI,IAAMA,EAAI,MAAM,CAAC,GAGzCA,EAAMA,GAAOxE,EAAE,EAAI,EAAI,IAAM,MAAQA,EAAE,GAC9B0D,EAAI,GACbc,EAAM,KAAOY,EAAc,CAAC1B,EAAI,CAAC,EAAIc,EACjChD,IAAOpB,EAAIoB,EAAKQ,GAAO,IAAGwC,GAAOY,EAAchF,CAAC,IAC3CsD,GAAK1B,GACdwC,GAAOY,EAAc1B,EAAI,EAAI1B,CAAG,EAC5BR,IAAOpB,EAAIoB,EAAKkC,EAAI,GAAK,IAAGc,EAAMA,EAAM,IAAMY,EAAchF,CAAC,MAE5DA,EAAIsD,EAAI,GAAK1B,IAAKwC,EAAMA,EAAI,MAAM,EAAGpE,CAAC,EAAI,IAAMoE,EAAI,MAAMpE,CAAC,GAC5DoB,IAAOpB,EAAIoB,EAAKQ,GAAO,IACrB0B,EAAI,IAAM1B,IAAKwC,GAAO,KAC1BA,GAAOY,EAAchF,CAAC,IAInBoE,CACT,CAIA,SAASX,GAAkB0D,EAAQ7D,EAAG,CACpC,IAAI5B,EAAIyF,EAAO,CAAC,EAGhB,IAAM7D,GAAK/D,EAAUmC,GAAK,GAAIA,GAAK,GAAI4B,IACvC,OAAOA,CACT,CAGA,SAASF,GAAQnD,EAAMmB,EAAIT,EAAI,CAC7B,GAAIS,EAAK3B,GAGP,MAAAf,EAAW,GACPiC,IAAIV,EAAK,UAAYU,GACnB,MAAM9B,EAAsB,EAEpC,OAAOgB,EAAS,IAAII,EAAK5B,EAAI,EAAG+C,EAAI,EAAG,EAAI,CAC7C,CAGA,SAASkB,EAAMrC,EAAMmB,EAAIR,EAAI,CAC3B,GAAIQ,EAAK1B,GAAc,MAAM,MAAMb,EAAsB,EACzD,OAAOgB,EAAS,IAAII,EAAK3B,EAAE,EAAG8C,EAAIR,EAAI,EAAI,CAC5C,CAGA,SAASkD,GAAaqD,EAAQ,CAC5B,IAAIzF,EAAIyF,EAAO,OAAS,EACtBvF,EAAMF,EAAInC,EAAW,EAKvB,GAHAmC,EAAIyF,EAAOzF,CAAC,EAGRA,EAAG,CAGL,KAAOA,EAAI,IAAM,EAAGA,GAAK,GAAIE,IAG7B,IAAKF,EAAIyF,EAAO,CAAC,EAAGzF,GAAK,GAAIA,GAAK,GAAIE,GACxC,CAEA,OAAOA,CACT,CAGA,SAASoD,EAAchF,EAAG,CAExB,QADIyH,EAAK,GACFzH,KAAMyH,GAAM,IACnB,OAAOA,CACT,CAUA,SAAS5C,GAAO5E,EAAML,EAAGoB,EAAGL,EAAI,CAC9B,IAAIuG,EACF,EAAI,IAAIjH,EAAK,CAAC,EAIdD,EAAI,KAAK,KAAKW,EAAKpB,EAAW,CAAC,EAIjC,IAFAb,EAAW,KAEF,CAOP,GANIsC,EAAI,IACN,EAAI,EAAE,MAAMpB,CAAC,EACT8H,GAAS,EAAE,EAAG1H,CAAC,IAAGkH,EAAc,KAGtClG,EAAIhC,EAAUgC,EAAI,CAAC,EACfA,IAAM,EAAG,CAGXA,EAAI,EAAE,EAAE,OAAS,EACbkG,GAAe,EAAE,EAAElG,CAAC,IAAM,GAAG,EAAE,EAAE,EAAEA,CAAC,EACxC,KACF,CAEApB,EAAIA,EAAE,MAAMA,CAAC,EACb8H,GAAS9H,EAAE,EAAGI,CAAC,CACjB,CAEA,OAAAtB,EAAW,GAEJ,CACT,CAGA,SAASiJ,GAAM3G,EAAG,CAChB,OAAOA,EAAE,EAAEA,EAAE,EAAE,OAAS,CAAC,EAAI,CAC/B,CAMA,SAAS4G,GAAS3H,EAAM4H,EAAM7G,EAAG,CAK/B,QAJIhB,EAAGE,EACLN,EAAI,IAAIK,EAAK4H,EAAK,CAAC,CAAC,EACpB1H,EAAI,EAEC,EAAEA,EAAI0H,EAAK,QAAS,CAIzB,GAHA3H,EAAI,IAAID,EAAK4H,EAAK1H,CAAC,CAAC,EAGhB,CAACD,EAAE,EAAG,CACRN,EAAIM,EACJ,KACF,CAEAF,EAAIJ,EAAE,IAAIM,CAAC,GAEPF,IAAMgB,GAAKhB,IAAM,GAAKJ,EAAE,IAAMoB,KAChCpB,EAAIM,EAER,CAEA,OAAON,CACT,CAkCA,SAAS+D,GAAmB/D,EAAGwB,EAAI,CACjC,IAAI0B,EAAaI,EAAO9C,EAAG0H,EAAKC,EAAK1G,EAAGkB,EACtCrB,EAAM,EACNf,EAAI,EACJH,EAAI,EACJC,EAAOL,EAAE,YACTgB,EAAKX,EAAK,SACVU,EAAKV,EAAK,UAGZ,GAAI,CAACL,EAAE,GAAK,CAACA,EAAE,EAAE,CAAC,GAAKA,EAAE,EAAI,GAE3B,OAAO,IAAIK,EAAKL,EAAE,EACbA,EAAE,EAAE,CAAC,EAAQA,EAAE,EAAI,EAAI,EAAI,IAAlB,EACVA,EAAE,EAAIA,EAAE,EAAI,EAAI,EAAIA,EAAI,GAAK,EAanC,IAVIwB,GAAM,MACR1C,EAAW,GACX6D,EAAM5B,GAEN4B,EAAMnB,EAGRC,EAAI,IAAIpB,EAAK,MAAO,EAGbL,EAAE,EAAI,IAGXA,EAAIA,EAAE,MAAMyB,CAAC,EACbrB,GAAK,EAUP,IALAkD,EAAQ,KAAK,IAAIjE,EAAQ,EAAGe,CAAC,CAAC,EAAI,KAAK,KAAO,EAAI,EAAI,EACtDuC,GAAOW,EACPJ,EAAcgF,EAAMC,EAAM,IAAI9H,EAAK,CAAC,EACpCA,EAAK,UAAYsC,IAER,CAKP,GAJAuF,EAAMjI,EAASiI,EAAI,MAAMlI,CAAC,EAAG2C,EAAK,CAAC,EACnCO,EAAcA,EAAY,MAAM,EAAE3C,CAAC,EACnCkB,EAAI0G,EAAI,KAAKtG,EAAOqG,EAAKhF,EAAaP,EAAK,CAAC,CAAC,EAEzCf,EAAeH,EAAE,CAAC,EAAE,MAAM,EAAGkB,CAAG,IAAMf,EAAeuG,EAAI,CAAC,EAAE,MAAM,EAAGxF,CAAG,EAAG,CAE7E,IADAnC,EAAIJ,EACGI,KAAK2H,EAAMlI,EAASkI,EAAI,MAAMA,CAAG,EAAGxF,EAAK,CAAC,EAOjD,GAAInB,GAAM,KAER,GAAIF,EAAM,GAAKmC,EAAoB0E,EAAI,EAAGxF,EAAMW,EAAOtC,EAAIM,CAAG,EAC5DjB,EAAK,UAAYsC,GAAO,GACxBO,EAAcgF,EAAMzG,EAAI,IAAIpB,EAAK,CAAC,EAClCE,EAAI,EACJe,QAEA,QAAOrB,EAASkI,EAAK9H,EAAK,UAAYU,EAAIC,EAAIlC,EAAW,EAAI,MAG/D,QAAAuB,EAAK,UAAYU,EACVoH,CAEX,CAEAA,EAAM1G,CACR,CACF,CAkBA,SAAS8B,EAAiBjD,EAAGkB,EAAI,CAC/B,IAAI4G,EAAGC,EAAInF,EAAaQ,EAAG4E,EAAWhH,EAAK6G,EAAK1G,EAAGkB,EAAK4F,EAAIxF,EAC1D3B,EAAI,EACJkC,EAAQ,GACRtD,EAAIM,EACJK,EAAKX,EAAE,EACPK,EAAOL,EAAE,YACTgB,EAAKX,EAAK,SACVU,EAAKV,EAAK,UAGZ,GAAIL,EAAE,EAAI,GAAK,CAACW,GAAM,CAACA,EAAG,CAAC,GAAK,CAACX,EAAE,GAAKW,EAAG,CAAC,GAAK,GAAKA,EAAG,QAAU,EACjE,OAAO,IAAIN,EAAKM,GAAM,CAACA,EAAG,CAAC,EAAI,KAASX,EAAE,GAAK,EAAI,IAAMW,EAAK,EAAIX,CAAC,EAcrE,GAXIwB,GAAM,MACR1C,EAAW,GACX6D,EAAM5B,GAEN4B,EAAMnB,EAGRnB,EAAK,UAAYsC,GAAOW,EACxB8E,EAAIxG,EAAejB,CAAE,EACrB0H,EAAKD,EAAE,OAAO,CAAC,EAEX,KAAK,IAAI1E,EAAI1D,EAAE,CAAC,EAAI,MAAQ,CAa9B,KAAOqI,EAAK,GAAKA,GAAM,GAAKA,GAAM,GAAKD,EAAE,OAAO,CAAC,EAAI,GACnDpI,EAAIA,EAAE,MAAMM,CAAC,EACb8H,EAAIxG,EAAe5B,EAAE,CAAC,EACtBqI,EAAKD,EAAE,OAAO,CAAC,EACfhH,IAGFsC,EAAI1D,EAAE,EAEFqI,EAAK,GACPrI,EAAI,IAAIK,EAAK,KAAO+H,CAAC,EACrB1E,KAEA1D,EAAI,IAAIK,EAAKgI,EAAK,IAAMD,EAAE,MAAM,CAAC,CAAC,CAEtC,KAKE,QAAA3G,EAAI+B,GAAQnD,EAAMsC,EAAM,EAAG5B,CAAE,EAAE,MAAM2C,EAAI,EAAE,EAC3C1D,EAAIuD,EAAiB,IAAIlD,EAAKgI,EAAK,IAAMD,EAAE,MAAM,CAAC,CAAC,EAAGzF,EAAMW,CAAK,EAAE,KAAK7B,CAAC,EACzEpB,EAAK,UAAYU,EAEVS,GAAM,KAAOvB,EAASD,EAAGe,EAAIC,EAAIlC,EAAW,EAAI,EAAIkB,EAa7D,IATAuI,EAAKvI,EAKLmI,EAAMG,EAAYtI,EAAI6B,EAAO7B,EAAE,MAAM,CAAC,EAAGA,EAAE,KAAK,CAAC,EAAG2C,EAAK,CAAC,EAC1DI,EAAK9C,EAASD,EAAE,MAAMA,CAAC,EAAG2C,EAAK,CAAC,EAChCO,EAAc,IAEL,CAIP,GAHAoF,EAAYrI,EAASqI,EAAU,MAAMvF,CAAE,EAAGJ,EAAK,CAAC,EAChDlB,EAAI0G,EAAI,KAAKtG,EAAOyG,EAAW,IAAIjI,EAAK6C,CAAW,EAAGP,EAAK,CAAC,CAAC,EAEzDf,EAAeH,EAAE,CAAC,EAAE,MAAM,EAAGkB,CAAG,IAAMf,EAAeuG,EAAI,CAAC,EAAE,MAAM,EAAGxF,CAAG,EAc1E,GAbAwF,EAAMA,EAAI,MAAM,CAAC,EAIbzE,IAAM,IAAGyE,EAAMA,EAAI,KAAK3E,GAAQnD,EAAMsC,EAAM,EAAG5B,CAAE,EAAE,MAAM2C,EAAI,EAAE,CAAC,GACpEyE,EAAMtG,EAAOsG,EAAK,IAAI9H,EAAKe,CAAC,EAAGuB,EAAK,CAAC,EAQjCnB,GAAM,KACR,GAAIiC,EAAoB0E,EAAI,EAAGxF,EAAMW,EAAOtC,EAAIM,CAAG,EACjDjB,EAAK,UAAYsC,GAAOW,EACxB7B,EAAI6G,EAAYtI,EAAI6B,EAAO0G,EAAG,MAAM,CAAC,EAAGA,EAAG,KAAK,CAAC,EAAG5F,EAAK,CAAC,EAC1DI,EAAK9C,EAASD,EAAE,MAAMA,CAAC,EAAG2C,EAAK,CAAC,EAChCO,EAAc5B,EAAM,MAEpB,QAAOrB,EAASkI,EAAK9H,EAAK,UAAYU,EAAIC,EAAIlC,EAAW,EAAI,MAG/D,QAAAuB,EAAK,UAAYU,EACVoH,EAIXA,EAAM1G,EACNyB,GAAe,CACjB,CACF,CAIA,SAAS0E,GAAkB5H,EAAG,CAE5B,OAAO,OAAOA,EAAE,EAAIA,EAAE,EAAI,CAAC,CAC7B,CAMA,SAASwI,GAAaxI,EAAGwE,EAAK,CAC5B,IAAId,EAAGnD,EAAGyB,EAoBV,KAhBK0B,EAAIc,EAAI,QAAQ,GAAG,GAAK,KAAIA,EAAMA,EAAI,QAAQ,IAAK,EAAE,IAGrDjE,EAAIiE,EAAI,OAAO,IAAI,GAAK,GAGvBd,EAAI,IAAGA,EAAInD,GACfmD,GAAK,CAACc,EAAI,MAAMjE,EAAI,CAAC,EACrBiE,EAAMA,EAAI,UAAU,EAAGjE,CAAC,GACfmD,EAAI,IAGbA,EAAIc,EAAI,QAILjE,EAAI,EAAGiE,EAAI,WAAWjE,CAAC,IAAM,GAAIA,IAAI,CAG1C,IAAKyB,EAAMwC,EAAI,OAAQA,EAAI,WAAWxC,EAAM,CAAC,IAAM,GAAI,EAAEA,EAAI,CAG7D,GAFAwC,EAAMA,EAAI,MAAMjE,EAAGyB,CAAG,EAElBwC,EAAK,CAYP,GAXAxC,GAAOzB,EACPP,EAAE,EAAI0D,EAAIA,EAAInD,EAAI,EAClBP,EAAE,EAAI,CAAC,EAMPO,GAAKmD,EAAI,GAAK/D,EACV+D,EAAI,IAAGnD,GAAKZ,GAEZY,EAAIyB,EAAK,CAEX,IADIzB,GAAGP,EAAE,EAAE,KAAK,CAACwE,EAAI,MAAM,EAAGjE,CAAC,CAAC,EAC3ByB,GAAOrC,EAAUY,EAAIyB,GAAMhC,EAAE,EAAE,KAAK,CAACwE,EAAI,MAAMjE,EAAGA,GAAKZ,CAAQ,CAAC,EACrE6E,EAAMA,EAAI,MAAMjE,CAAC,EACjBA,EAAIZ,EAAW6E,EAAI,MACrB,MACEjE,GAAKyB,EAGP,KAAOzB,KAAMiE,GAAO,IACpBxE,EAAE,EAAE,KAAK,CAACwE,CAAG,EAET1F,IAGEkB,EAAE,EAAIA,EAAE,YAAY,MAGtBA,EAAE,EAAI,KACNA,EAAE,EAAI,KAGGA,EAAE,EAAIA,EAAE,YAAY,OAG7BA,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,GAId,MAGEA,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,EAGV,OAAOA,CACT,CAMA,SAASyI,GAAWzI,EAAGwE,EAAK,CAC1B,IAAIxB,EAAM3C,EAAMqI,EAASnI,EAAGoI,EAAS3G,EAAK4G,EAAGjI,EAAIgD,EAEjD,GAAIa,EAAI,QAAQ,GAAG,EAAI,IAErB,GADAA,EAAMA,EAAI,QAAQ,eAAgB,IAAI,EAClC/E,GAAU,KAAK+E,CAAG,EAAG,OAAOgE,GAAaxI,EAAGwE,CAAG,UAC1CA,IAAQ,YAAcA,IAAQ,MACvC,MAAK,CAACA,IAAKxE,EAAE,EAAI,KACjBA,EAAE,EAAI,IACNA,EAAE,EAAI,KACCA,EAGT,GAAIT,GAAM,KAAKiF,CAAG,EAChBxB,EAAO,GACPwB,EAAMA,EAAI,YAAY,UACblF,GAAS,KAAKkF,CAAG,EAC1BxB,EAAO,UACExD,GAAQ,KAAKgF,CAAG,EACzBxB,EAAO,MAEP,OAAM,MAAMhE,EAAkBwF,CAAG,EAgCnC,IA5BAjE,EAAIiE,EAAI,OAAO,IAAI,EAEfjE,EAAI,GACNqI,EAAI,CAACpE,EAAI,MAAMjE,EAAI,CAAC,EACpBiE,EAAMA,EAAI,UAAU,EAAGjE,CAAC,GAExBiE,EAAMA,EAAI,MAAM,CAAC,EAKnBjE,EAAIiE,EAAI,QAAQ,GAAG,EACnBmE,EAAUpI,GAAK,EACfF,EAAOL,EAAE,YAEL2I,IACFnE,EAAMA,EAAI,QAAQ,IAAK,EAAE,EACzBxC,EAAMwC,EAAI,OACVjE,EAAIyB,EAAMzB,EAGVmI,EAAUzD,GAAO5E,EAAM,IAAIA,EAAK2C,CAAI,EAAGzC,EAAGA,EAAI,CAAC,GAGjDI,EAAK6E,GAAYhB,EAAKxB,EAAMtD,CAAI,EAChCiE,EAAKhD,EAAG,OAAS,EAGZJ,EAAIoD,EAAIhD,EAAGJ,CAAC,IAAM,EAAG,EAAEA,EAAGI,EAAG,IAAI,EACtC,OAAIJ,EAAI,EAAU,IAAIF,EAAKL,EAAE,EAAI,CAAC,GAClCA,EAAE,EAAI6D,GAAkBlD,EAAIgD,CAAE,EAC9B3D,EAAE,EAAIW,EACN7B,EAAW,GAQP6J,IAAS3I,EAAI6B,EAAO7B,EAAG0I,EAAS1G,EAAM,CAAC,GAGvC4G,IAAG5I,EAAIA,EAAE,MAAM,KAAK,IAAI4I,CAAC,EAAI,GAAKvJ,EAAQ,EAAGuJ,CAAC,EAAIC,EAAQ,IAAI,EAAGD,CAAC,CAAC,GACvE9J,EAAW,GAEJkB,EACT,CAQA,SAASmE,GAAK9D,EAAML,EAAG,CACrB,IAAII,EACF4B,EAAMhC,EAAE,EAAE,OAEZ,GAAIgC,EAAM,EACR,OAAOhC,EAAE,OAAO,EAAIA,EAAImC,EAAa9B,EAAM,EAAGL,EAAGA,CAAC,EAQpDI,EAAI,IAAM,KAAK,KAAK4B,CAAG,EACvB5B,EAAIA,EAAI,GAAK,GAAKA,EAAI,EAEtBJ,EAAIA,EAAE,MAAM,EAAIkC,GAAQ,EAAG9B,CAAC,CAAC,EAC7BJ,EAAImC,EAAa9B,EAAM,EAAGL,EAAGA,CAAC,EAO9B,QAJI8I,EACFvG,EAAK,IAAIlC,EAAK,CAAC,EACfmC,EAAM,IAAInC,EAAK,EAAE,EACjBoC,EAAM,IAAIpC,EAAK,EAAE,EACZD,KACL0I,EAAS9I,EAAE,MAAMA,CAAC,EAClBA,EAAIA,EAAE,MAAMuC,EAAG,KAAKuG,EAAO,MAAMtG,EAAI,MAAMsG,CAAM,EAAE,MAAMrG,CAAG,CAAC,CAAC,CAAC,EAGjE,OAAOzC,CACT,CAIA,SAASmC,EAAa9B,EAAM,EAAGL,EAAGM,EAAGyI,EAAc,CACjD,IAAIvI,EAAGiB,EAAG,EAAGsB,EACXxC,EAAI,EACJQ,EAAKV,EAAK,UACVD,EAAI,KAAK,KAAKW,EAAKpB,CAAQ,EAM7B,IAJAb,EAAW,GACXiE,EAAK/C,EAAE,MAAMA,CAAC,EACd,EAAI,IAAIK,EAAKC,CAAC,IAEL,CAMP,GALAmB,EAAII,EAAO,EAAE,MAAMkB,CAAE,EAAG,IAAI1C,EAAK,IAAM,GAAG,EAAGU,EAAI,CAAC,EAClD,EAAIgI,EAAezI,EAAE,KAAKmB,CAAC,EAAInB,EAAE,MAAMmB,CAAC,EACxCnB,EAAIuB,EAAOJ,EAAE,MAAMsB,CAAE,EAAG,IAAI1C,EAAK,IAAM,GAAG,EAAGU,EAAI,CAAC,EAClDU,EAAI,EAAE,KAAKnB,CAAC,EAERmB,EAAE,EAAErB,CAAC,IAAM,OAAQ,CACrB,IAAKI,EAAIJ,EAAGqB,EAAE,EAAEjB,CAAC,IAAM,EAAE,EAAEA,CAAC,GAAKA,KAAK,CACtC,GAAIA,GAAK,GAAI,KACf,CAEAA,EAAI,EACJ,EAAIF,EACJA,EAAImB,EACJA,EAAIjB,EACJD,GACF,CAEA,OAAAzB,EAAW,GACX2C,EAAE,EAAE,OAASrB,EAAI,EAEVqB,CACT,CAIA,SAASS,GAAQiE,EAAGzC,EAAG,CAErB,QADItC,EAAI+E,EACD,EAAEzC,GAAGtC,GAAK+E,EACjB,OAAO/E,CACT,CAIA,SAASF,GAAiBb,EAAML,EAAG,CACjC,IAAIyB,EACFuH,EAAQhJ,EAAE,EAAI,EACdiJ,EAAKvG,EAAMrC,EAAMA,EAAK,UAAW,CAAC,EAClCwC,EAASoG,EAAG,MAAM,EAAG,EAIvB,GAFAjJ,EAAIA,EAAE,IAAI,EAENA,EAAE,IAAI6C,CAAM,EACd,OAAAhE,EAAWmK,EAAQ,EAAI,EAChBhJ,EAKT,GAFAyB,EAAIzB,EAAE,SAASiJ,CAAE,EAEbxH,EAAE,OAAO,EACX5C,EAAWmK,EAAQ,EAAI,MAClB,CAIL,GAHAhJ,EAAIA,EAAE,MAAMyB,EAAE,MAAMwH,CAAE,CAAC,EAGnBjJ,EAAE,IAAI6C,CAAM,EACd,OAAAhE,EAAWkJ,GAAMtG,CAAC,EAAKuH,EAAQ,EAAI,EAAMA,EAAQ,EAAI,EAC9ChJ,EAGTnB,EAAWkJ,GAAMtG,CAAC,EAAKuH,EAAQ,EAAI,EAAMA,EAAQ,EAAI,CACvD,CAEA,OAAOhJ,EAAE,MAAMiJ,CAAE,EAAE,IAAI,CACzB,CAQA,SAAS5E,GAAerE,EAAG0F,EAASlE,EAAIR,EAAI,CAC1C,IAAIgC,EAAMU,EAAGnD,EAAGH,EAAG4B,EAAKwF,EAAShD,EAAK7D,EAAIL,EACxCD,EAAOL,EAAE,YACT2H,EAAQnG,IAAO,OAWjB,GATImG,GACFpD,EAAW/C,EAAI,EAAGjD,CAAU,EACxByC,IAAO,OAAQA,EAAKX,EAAK,SACxBkE,EAAWvD,EAAI,EAAG,CAAC,IAExBQ,EAAKnB,EAAK,UACVW,EAAKX,EAAK,UAGR,CAACL,EAAE,SAAS,EACdwE,EAAMoD,GAAkB5H,CAAC,MACpB,CAoCL,IAnCAwE,EAAMC,EAAezE,CAAC,EACtBO,EAAIiE,EAAI,QAAQ,GAAG,EAOfmD,GACF3E,EAAO,EACH0C,GAAW,GACblE,EAAKA,EAAK,EAAI,EACLkE,GAAW,IACpBlE,EAAKA,EAAK,EAAI,IAGhBwB,EAAO0C,EAOLnF,GAAK,IACPiE,EAAMA,EAAI,QAAQ,IAAK,EAAE,EACzBlE,EAAI,IAAID,EAAK,CAAC,EACdC,EAAE,EAAIkE,EAAI,OAASjE,EACnBD,EAAE,EAAIkF,GAAYf,EAAenE,CAAC,EAAG,GAAI0C,CAAI,EAC7C1C,EAAE,EAAIA,EAAE,EAAE,QAGZK,EAAK6E,GAAYhB,EAAK,GAAIxB,CAAI,EAC9BU,EAAI1B,EAAMrB,EAAG,OAGNA,EAAG,EAAEqB,CAAG,GAAK,GAAIrB,EAAG,IAAI,EAE/B,GAAI,CAACA,EAAG,CAAC,EACP6D,EAAMmD,EAAQ,OAAS,QAClB,CAyBL,GAxBIpH,EAAI,EACNmD,KAEA1D,EAAI,IAAIK,EAAKL,CAAC,EACdA,EAAE,EAAIW,EACNX,EAAE,EAAI0D,EACN1D,EAAI6B,EAAO7B,EAAGM,EAAGkB,EAAIR,EAAI,EAAGgC,CAAI,EAChCrC,EAAKX,EAAE,EACP0D,EAAI1D,EAAE,EACNwH,EAAU5I,IAIZ2B,EAAII,EAAGa,CAAE,EACTpB,EAAI4C,EAAO,EACXwE,EAAUA,GAAW7G,EAAGa,EAAK,CAAC,IAAM,OAEpCgG,EAAUxG,EAAK,GACVT,IAAM,QAAUiH,KAAaxG,IAAO,GAAKA,KAAQhB,EAAE,EAAI,EAAI,EAAI,IAChEO,EAAIH,GAAKG,IAAMH,IAAMY,IAAO,GAAKwG,GAAWxG,IAAO,GAAKL,EAAGa,EAAK,CAAC,EAAI,GACrER,KAAQhB,EAAE,EAAI,EAAI,EAAI,IAE1BW,EAAG,OAASa,EAERgG,EAGF,KAAO,EAAE7G,EAAG,EAAEa,CAAE,EAAIwB,EAAO,GACzBrC,EAAGa,CAAE,EAAI,EACJA,IACH,EAAEkC,EACF/C,EAAG,QAAQ,CAAC,GAMlB,IAAKqB,EAAMrB,EAAG,OAAQ,CAACA,EAAGqB,EAAM,CAAC,EAAG,EAAEA,EAAI,CAG1C,IAAKzB,EAAI,EAAGiE,EAAM,GAAIjE,EAAIyB,EAAKzB,IAAKiE,GAAOhG,GAAS,OAAOmC,EAAGJ,CAAC,CAAC,EAGhE,GAAIoH,EAAO,CACT,GAAI3F,EAAM,EACR,GAAI0D,GAAW,IAAMA,GAAW,EAAG,CAEjC,IADAnF,EAAImF,GAAW,GAAK,EAAI,EACnB,EAAE1D,EAAKA,EAAMzB,EAAGyB,IAAOwC,GAAO,IAEnC,IADA7D,EAAK6E,GAAYhB,EAAKxB,EAAM0C,CAAO,EAC9B1D,EAAMrB,EAAG,OAAQ,CAACA,EAAGqB,EAAM,CAAC,EAAG,EAAEA,EAAI,CAG1C,IAAKzB,EAAI,EAAGiE,EAAM,KAAMjE,EAAIyB,EAAKzB,IAAKiE,GAAOhG,GAAS,OAAOmC,EAAGJ,CAAC,CAAC,CACpE,MACEiE,EAAMA,EAAI,OAAO,CAAC,EAAI,IAAMA,EAAI,MAAM,CAAC,EAI3CA,EAAOA,GAAOd,EAAI,EAAI,IAAM,MAAQA,CACtC,SAAWA,EAAI,EAAG,CAChB,KAAO,EAAEA,GAAIc,EAAM,IAAMA,EACzBA,EAAM,KAAOA,CACf,SACM,EAAEd,EAAI1B,EAAK,IAAK0B,GAAK1B,EAAK0B,KAAOc,GAAO,SACnCd,EAAI1B,IAAKwC,EAAMA,EAAI,MAAM,EAAGd,CAAC,EAAI,IAAMc,EAAI,MAAMd,CAAC,EAE/D,CAEAc,GAAOkB,GAAW,GAAK,KAAOA,GAAW,EAAI,KAAOA,GAAW,EAAI,KAAO,IAAMlB,CAClF,CAEA,OAAOxE,EAAE,EAAI,EAAI,IAAMwE,EAAMA,CAC/B,CAIA,SAASsD,GAASnC,EAAK3D,EAAK,CAC1B,GAAI2D,EAAI,OAAS3D,EACf,OAAA2D,EAAI,OAAS3D,EACN,EAEX,CAyDA,SAASkH,GAAIlJ,EAAG,CACd,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CACzB,CASA,SAASmJ,GAAKnJ,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAUA,SAASoJ,GAAMpJ,EAAG,CAChB,OAAO,IAAI,KAAKA,CAAC,EAAE,MAAM,CAC3B,CAWA,SAASqJ,GAAIrJ,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,KAAKM,CAAC,CAC3B,CAUA,SAASgJ,GAAKtJ,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAUA,SAASuJ,GAAMvJ,EAAG,CAChB,OAAO,IAAI,KAAKA,CAAC,EAAE,MAAM,CAC3B,CAUA,SAASwJ,GAAKxJ,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAUA,SAASyJ,GAAMzJ,EAAG,CAChB,OAAO,IAAI,KAAKA,CAAC,EAAE,MAAM,CAC3B,CA4BA,SAAS0J,GAAMpJ,EAAGN,EAAG,CACnBM,EAAI,IAAI,KAAKA,CAAC,EACdN,EAAI,IAAI,KAAKA,CAAC,EACd,IAAIqB,EACFN,EAAK,KAAK,UACVC,EAAK,KAAK,SACV2B,EAAM5B,EAAK,EAGb,MAAI,CAACT,EAAE,GAAK,CAACN,EAAE,EACbqB,EAAI,IAAI,KAAK,GAAG,EAGP,CAACf,EAAE,GAAK,CAACN,EAAE,GACpBqB,EAAIqB,EAAM,KAAMC,EAAK,CAAC,EAAE,MAAM3C,EAAE,EAAI,EAAI,IAAO,GAAI,EACnDqB,EAAE,EAAIf,EAAE,GAGC,CAACN,EAAE,GAAKM,EAAE,OAAO,GAC1Be,EAAIrB,EAAE,EAAI,EAAI0C,EAAM,KAAM3B,EAAIC,CAAE,EAAI,IAAI,KAAK,CAAC,EAC9CK,EAAE,EAAIf,EAAE,GAGC,CAACA,EAAE,GAAKN,EAAE,OAAO,GAC1BqB,EAAIqB,EAAM,KAAMC,EAAK,CAAC,EAAE,MAAM,EAAG,EACjCtB,EAAE,EAAIf,EAAE,GAGCN,EAAE,EAAI,GACf,KAAK,UAAY2C,EACjB,KAAK,SAAW,EAChBtB,EAAI,KAAK,KAAKQ,EAAOvB,EAAGN,EAAG2C,EAAK,CAAC,CAAC,EAClC3C,EAAI0C,EAAM,KAAMC,EAAK,CAAC,EACtB,KAAK,UAAY5B,EACjB,KAAK,SAAWC,EAChBK,EAAIf,EAAE,EAAI,EAAIe,EAAE,MAAMrB,CAAC,EAAIqB,EAAE,KAAKrB,CAAC,GAEnCqB,EAAI,KAAK,KAAKQ,EAAOvB,EAAGN,EAAG2C,EAAK,CAAC,CAAC,EAG7BtB,CACT,CAUA,SAASsI,GAAK3J,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CASA,SAAS4J,GAAK5J,EAAG,CACf,OAAOC,EAASD,EAAI,IAAI,KAAKA,CAAC,EAAGA,EAAE,EAAI,EAAG,CAAC,CAC7C,CAWA,SAAS6J,GAAM7J,EAAGE,EAAKC,EAAK,CAC1B,OAAO,IAAI,KAAKH,CAAC,EAAE,MAAME,EAAKC,CAAG,CACnC,CAqBA,SAAS2J,GAAOC,EAAK,CACnB,GAAI,CAACA,GAAO,OAAOA,GAAQ,SAAU,MAAM,MAAMhL,GAAe,iBAAiB,EACjF,IAAIwB,EAAGqI,EAAGoB,EACRC,EAAcF,EAAI,WAAa,GAC/BG,EAAK,CACH,YAAa,EAAG3L,EAChB,WAAY,EAAG,EACf,WAAY,CAACD,EAAW,EACxB,WAAY,EAAGA,EACf,OAAQ,EAAGA,EACX,OAAQ,CAACA,EAAW,EACpB,SAAU,EAAG,CACf,EAEF,IAAKiC,EAAI,EAAGA,EAAI2J,EAAG,OAAQ3J,GAAK,EAE9B,GADIqI,EAAIsB,EAAG3J,CAAC,EAAG0J,IAAa,KAAKrB,CAAC,EAAIjK,GAASiK,CAAC,IAC3CoB,EAAID,EAAInB,CAAC,KAAO,OACnB,GAAIxJ,EAAU4K,CAAC,IAAMA,GAAKA,GAAKE,EAAG3J,EAAI,CAAC,GAAKyJ,GAAKE,EAAG3J,EAAI,CAAC,EAAG,KAAKqI,CAAC,EAAIoB,MACjE,OAAM,MAAMhL,EAAkB4J,EAAI,KAAOoB,CAAC,EAKnD,GADIpB,EAAI,SAAUqB,IAAa,KAAKrB,CAAC,EAAIjK,GAASiK,CAAC,IAC9CoB,EAAID,EAAInB,CAAC,KAAO,OACnB,GAAIoB,IAAM,IAAQA,IAAM,IAASA,IAAM,GAAKA,IAAM,EAChD,GAAIA,EACF,GAAI,OAAO,OAAU,KAAe,SACjC,OAAO,iBAAmB,OAAO,aAClC,KAAKpB,CAAC,EAAI,OAEV,OAAM,MAAM1J,EAAiB,OAG/B,KAAK0J,CAAC,EAAI,OAGZ,OAAM,MAAM5J,EAAkB4J,EAAI,KAAOoB,CAAC,EAI9C,OAAO,IACT,CAUA,SAASG,GAAInK,EAAG,CACd,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CACzB,CAUA,SAASoK,GAAKpK,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAQA,SAASqK,GAAMN,EAAK,CAClB,IAAIxJ,EAAGqI,EAAGsB,EASV,SAASrB,EAAQmB,EAAG,CAClB,IAAItG,EAAGnD,EAAGkB,EACRzB,EAAI,KAGN,GAAI,EAAEA,aAAa6I,GAAU,OAAO,IAAIA,EAAQmB,CAAC,EAMjD,GAFAhK,EAAE,YAAc6I,EAEZyB,GAAkBN,CAAC,EAAG,CACxBhK,EAAE,EAAIgK,EAAE,EAEJlL,EACE,CAACkL,EAAE,GAAKA,EAAE,EAAInB,EAAQ,MAGxB7I,EAAE,EAAI,IACNA,EAAE,EAAI,MACGgK,EAAE,EAAInB,EAAQ,MAGvB7I,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,IAERA,EAAE,EAAIgK,EAAE,EACRhK,EAAE,EAAIgK,EAAE,EAAE,MAAM,IAGlBhK,EAAE,EAAIgK,EAAE,EACRhK,EAAE,EAAIgK,EAAE,EAAIA,EAAE,EAAE,MAAM,EAAIA,EAAE,GAG9B,MACF,CAIA,GAFAvI,EAAI,OAAOuI,EAEPvI,IAAM,SAAU,CAClB,GAAIuI,IAAM,EAAG,CACXhK,EAAE,EAAI,EAAIgK,EAAI,EAAI,GAAK,EACvBhK,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,EACR,MACF,CAUA,GARIgK,EAAI,GACNA,EAAI,CAACA,EACLhK,EAAE,EAAI,IAENA,EAAE,EAAI,EAIJgK,IAAM,CAAC,CAACA,GAAKA,EAAI,IAAK,CACxB,IAAKtG,EAAI,EAAGnD,EAAIyJ,EAAGzJ,GAAK,GAAIA,GAAK,GAAImD,IAEjC5E,EACE4E,EAAImF,EAAQ,MACd7I,EAAE,EAAI,IACNA,EAAE,EAAI,MACG0D,EAAImF,EAAQ,MACrB7I,EAAE,EAAI,EACNA,EAAE,EAAI,CAAC,CAAC,IAERA,EAAE,EAAI0D,EACN1D,EAAE,EAAI,CAACgK,CAAC,IAGVhK,EAAE,EAAI0D,EACN1D,EAAE,EAAI,CAACgK,CAAC,GAGV,MACF,CAGA,GAAIA,EAAI,IAAM,EAAG,CACVA,IAAGhK,EAAE,EAAI,KACdA,EAAE,EAAI,IACNA,EAAE,EAAI,KACN,MACF,CAEA,OAAOwI,GAAaxI,EAAGgK,EAAE,SAAS,CAAC,CACrC,CAEA,GAAIvI,IAAM,SACR,OAAKlB,EAAIyJ,EAAE,WAAW,CAAC,KAAO,IAC5BA,EAAIA,EAAE,MAAM,CAAC,EACbhK,EAAE,EAAI,KAEFO,IAAM,KAAIyJ,EAAIA,EAAE,MAAM,CAAC,GAC3BhK,EAAE,EAAI,GAGDP,GAAU,KAAKuK,CAAC,EAAIxB,GAAaxI,EAAGgK,CAAC,EAAIvB,GAAWzI,EAAGgK,CAAC,EAGjE,GAAIvI,IAAM,SACR,OAAIuI,EAAI,GACNA,EAAI,CAACA,EACLhK,EAAE,EAAI,IAENA,EAAE,EAAI,EAGDwI,GAAaxI,EAAGgK,EAAE,SAAS,CAAC,EAGrC,MAAM,MAAMhL,EAAkBgL,CAAC,CACjC,CA2DA,GAzDAnB,EAAQ,UAAY9I,EAEpB8I,EAAQ,SAAW,EACnBA,EAAQ,WAAa,EACrBA,EAAQ,WAAa,EACrBA,EAAQ,YAAc,EACtBA,EAAQ,cAAgB,EACxBA,EAAQ,gBAAkB,EAC1BA,EAAQ,gBAAkB,EAC1BA,EAAQ,gBAAkB,EAC1BA,EAAQ,iBAAmB,EAC3BA,EAAQ,OAAS,EAEjBA,EAAQ,OAASA,EAAQ,IAAMiB,GAC/BjB,EAAQ,MAAQwB,GAChBxB,EAAQ,UAAYyB,GAEpBzB,EAAQ,IAAMK,GACdL,EAAQ,KAAOM,GACfN,EAAQ,MAAQO,GAChBP,EAAQ,IAAMQ,GACdR,EAAQ,KAAOS,GACfT,EAAQ,MAAQU,GAChBV,EAAQ,KAAOW,GACfX,EAAQ,MAAQY,GAChBZ,EAAQ,MAAQa,GAChBb,EAAQ,KAAOc,GACfd,EAAQ,KAAOe,GACff,EAAQ,MAAQgB,GAChBhB,EAAQ,IAAMsB,GACdtB,EAAQ,KAAOuB,GACfvB,EAAQ,IAAM0B,GACd1B,EAAQ,IAAM2B,GACd3B,EAAQ,MAAQ4B,GAChB5B,EAAQ,MAAQ6B,GAChB7B,EAAQ,GAAK8B,GACb9B,EAAQ,IAAM+B,GACd/B,EAAQ,MAAQgC,GAChBhC,EAAQ,KAAOiC,GACfjC,EAAQ,IAAM1I,GACd0I,EAAQ,IAAM3I,GACd2I,EAAQ,IAAMkC,GACdlC,EAAQ,IAAMmC,GACdnC,EAAQ,IAAMX,GACdW,EAAQ,OAASoC,GACjBpC,EAAQ,MAAQqC,GAChBrC,EAAQ,KAAOxB,GACfwB,EAAQ,IAAMsC,GACdtC,EAAQ,KAAOuC,GACfvC,EAAQ,KAAOwC,GACfxC,EAAQ,IAAMyC,GACdzC,EAAQ,IAAMV,GACdU,EAAQ,IAAM0C,GACd1C,EAAQ,KAAO2C,GACf3C,EAAQ,MAAQ4C,GAEZ1B,IAAQ,SAAQA,EAAM,CAAC,GACvBA,GACEA,EAAI,WAAa,GAEnB,IADAG,EAAK,CAAC,YAAa,WAAY,WAAY,WAAY,OAAQ,OAAQ,SAAU,QAAQ,EACpF3J,EAAI,EAAGA,EAAI2J,EAAG,QAAcH,EAAI,eAAenB,EAAIsB,EAAG3J,GAAG,CAAC,IAAGwJ,EAAInB,CAAC,EAAI,KAAKA,CAAC,GAIrF,OAAAC,EAAQ,OAAOkB,CAAG,EAEXlB,CACT,CAWA,SAAS0B,GAAIvK,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAUA,SAASkK,GAAIxK,EAAG,CACd,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CACzB,CASA,SAASyK,GAAMzK,EAAG,CAChB,OAAOC,EAASD,EAAI,IAAI,KAAKA,CAAC,EAAGA,EAAE,EAAI,EAAG,CAAC,CAC7C,CAYA,SAAS0K,IAAQ,CACf,IAAInK,EAAG,EACLkB,EAAI,IAAI,KAAK,CAAC,EAIhB,IAFA3C,EAAW,GAENyB,EAAI,EAAGA,EAAI,UAAU,QAExB,GADA,EAAI,IAAI,KAAK,UAAUA,GAAG,CAAC,EACtB,EAAE,EAMIkB,EAAE,IACXA,EAAIA,EAAE,KAAK,EAAE,MAAM,CAAC,CAAC,OAPb,CACR,GAAI,EAAE,EACJ,OAAA3C,EAAW,GACJ,IAAI,KAAK,GAAK,EAEvB2C,EAAI,CACN,CAKF,OAAA3C,EAAW,GAEJ2C,EAAE,KAAK,CAChB,CAQA,SAAS6I,GAAkBP,EAAK,CAC9B,OAAOA,aAAelB,GAAWkB,GAAOA,EAAI,cAAgB5K,IAAO,EACrE,CAUA,SAASwL,GAAG3K,EAAG,CACb,OAAO,IAAI,KAAKA,CAAC,EAAE,GAAG,CACxB,CAaA,SAAS4K,GAAI5K,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAUA,SAASwK,GAAK9K,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CAAC,CAC1B,CAUA,SAAS6K,GAAM7K,EAAG,CAChB,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,EAAE,CAC3B,CASA,SAASG,IAAM,CACb,OAAO6H,GAAS,KAAM,UAAW,EAAE,CACrC,CASA,SAAS9H,IAAM,CACb,OAAO8H,GAAS,KAAM,UAAW,CAAC,CACpC,CAWA,SAAS+C,GAAI/K,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAWA,SAAS0K,GAAIhL,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAWA,SAAS4H,GAAIlI,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAWA,SAAS2K,GAAOzJ,EAAI,CAClB,IAAIO,EAAG2B,EAAGtD,EAAGgB,EACXb,EAAI,EACJc,EAAI,IAAI,KAAK,CAAC,EACdkE,EAAK,CAAC,EAOR,GALI/D,IAAO,OAAQA,EAAK,KAAK,UACxB+C,EAAW/C,EAAI,EAAGjD,CAAU,EAEjC6B,EAAI,KAAK,KAAKoB,EAAK7B,CAAQ,EAEtB,KAAK,OAIH,GAAI,OAAO,gBAGhB,IAFAoC,EAAI,OAAO,gBAAgB,IAAI,YAAY3B,CAAC,CAAC,EAEtCG,EAAIH,GACTgB,EAAIW,EAAExB,CAAC,EAIHa,GAAK,MACPW,EAAExB,CAAC,EAAI,OAAO,gBAAgB,IAAI,YAAY,CAAC,CAAC,EAAE,CAAC,EAKnDgF,EAAGhF,GAAG,EAAIa,EAAI,YAKT,OAAO,YAAa,CAK7B,IAFAW,EAAI,OAAO,YAAY3B,GAAK,CAAC,EAEtBG,EAAIH,GAGTgB,EAAIW,EAAExB,CAAC,GAAKwB,EAAExB,EAAI,CAAC,GAAK,IAAMwB,EAAExB,EAAI,CAAC,GAAK,MAAQwB,EAAExB,EAAI,CAAC,EAAI,MAAS,IAGlEa,GAAK,MACP,OAAO,YAAY,CAAC,EAAE,KAAKW,EAAGxB,CAAC,GAK/BgF,EAAG,KAAKnE,EAAI,GAAG,EACfb,GAAK,GAITA,EAAIH,EAAI,CACV,KACE,OAAM,MAAMlB,EAAiB,MA9C7B,MAAOqB,EAAIH,GAAImF,EAAGhF,GAAG,EAAI,KAAK,OAAO,EAAI,IAAM,EA2DjD,IAVAH,EAAImF,EAAG,EAAEhF,CAAC,EACViB,GAAM7B,EAGFS,GAAKoB,IACPJ,EAAI/B,EAAQ,GAAIM,EAAW6B,CAAE,EAC7B+D,EAAGhF,CAAC,GAAKH,EAAIgB,EAAI,GAAKA,GAIjBmE,EAAGhF,CAAC,IAAM,EAAGA,IAAKgF,EAAG,IAAI,EAGhC,GAAIhF,EAAI,EACNmD,EAAI,EACJ6B,EAAK,CAAC,CAAC,MACF,CAIL,IAHA7B,EAAI,GAGG6B,EAAG,CAAC,IAAM,EAAG7B,GAAK/D,EAAU4F,EAAG,MAAM,EAG5C,IAAKnF,EAAI,EAAGgB,EAAImE,EAAG,CAAC,EAAGnE,GAAK,GAAIA,GAAK,GAAIhB,IAGrCA,EAAIT,IAAU+D,GAAK/D,EAAWS,EACpC,CAEA,OAAAiB,EAAE,EAAIqC,EACNrC,EAAE,EAAIkE,EAEClE,CACT,CAWA,SAAS6J,GAAMlL,EAAG,CAChB,OAAOC,EAASD,EAAI,IAAI,KAAKA,CAAC,EAAGA,EAAE,EAAI,EAAG,KAAK,QAAQ,CACzD,CAcA,SAASqH,GAAKrH,EAAG,CACf,OAAAA,EAAI,IAAI,KAAKA,CAAC,EACPA,EAAE,EAAKA,EAAE,EAAE,CAAC,EAAIA,EAAE,EAAI,EAAIA,EAAE,EAAKA,EAAE,GAAK,GACjD,CAUA,SAASmL,GAAInL,EAAG,CACd,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CACzB,CAUA,SAASoL,GAAKpL,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAUA,SAASqL,GAAKrL,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CAWA,SAASsL,GAAItL,EAAGM,EAAG,CACjB,OAAO,IAAI,KAAKN,CAAC,EAAE,IAAIM,CAAC,CAC1B,CAYA,SAAS6H,IAAM,CACb,IAAI5H,EAAI,EACN0H,EAAO,UACPjI,EAAI,IAAI,KAAKiI,EAAK1H,CAAC,CAAC,EAGtB,IADAzB,EAAW,GACJkB,EAAE,GAAK,EAAEO,EAAI0H,EAAK,QAASjI,EAAIA,EAAE,KAAKiI,EAAK1H,CAAC,CAAC,EACpD,OAAAzB,EAAW,GAEJmB,EAASD,EAAG,KAAK,UAAW,KAAK,QAAQ,CAClD,CAUA,SAASuL,GAAIvL,EAAG,CACd,OAAO,IAAI,KAAKA,CAAC,EAAE,IAAI,CACzB,CAUA,SAASwL,GAAKxL,EAAG,CACf,OAAO,IAAI,KAAKA,CAAC,EAAE,KAAK,CAC1B,CASA,SAASyL,GAAMzL,EAAG,CAChB,OAAOC,EAASD,EAAI,IAAI,KAAKA,CAAC,EAAGA,EAAE,EAAI,EAAG,CAAC,CAC7C,CAGAD,EAAE,OAAO,IAAI,4BAA4B,CAAC,EAAIA,EAAE,SAChDA,EAAE,OAAO,WAAW,EAAI,UAGjB,IAAI8I,EAAU9I,EAAE,YAAcsK,GAAM1L,EAAQ,EAGnDF,GAAO,IAAIoK,EAAQpK,EAAI,EACvBC,GAAK,IAAImK,EAAQnK,EAAE,EAEnB,IAAOgN,GAAQ7C", "names": ["public_exports", "__export", "validator", "validator", "_args", "args", "secret", "representations", "ObjectEnumValue", "arg", "NullTypesEnumValue", "__brand_DbNull", "DbNull", "__privateAdd", "setClassName", "__brand_<PERSON>son<PERSON><PERSON>", "JsonNull", "__brand_<PERSON><PERSON><PERSON>", "AnyNull", "objectEnumValues", "classObject", "name", "allowList", "makeStrictEnum", "definition", "target", "property", "isNode", "_a", "_b", "isBun", "isDeno", "isNetlify", "isEdgeLight", "isWorkerd", "detectRuntime", "check", "runtimes<PERSON><PERSON><PERSON>Names", "getRuntime", "runtimeId", "EXP_LIMIT", "MAX_DIGITS", "NUMERALS", "LN10", "PI", "DEFAULTS", "inexact", "quadrant", "external", "decimalError", "invalidArgument", "precisionLimitExceeded", "cryptoUnavailable", "tag", "mathfloor", "mathpow", "isBinary", "isHex", "isOctal", "isDecimal", "BASE", "LOG_BASE", "MAX_SAFE_INTEGER", "LN10_PRECISION", "PI_PRECISION", "P", "x", "finalise", "min", "max", "k", "Ctor", "y", "i", "j", "xdL", "ydL", "xd", "yd", "xs", "ys", "pr", "rm", "cosine", "toLessThanHalfPi", "m", "n", "r", "rep", "s", "sd", "t", "t3", "t3plusx", "digitsToString", "divide", "w", "d", "len", "one", "tinyPow", "taylorSeries", "cosh2_x", "d8", "sinh2_x", "d5", "d16", "d20", "getPi", "wpr", "xsd", "halfPi", "px", "x2", "base", "isBase10", "denominator", "inf", "num", "arg", "guard", "naturalLogarithm", "getLn10", "checkRoundingDigits", "e", "xe", "xLTy", "getBase10Exponent", "q", "naturalExponential", "carry", "z", "getPrecision", "sine", "rL", "toStringBinary", "dp", "checkInt32", "str", "finiteToString", "maxD", "d0", "d1", "d2", "n0", "n1", "yn", "intPow", "ws", "indexOfLastWord", "getZeroString", "repeating", "di", "rd", "convertBase", "baseIn", "baseOut", "arr", "arrL", "strL", "cos2x", "multiplyInteger", "temp", "compare", "a", "b", "aL", "bL", "subtract", "cmp", "logBase", "more", "prod", "prodL", "qd", "rem", "remL", "rem0", "xi", "xL", "yd0", "yL", "yz", "sign", "isTruncated", "digits", "roundUp", "xdi", "out", "isExp", "nonFiniteToString", "zs", "truncate", "isOdd", "maxOrMin", "args", "pow", "sum", "c", "c0", "numerator", "x1", "parseDecimal", "parseOther", "divisor", "isFloat", "p", "Decimal", "sin2_x", "isHyperbolic", "isNeg", "pi", "abs", "acos", "acosh", "add", "asin", "asinh", "atan", "atanh", "atan2", "cbrt", "ceil", "clamp", "config", "obj", "v", "useDefaults", "ps", "cos", "cosh", "clone", "isDecimalInstance", "div", "exp", "floor", "hypot", "ln", "log", "log10", "log2", "mod", "mul", "random", "round", "sin", "sinh", "sqrt", "sub", "tan", "tanh", "trunc", "decimal_default"]}