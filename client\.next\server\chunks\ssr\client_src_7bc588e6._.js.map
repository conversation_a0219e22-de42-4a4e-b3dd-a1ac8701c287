{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionI<PERSON> is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,wEACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,wEACA;AAEG,MAAM,gBAAgB,IAAA,kRAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,wEACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,wEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 33, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/accordion.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/accordion.tsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;AACvE;;AACO,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,oDACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oDACA;AAEG,MAAM,gBAAgB,IAAA,kRAAuB,EAChD;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,oDACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 62, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 70, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/utils.ts"], "sourcesContent": ["import { type ClassValue, clsx } from 'clsx';\nimport { twMerge } from 'tailwind-merge';\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs));\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,IAAA,gLAAO,EAAC,IAAA,uJAAI,EAAC;AACtB", "debugId": null}}, {"offset": {"line": 85, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,IAAA,iLAAG,EACxB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 142, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/navigation-menu.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport * as NavigationMenuPrimitive from \"@radix-ui/react-navigation-menu\"\nimport { cva } from \"class-variance-authority\"\nimport { ChevronDownIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction NavigationMenu({\n  className,\n  children,\n  viewport = true,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Root> & {\n  viewport?: boolean\n}) {\n  return (\n    <NavigationMenuPrimitive.Root\n      data-slot=\"navigation-menu\"\n      data-viewport={viewport}\n      className={cn(\n        \"group/navigation-menu relative flex max-w-max flex-1 items-center justify-center\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      {viewport && <NavigationMenuViewport />}\n    </NavigationMenuPrimitive.Root>\n  )\n}\n\nfunction NavigationMenuList({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.List>) {\n  return (\n    <NavigationMenuPrimitive.List\n      data-slot=\"navigation-menu-list\"\n      className={cn(\n        \"group flex flex-1 list-none items-center justify-center gap-1\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuItem({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Item>) {\n  return (\n    <NavigationMenuPrimitive.Item\n      data-slot=\"navigation-menu-item\"\n      className={cn(\"relative\", className)}\n      {...props}\n    />\n  )\n}\n\nconst navigationMenuTriggerStyle = cva(\n  \"group inline-flex h-9 w-max items-center justify-center rounded-md bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground disabled:pointer-events-none disabled:opacity-50 data-[state=open]:hover:bg-accent data-[state=open]:text-accent-foreground data-[state=open]:focus:bg-accent data-[state=open]:bg-accent/50 focus-visible:ring-ring/50 outline-none transition-[color,box-shadow] focus-visible:ring-[3px] focus-visible:outline-1\"\n)\n\nfunction NavigationMenuTrigger({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Trigger>) {\n  return (\n    <NavigationMenuPrimitive.Trigger\n      data-slot=\"navigation-menu-trigger\"\n      className={cn(navigationMenuTriggerStyle(), \"group\", className)}\n      {...props}\n    >\n      {children}{\" \"}\n      <ChevronDownIcon\n        className=\"relative top-[1px] ml-1 size-3 transition duration-300 group-data-[state=open]:rotate-180\"\n        aria-hidden=\"true\"\n      />\n    </NavigationMenuPrimitive.Trigger>\n  )\n}\n\nfunction NavigationMenuContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Content>) {\n  return (\n    <NavigationMenuPrimitive.Content\n      data-slot=\"navigation-menu-content\"\n      className={cn(\n        \"data-[motion^=from-]:animate-in data-[motion^=to-]:animate-out data-[motion^=from-]:fade-in data-[motion^=to-]:fade-out data-[motion=from-end]:slide-in-from-right-52 data-[motion=from-start]:slide-in-from-left-52 data-[motion=to-end]:slide-out-to-right-52 data-[motion=to-start]:slide-out-to-left-52 top-0 left-0 w-full p-2 pr-2.5 md:absolute md:w-auto\",\n        \"group-data-[viewport=false]/navigation-menu:bg-popover group-data-[viewport=false]/navigation-menu:text-popover-foreground group-data-[viewport=false]/navigation-menu:data-[state=open]:animate-in group-data-[viewport=false]/navigation-menu:data-[state=closed]:animate-out group-data-[viewport=false]/navigation-menu:data-[state=closed]:zoom-out-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:zoom-in-95 group-data-[viewport=false]/navigation-menu:data-[state=open]:fade-in-0 group-data-[viewport=false]/navigation-menu:data-[state=closed]:fade-out-0 group-data-[viewport=false]/navigation-menu:top-full group-data-[viewport=false]/navigation-menu:mt-1.5 group-data-[viewport=false]/navigation-menu:overflow-hidden group-data-[viewport=false]/navigation-menu:rounded-md group-data-[viewport=false]/navigation-menu:border group-data-[viewport=false]/navigation-menu:shadow group-data-[viewport=false]/navigation-menu:duration-200 **:data-[slot=navigation-menu-link]:focus:ring-0 **:data-[slot=navigation-menu-link]:focus:outline-none\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuViewport({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Viewport>) {\n  return (\n    <div\n      className={cn(\n        \"absolute top-full left-0 isolate z-50 flex justify-center\"\n      )}\n    >\n      <NavigationMenuPrimitive.Viewport\n        data-slot=\"navigation-menu-viewport\"\n        className={cn(\n          \"origin-top-center bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-90 relative mt-1.5 h-[var(--radix-navigation-menu-viewport-height)] w-full overflow-hidden rounded-md border shadow md:w-[var(--radix-navigation-menu-viewport-width)]\",\n          className\n        )}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction NavigationMenuLink({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Link>) {\n  return (\n    <NavigationMenuPrimitive.Link\n      data-slot=\"navigation-menu-link\"\n      className={cn(\n        \"data-[active=true]:focus:bg-accent data-[active=true]:hover:bg-accent data-[active=true]:bg-accent/50 data-[active=true]:text-accent-foreground hover:bg-accent hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground focus-visible:ring-ring/50 [&_svg:not([class*='text-'])]:text-muted-foreground flex flex-col gap-1 rounded-sm p-2 text-sm transition-all outline-none focus-visible:ring-[3px] focus-visible:outline-1 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction NavigationMenuIndicator({\n  className,\n  ...props\n}: React.ComponentProps<typeof NavigationMenuPrimitive.Indicator>) {\n  return (\n    <NavigationMenuPrimitive.Indicator\n      data-slot=\"navigation-menu-indicator\"\n      className={cn(\n        \"data-[state=visible]:animate-in data-[state=hidden]:animate-out data-[state=hidden]:fade-out data-[state=visible]:fade-in top-full z-[1] flex h-1.5 items-end justify-center overflow-hidden\",\n        className\n      )}\n      {...props}\n    >\n      <div className=\"bg-border relative top-[60%] h-2 w-2 rotate-45 rounded-tl-sm shadow-md\" />\n    </NavigationMenuPrimitive.Indicator>\n  )\n}\n\nexport {\n  NavigationMenu,\n  NavigationMenuList,\n  NavigationMenuItem,\n  NavigationMenuContent,\n  NavigationMenuTrigger,\n  NavigationMenuLink,\n  NavigationMenuIndicator,\n  NavigationMenuViewport,\n  navigationMenuTriggerStyle,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;AACA;AACA;AACA;AAEA;;;;;;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,QAAQ,EACR,WAAW,IAAI,EACf,GAAG,OAGJ;IACC,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,iBAAe;QACf,WAAW,IAAA,mIAAE,EACX,oFACA;QAED,GAAG,KAAK;;YAER;YACA,0BAAY,wPAAC;;;;;;;;;;;AAGpB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,iEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,YAAY;QACzB,GAAG,KAAK;;;;;;AAGf;AAEA,MAAM,6BAA6B,IAAA,iLAAG,EACpC;AAGF,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,wPAAC,mMAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,8BAA8B,SAAS;QACpD,GAAG,KAAK;;YAER;YAAU;0BACX,wPAAC,qPAAe;gBACd,WAAU;gBACV,eAAY;;;;;;;;;;;;AAIpB;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,wPAAC,mMAA+B;QAC9B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,oWACA,6hCACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,wPAAC;QACC,WAAW,IAAA,mIAAE,EACX;kBAGF,cAAA,wPAAC,oMAAgC;YAC/B,aAAU;YACV,WAAW,IAAA,mIAAE,EACX,sVACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,mBAAmB,EAC1B,SAAS,EACT,GAAG,OACuD;IAC1D,qBACE,wPAAC,gMAA4B;QAC3B,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,ydACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,wBAAwB,EAC/B,SAAS,EACT,GAAG,OAC4D;IAC/D,qBACE,wPAAC,qMAAiC;QAChC,aAAU;QACV,WAAW,IAAA,mIAAE,EACX,gMACA;QAED,GAAG,KAAK;kBAET,cAAA,wPAAC;YAAI,WAAU;;;;;;;;;;;AAGrB", "debugId": null}}, {"offset": {"line": 301, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/sheet.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet() from the server but Sheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"Sheet\",\n);\nexport const SheetClose = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetClose() from the server but SheetClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetClose\",\n);\nexport const SheetContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet<PERSON>ontent() from the server but <PERSON><PERSON><PERSON>ontent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetContent\",\n);\nexport const SheetDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetDescription() from the server but SheetDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetDescription\",\n);\nexport const SheetFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetFooter() from the server but SheetFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetFooter\",\n);\nexport const SheetHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetHeader() from the server but SheetHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetHeader\",\n);\nexport const SheetTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTitle() from the server but SheetTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetTitle\",\n);\nexport const SheetTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTrigger() from the server but SheetTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx <module evaluation>\",\n    \"SheetTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,kRAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,oEACA;AAEG,MAAM,aAAa,IAAA,kRAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oEACA;AAEG,MAAM,eAAe,IAAA,kRAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oEACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,oEACA;AAEG,MAAM,cAAc,IAAA,kRAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA;AAEG,MAAM,cAAc,IAAA,kRAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,oEACA;AAEG,MAAM,aAAa,IAAA,kRAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,oEACA;AAEG,MAAM,eAAe,IAAA,kRAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,oEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 350, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/sheet.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Sheet = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet() from the server but Sheet is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"Sheet\",\n);\nexport const SheetClose = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetClose() from the server but SheetClose is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetClose\",\n);\nexport const SheetContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call Sheet<PERSON>ontent() from the server but Sheet<PERSON>ontent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetContent\",\n);\nexport const SheetDescription = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetDescription() from the server but SheetDescription is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetDescription\",\n);\nexport const SheetFooter = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetFooter() from the server but SheetFooter is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetFooter\",\n);\nexport const SheetHeader = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetHeader() from the server but SheetHeader is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetHeader\",\n);\nexport const SheetTitle = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTitle() from the server but SheetTitle is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetTitle\",\n);\nexport const SheetTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call SheetTrigger() from the server but SheetTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/sheet.tsx\",\n    \"SheetTrigger\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,QAAQ,IAAA,kRAAuB,EACxC;IAAa,MAAM,IAAI,MAAM;AAA0N,GACvP,gDACA;AAEG,MAAM,aAAa,IAAA,kRAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gDACA;AAEG,MAAM,eAAe,IAAA,kRAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gDACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,gDACA;AAEG,MAAM,cAAc,IAAA,kRAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gDACA;AAEG,MAAM,cAAc,IAAA,kRAAuB,EAC9C;IAAa,MAAM,IAAI,MAAM;AAAsO,GACnQ,gDACA;AAEG,MAAM,aAAa,IAAA,kRAAuB,EAC7C;IAAa,MAAM,IAAI,MAAM;AAAoO,GACjQ,gDACA;AAEG,MAAM,eAAe,IAAA,kRAAuB,EAC/C;IAAa,MAAM,IAAI,MAAM;AAAwO,GACrQ,gDACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 399, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 407, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/client/src/components/header/DarkModeToggle/page.tsx <module evaluation> from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/header/DarkModeToggle/page.tsx <module evaluation>\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,kRAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAwT,GACrV,sFACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 421, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/DarkModeToggle/page.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport default registerClientReference(\n    function() { throw new Error(\"Attempted to call the default export of [project]/client/src/components/header/DarkModeToggle/page.tsx from the server, but it's on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/header/DarkModeToggle/page.tsx\",\n    \"default\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;AACvE;;uCACe,IAAA,kRAAuB,EAClC;IAAa,MAAM,IAAI,MAAM;AAAoS,GACjU,kEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 435, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 443, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/header/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Menu } from 'lucide-react';\r\nimport {\r\n  Accordion,\r\n  AccordionContent,\r\n  AccordionItem,\r\n  AccordionTrigger,\r\n} from '@/components/ui/accordion';\r\nimport { Button } from '@/components/ui/button';\r\nimport {\r\n  NavigationMenu,\r\n  NavigationMenuContent,\r\n  NavigationMenuItem,\r\n  NavigationMenuLink,\r\n  NavigationMenuList,\r\n  NavigationMenuTrigger,\r\n} from '@/components/ui/navigation-menu';\r\nimport {\r\n  Sheet,\r\n  SheetContent,\r\n  SheetHeader,\r\n  SheetTitle,\r\n  SheetTrigger,\r\n} from '@/components/ui/sheet';\r\nimport DarkModeToggle from '@/components/header/DarkModeToggle/page';\r\n\r\ninterface MenuItem {\r\n  title: string;\r\n  url: string;\r\n  description?: string;\r\n  icon?: React.ReactNode;\r\n  items?: MenuItem[];\r\n}\r\n\r\ninterface HeaderProps {\r\n  logo?: {\r\n    url: string;\r\n    alt: string;\r\n    title: string;\r\n  };\r\n  menu?: MenuItem[];\r\n  auth?: {\r\n    login: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n    signup: {\r\n      title: string;\r\n      url: string;\r\n    };\r\n  };\r\n}\r\n\r\nconst Header = ({\r\n  logo = {\r\n    url: '/',\r\n    alt: 'logo',\r\n    title: 'The Salty Devs',\r\n  },\r\n  menu = [\r\n    { title: 'Articles', url: '/articles' },\r\n    { title: 'Categories', url: '/categories' },\r\n    { title: 'About', url: '/about' },\r\n  ],\r\n  auth = {\r\n    login: { title: 'Login', url: '/login' },\r\n    signup: { title: 'Sign up', url: '/signup' },\r\n  },\r\n}: HeaderProps) => {\r\n  return (\r\n    <section className=\"py-4\">\r\n      <div className=\"container mx-auto w-full\">\r\n        {/* Desktop Menu */}\r\n        <nav className=\"hidden justify-between lg:flex w-full\">\r\n          <div className=\"flex items-center gap-10\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <span className=\"text-2xl font-bold tracking-tighter leading-none flex items-center\">\r\n                {logo.title}\r\n              </span>\r\n            </a>\r\n            <div className=\"flex items-center\">\r\n              <NavigationMenu>\r\n                <NavigationMenuList className=\"items-center\">\r\n                  {menu.map((item) => renderMenuItem(item))}\r\n                </NavigationMenuList>\r\n              </NavigationMenu>\r\n            </div>\r\n          </div>\r\n          <div className=\"flex items-center gap-10\">\r\n            <div>\r\n              <DarkModeToggle />\r\n            </div>\r\n            <div className=\"flex gap-2\">\r\n              <Button asChild variant=\"outline\">\r\n                <a href={auth.login.url}>{auth.login.title}</a>\r\n              </Button>\r\n              <Button asChild>\r\n                <a href={auth.signup.url}>{auth.signup.title}</a>\r\n              </Button>\r\n            </div>\r\n          </div>\r\n        </nav>\r\n\r\n        {/* Mobile Menu */}\r\n        <div className=\"block lg:hidden\">\r\n          <div className=\"flex items-center justify-between mx-6\">\r\n            {/* Logo */}\r\n            <a href={logo.url} className=\"flex items-center gap-2\">\r\n              <span className=\"text-lg font-semibold tracking-tighter leading-none\">\r\n                {logo.title}\r\n              </span>\r\n            </a>\r\n            <Sheet>\r\n              <SheetTrigger asChild>\r\n                <Button variant=\"outline\" size=\"icon\">\r\n                  <Menu className=\"size-4\" />\r\n                </Button>\r\n              </SheetTrigger>\r\n              <SheetContent className=\"overflow-y-auto\">\r\n                <SheetHeader>\r\n                  <SheetTitle>\r\n                    <a href={logo.url} className=\"flex items-center gap-2\">\r\n                      <span className=\"text-lg font-semibold tracking-tighter\">\r\n                        {logo.title}\r\n                      </span>\r\n                    </a>\r\n                  </SheetTitle>\r\n                </SheetHeader>\r\n                <div className=\"flex flex-col gap-6 p-4\">\r\n                  <Accordion\r\n                    type=\"single\"\r\n                    collapsible\r\n                    className=\"flex w-full flex-col gap-4\"\r\n                  >\r\n                    {menu.map((item) => renderMobileMenuItem(item))}\r\n                  </Accordion>\r\n\r\n                  <div className=\"flex flex-col gap-3\">\r\n                    <Button asChild variant=\"outline\">\r\n                      <a href={auth.login.url}>{auth.login.title}</a>\r\n                    </Button>\r\n                    <Button asChild>\r\n                      <a href={auth.signup.url}>{auth.signup.title}</a>\r\n                    </Button>\r\n                  </div>\r\n                </div>\r\n              </SheetContent>\r\n            </Sheet>\r\n          </div>\r\n        </div>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nconst renderMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <NavigationMenuItem key={item.title}>\r\n        <NavigationMenuTrigger>{item.title}</NavigationMenuTrigger>\r\n        <NavigationMenuContent className=\"bg-popover text-popover-foreground\">\r\n          {item.items.map((subItem) => (\r\n            <NavigationMenuLink asChild key={subItem.title} className=\"w-80\">\r\n              <SubMenuLink item={subItem} />\r\n            </NavigationMenuLink>\r\n          ))}\r\n        </NavigationMenuContent>\r\n      </NavigationMenuItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <NavigationMenuItem key={item.title}>\r\n      <NavigationMenuLink\r\n        href={item.url}\r\n        className=\"bg-background hover:bg-muted hover:text-accent-foreground group inline-flex h-10 w-max items-center justify-center rounded-md px-4 py-2 text-lg font-medium transition-colors\"\r\n      >\r\n        {item.title}\r\n      </NavigationMenuLink>\r\n    </NavigationMenuItem>\r\n  );\r\n};\r\n\r\nconst renderMobileMenuItem = (item: MenuItem) => {\r\n  if (item.items) {\r\n    return (\r\n      <AccordionItem key={item.title} value={item.title} className=\"border-b-0\">\r\n        <AccordionTrigger className=\"text-md py-0 font-semibold hover:no-underline\">\r\n          {item.title}\r\n        </AccordionTrigger>\r\n        <AccordionContent className=\"mt-2\">\r\n          {item.items.map((subItem) => (\r\n            <SubMenuLink key={subItem.title} item={subItem} />\r\n          ))}\r\n        </AccordionContent>\r\n      </AccordionItem>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <a key={item.title} href={item.url} className=\"text-md font-semibold\">\r\n      {item.title}\r\n    </a>\r\n  );\r\n};\r\n\r\nconst SubMenuLink = ({ item }: { item: MenuItem }) => {\r\n  return (\r\n    <a\r\n      className=\"hover:bg-muted hover:text-accent-foreground flex select-none flex-row gap-4 rounded-md p-3 leading-none no-underline outline-none transition-colors\"\r\n      href={item.url}\r\n    >\r\n      <div className=\"text-foreground\">{item.icon}</div>\r\n      <div>\r\n        <div className=\"text-sm font-semibold\">{item.title}</div>\r\n        {item.description && (\r\n          <p className=\"text-muted-foreground text-sm leading-snug\">\r\n            {item.description}\r\n          </p>\r\n        )}\r\n      </div>\r\n    </a>\r\n  );\r\n};\r\n\r\nexport default Header;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAMA;AACA;AAQA;AAOA;;;;;;;;AA6BA,MAAM,SAAS,CAAC,EACd,OAAO;IACL,KAAK;IACL,KAAK;IACL,OAAO;AACT,CAAC,EACD,OAAO;IACL;QAAE,OAAO;QAAY,KAAK;IAAY;IACtC;QAAE,OAAO;QAAc,KAAK;IAAc;IAC1C;QAAE,OAAO;QAAS,KAAK;IAAS;CACjC,EACD,OAAO;IACL,OAAO;QAAE,OAAO;QAAS,KAAK;IAAS;IACvC,QAAQ;QAAE,OAAO;QAAW,KAAK;IAAU;AAC7C,CAAC,EACW;IACZ,qBACE,wPAAC;QAAQ,WAAU;kBACjB,cAAA,wPAAC;YAAI,WAAU;;8BAEb,wPAAC;oBAAI,WAAU;;sCACb,wPAAC;4BAAI,WAAU;;8CAEb,wPAAC;oCAAE,MAAM,KAAK,GAAG;oCAAE,WAAU;8CAC3B,cAAA,wPAAC;wCAAK,WAAU;kDACb,KAAK,KAAK;;;;;;;;;;;8CAGf,wPAAC;oCAAI,WAAU;8CACb,cAAA,wPAAC,0KAAc;kDACb,cAAA,wPAAC,8KAAkB;4CAAC,WAAU;sDAC3B,KAAK,GAAG,CAAC,CAAC,OAAS,eAAe;;;;;;;;;;;;;;;;;;;;;;sCAK3C,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;8CACC,cAAA,wPAAC,2KAAc;;;;;;;;;;8CAEjB,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,sJAAM;4CAAC,OAAO;4CAAC,SAAQ;sDACtB,cAAA,wPAAC;gDAAE,MAAM,KAAK,KAAK,CAAC,GAAG;0DAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;sDAE5C,wPAAC,sJAAM;4CAAC,OAAO;sDACb,cAAA,wPAAC;gDAAE,MAAM,KAAK,MAAM,CAAC,GAAG;0DAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8BAOpD,wPAAC;oBAAI,WAAU;8BACb,cAAA,wPAAC;wBAAI,WAAU;;0CAEb,wPAAC;gCAAE,MAAM,KAAK,GAAG;gCAAE,WAAU;0CAC3B,cAAA,wPAAC;oCAAK,WAAU;8CACb,KAAK,KAAK;;;;;;;;;;;0CAGf,wPAAC,oJAAK;;kDACJ,wPAAC,2JAAY;wCAAC,OAAO;kDACnB,cAAA,wPAAC,sJAAM;4CAAC,SAAQ;4CAAU,MAAK;sDAC7B,cAAA,wPAAC,oNAAI;gDAAC,WAAU;;;;;;;;;;;;;;;;kDAGpB,wPAAC,2JAAY;wCAAC,WAAU;;0DACtB,wPAAC,0JAAW;0DACV,cAAA,wPAAC,yJAAU;8DACT,cAAA,wPAAC;wDAAE,MAAM,KAAK,GAAG;wDAAE,WAAU;kEAC3B,cAAA,wPAAC;4DAAK,WAAU;sEACb,KAAK,KAAK;;;;;;;;;;;;;;;;;;;;;0DAKnB,wPAAC;gDAAI,WAAU;;kEACb,wPAAC,4JAAS;wDACR,MAAK;wDACL,WAAW;wDACX,WAAU;kEAET,KAAK,GAAG,CAAC,CAAC,OAAS,qBAAqB;;;;;;kEAG3C,wPAAC;wDAAI,WAAU;;0EACb,wPAAC,sJAAM;gEAAC,OAAO;gEAAC,SAAQ;0EACtB,cAAA,wPAAC;oEAAE,MAAM,KAAK,KAAK,CAAC,GAAG;8EAAG,KAAK,KAAK,CAAC,KAAK;;;;;;;;;;;0EAE5C,wPAAC,sJAAM;gEAAC,OAAO;0EACb,cAAA,wPAAC;oEAAE,MAAM,KAAK,MAAM,CAAC,GAAG;8EAAG,KAAK,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAWlE;AAEA,MAAM,iBAAiB,CAAC;IACtB,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,wPAAC,8KAAkB;;8BACjB,wPAAC,iLAAqB;8BAAE,KAAK,KAAK;;;;;;8BAClC,wPAAC,iLAAqB;oBAAC,WAAU;8BAC9B,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,wPAAC,8KAAkB;4BAAC,OAAO;4BAAqB,WAAU;sCACxD,cAAA,wPAAC;gCAAY,MAAM;;;;;;2BADY,QAAQ,KAAK;;;;;;;;;;;WAJ3B,KAAK,KAAK;;;;;IAWvC;IAEA,qBACE,wPAAC,8KAAkB;kBACjB,cAAA,wPAAC,8KAAkB;YACjB,MAAM,KAAK,GAAG;YACd,WAAU;sBAET,KAAK,KAAK;;;;;;OALU,KAAK,KAAK;;;;;AASvC;AAEA,MAAM,uBAAuB,CAAC;IAC5B,IAAI,KAAK,KAAK,EAAE;QACd,qBACE,wPAAC,gKAAa;YAAkB,OAAO,KAAK,KAAK;YAAE,WAAU;;8BAC3D,wPAAC,mKAAgB;oBAAC,WAAU;8BACzB,KAAK,KAAK;;;;;;8BAEb,wPAAC,mKAAgB;oBAAC,WAAU;8BACzB,KAAK,KAAK,CAAC,GAAG,CAAC,CAAC,wBACf,wPAAC;4BAAgC,MAAM;2BAArB,QAAQ,KAAK;;;;;;;;;;;WANjB,KAAK,KAAK;;;;;IAWlC;IAEA,qBACE,wPAAC;QAAmB,MAAM,KAAK,GAAG;QAAE,WAAU;kBAC3C,KAAK,KAAK;OADL,KAAK,KAAK;;;;;AAItB;AAEA,MAAM,cAAc,CAAC,EAAE,IAAI,EAAsB;IAC/C,qBACE,wPAAC;QACC,WAAU;QACV,MAAM,KAAK,GAAG;;0BAEd,wPAAC;gBAAI,WAAU;0BAAmB,KAAK,IAAI;;;;;;0BAC3C,wPAAC;;kCACC,wPAAC;wBAAI,WAAU;kCAAyB,KAAK,KAAK;;;;;;oBACjD,KAAK,WAAW,kBACf,wPAAC;wBAAE,WAAU;kCACV,KAAK,WAAW;;;;;;;;;;;;;;;;;;AAM7B;uCAEe", "debugId": null}}, {"offset": {"line": 928, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/logo.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"Logo\",\n);\nexport const LogoBrandDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoBrandDownload() from the server but LogoBrandDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoBrandDownload\",\n);\nexport const LogoImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImage() from the server but LogoImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImage\",\n);\nexport const LogoImageDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageDesktop() from the server but LogoImageDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImageDesktop\",\n);\nexport const LogoImageMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageMobile() from the server but LogoImageMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoImageMobile\",\n);\nexport const LogoText = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoText() from the server but LogoText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoText\",\n);\nexport const LogoTextDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextDesktop() from the server but LogoTextDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoTextDesktop\",\n);\nexport const LogoTextMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextMobile() from the server but LogoTextMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx <module evaluation>\",\n    \"LogoTextMobile\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,kRAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,mEACA;AAEG,MAAM,oBAAoB,IAAA,kRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,mEACA;AAEG,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,mEACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,mEACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,mEACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,mEACA;AAEG,MAAM,iBAAiB,IAAA,kRAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,mEACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 977, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/logo.tsx/__nextjs-internal-proxy.mjs"], "sourcesContent": ["// This file is generated by next-core EcmascriptClientReferenceModule.\nimport { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Logo = registerClientReference(\n    function() { throw new Error(\"Attempted to call Logo() from the server but Logo is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"Logo\",\n);\nexport const LogoBrandDownload = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoBrandDownload() from the server but LogoBrandDownload is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoBrandDownload\",\n);\nexport const LogoImage = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImage() from the server but LogoImage is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImage\",\n);\nexport const LogoImageDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageDesktop() from the server but LogoImageDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImageDesktop\",\n);\nexport const LogoImageMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoImageMobile() from the server but LogoImageMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoImageMobile\",\n);\nexport const LogoText = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoText() from the server but LogoText is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoText\",\n);\nexport const LogoTextDesktop = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextDesktop() from the server but LogoTextDesktop is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoTextDesktop\",\n);\nexport const LogoTextMobile = registerClientReference(\n    function() { throw new Error(\"Attempted to call LogoTextMobile() from the server but LogoTextMobile is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/client/src/components/ui/logo.tsx\",\n    \"LogoTextMobile\",\n);\n"], "names": [], "mappings": "AAAA,uEAAuE;;;;;;;;;;;;;;;;;;;AACvE;;AACO,MAAM,OAAO,IAAA,kRAAuB,EACvC;IAAa,MAAM,IAAI,MAAM;AAAwN,GACrP,+CACA;AAEG,MAAM,oBAAoB,IAAA,kRAAuB,EACpD;IAAa,MAAM,IAAI,MAAM;AAAkP,GAC/Q,+CACA;AAEG,MAAM,YAAY,IAAA,kRAAuB,EAC5C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,+CACA;AAEG,MAAM,mBAAmB,IAAA,kRAAuB,EACnD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,+CACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;AAEG,MAAM,WAAW,IAAA,kRAAuB,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAgO,GAC7P,+CACA;AAEG,MAAM,kBAAkB,IAAA,kRAAuB,EAClD;IAAa,MAAM,IAAI,MAAM;AAA8O,GAC3Q,+CACA;AAEG,MAAM,iBAAiB,IAAA,kRAAuB,EACjD;IAAa,MAAM,IAAI,MAAM;AAA4O,GACzQ,+CACA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 1026, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 1034, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/footer/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport { Logo, LogoText } from '@/components/ui/logo';\r\n\r\ninterface FooterProps {\r\n  logo?: {\r\n    url: string;\r\n    alt: string;\r\n    title: string;\r\n  };\r\n  tagline?: string;\r\n  copyright?: string;\r\n}\r\n\r\nconst Footer = ({\r\n  logo = {\r\n    alt: 'The Salty Devs',\r\n    title: 'The Salty Devs',\r\n    url: '/',\r\n  },\r\n  tagline = 'Sharing knowledge, one article at a time.',\r\n  copyright = `© ${new Date().getFullYear()} The Salty Devs. All rights reserved.`,\r\n}: FooterProps) => {\r\n  return (\r\n    <section className=\"py-16\">\r\n      <div className=\"container mx-auto\">\r\n        <footer>\r\n          <div className=\"flex flex-col items-center text-center\">\r\n            <div className=\"mb-6\">\r\n              <div className=\"flex items-center justify-center gap-2\">\r\n                <Logo url={logo.url}>\r\n                  <LogoText className=\"text-lg\">{logo.title}</LogoText>\r\n                </Logo>\r\n              </div>\r\n              <p className=\"mt-2 text-muted-foreground\">{tagline}</p>\r\n            </div>\r\n            <div className=\"border-t pt-6 w-full\">\r\n              <p className=\"text-sm text-muted-foreground\">{copyright}</p>\r\n            </div>\r\n          </div>\r\n        </footer>\r\n      </div>\r\n    </section>\r\n  );\r\n};\r\n\r\nexport default Footer;\r\n"], "names": [], "mappings": ";;;;;AACA;;;AAYA,MAAM,SAAS,CAAC,EACd,OAAO;IACL,KAAK;IACL,OAAO;IACP,KAAK;AACP,CAAC,EACD,UAAU,2CAA2C,EACrD,YAAY,CAAC,EAAE,EAAE,IAAI,OAAO,WAAW,GAAG,qCAAqC,CAAC,EACpE;IACZ,qBACE,wPAAC;QAAQ,WAAU;kBACjB,cAAA,wPAAC;YAAI,WAAU;sBACb,cAAA,wPAAC;0BACC,cAAA,wPAAC;oBAAI,WAAU;;sCACb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;oCAAI,WAAU;8CACb,cAAA,wPAAC,kJAAI;wCAAC,KAAK,KAAK,GAAG;kDACjB,cAAA,wPAAC,sJAAQ;4CAAC,WAAU;sDAAW,KAAK,KAAK;;;;;;;;;;;;;;;;8CAG7C,wPAAC;oCAAE,WAAU;8CAA8B;;;;;;;;;;;;sCAE7C,wPAAC;4BAAI,WAAU;sCACb,cAAA,wPAAC;gCAAE,WAAU;0CAAiC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO5D;uCAEe", "debugId": null}}, {"offset": {"line": 1136, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/layout.tsx"], "sourcesContent": ["import type { <PERSON>ada<PERSON> } from 'next';\r\nimport React from 'react';\r\nimport { ThemeProvider } from 'next-themes';\r\nimport Header from '@/components/header/page';\r\nimport Footer from '@/components/footer/page';\r\nimport '../styles/globals.css';\r\n\r\nexport const metadata: Metadata = {\r\n  title: 'The Salty Devs',\r\n  description: 'The Salty Devs Blog Page',\r\n};\r\n\r\nexport default function RootLayout({\r\n  children,\r\n}: Readonly<{\r\n  children: React.ReactNode;\r\n}>) {\r\n  return (\r\n    <html lang=\"en\" suppressHydrationWarning={true}>\r\n      <body suppressHydrationWarning>\r\n        <ThemeProvider\r\n          attribute=\"class\"\r\n          defaultTheme=\"system\"\r\n          enableSystem\r\n          disableTransitionOnChange\r\n        >\r\n          <div className=\"min-h-screen flex flex-col\">\r\n            <Header />\r\n            <main className=\"flex-1 pt-20\">{children}</main>\r\n            <Footer />\r\n          </div>\r\n        </ThemeProvider>\r\n      </body>\r\n    </html>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;;;AAEA;AACA;AACA;;;;;;AAGO,MAAM,WAAqB;IAChC,OAAO;IACP,aAAa;AACf;AAEe,SAAS,WAAW,EACjC,QAAQ,EAGR;IACA,qBACE,wPAAC;QAAK,MAAK;QAAK,0BAA0B;kBACxC,cAAA,wPAAC;YAAK,wBAAwB;sBAC5B,cAAA,wPAAC,2KAAa;gBACZ,WAAU;gBACV,cAAa;gBACb,YAAY;gBACZ,yBAAyB;0BAEzB,cAAA,wPAAC;oBAAI,WAAU;;sCACb,wPAAC,yJAAM;;;;;sCACP,wPAAC;4BAAK,WAAU;sCAAgB;;;;;;sCAChC,wPAAC,yJAAM;;;;;;;;;;;;;;;;;;;;;;;;;;AAMnB", "debugId": null}}]}