import React from 'react';
import PostCard from '@/components/postCard/page';
import { fetchPosts, transformPostForCard } from '@/lib/api';
import { Post } from '@/types/api';

// Server Component - runs on the server, great for SEO
export default async function Home() {
  // Fetch posts on the server side
  const { data: posts, error } = await fetchPosts();

  // Handle error state
  if (error) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl font-bold mb-4 text-red-600">
            Error Loading Posts
          </h1>
          <p className="text-lg text-muted-foreground">{error.message}</p>
          <p className="text-sm text-muted-foreground mt-2">
            Make sure your backend server is running on http://localhost:4000
          </p>
        </div>
      </main>
    );
  }

  // Handle empty state
  if (!posts || posts.length === 0) {
    return (
      <main className="container mx-auto px-4 py-8">
        <div className="text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-4">
            Welcome to Our Blog
          </h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto mb-8">
            Discover insights, tutorials, and thoughts on web development,
            design, and technology.
          </p>
          <p className="text-lg text-muted-foreground">
            No posts available yet. Check back soon!
          </p>
        </div>
      </main>
    );
  }

  return (
    <main className="container mx-auto px-4 py-8">
      <div className="text-center mb-12">
        <h1 className="text-4xl md:text-6xl font-bold mb-4">
          Welcome to Our Blog
        </h1>
        <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
          Discover insights, tutorials, and thoughts on web development, design,
          and technology.
        </p>
        <p className="text-sm text-muted-foreground mt-4">
          Showing {posts.length} article{posts.length !== 1 ? 's' : ''}
        </p>
      </div>

      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {posts.map((post: Post) => (
          <PostCard key={post.id} post={transformPostForCard(post)} />
        ))}
      </div>
    </main>
  );
}
