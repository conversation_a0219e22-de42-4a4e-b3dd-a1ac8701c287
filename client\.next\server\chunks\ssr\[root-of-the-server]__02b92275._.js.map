{"version": 3, "sources": [], "sections": [{"offset": {"line": 3, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 11, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,IAAA,iLAAG,EACvB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,kLAAI,GAAG;IAE9B,qBACE,wPAAC;QACC,aAAU;QACV,WAAW,IAAA,mIAAE,EAAC,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 57, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/components/postCard/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport Link from 'next/link';\r\nimport { Eye, User } from 'lucide-react';\r\nimport { Badge } from '@/components/ui/badge';\r\n\r\ninterface PostCardProps {\r\n  post: {\r\n    id: string;\r\n    title: string;\r\n    excerpt: string;\r\n    date: string;\r\n    slug: string;\r\n    author: string;\r\n    category?: string;\r\n    tags?: string[];\r\n    viewCount?: number;\r\n  };\r\n}\r\n\r\nconst PostCard = ({ post }: PostCardProps) => {\r\n  const {\r\n    title,\r\n    excerpt,\r\n    date,\r\n    slug,\r\n    author,\r\n    category = 'General',\r\n    tags = [],\r\n    viewCount = 0,\r\n  } = post;\r\n  return (\r\n    <Link href={`/articles/${slug}`}>\r\n      <article className=\"group bg-card border border-border p-6 hover:gradient-border transition-all duration-300 h-full flex flex-col\">\r\n        <div className=\"space-y-4 flex-1\">\r\n          {/* Category and View Count */}\r\n          <div className=\"flex items-center justify-between\">\r\n            <Badge variant=\"secondary\" className=\"text-xs\">\r\n              {category}\r\n            </Badge>\r\n            {viewCount > 0 && (\r\n              <div className=\"flex items-center space-x-1 text-xs text-muted-foreground\">\r\n                <Eye className=\"h-3 w-3\" />\r\n                <span>{viewCount.toLocaleString()}</span>\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Title */}\r\n          <h2 className=\"text-xl font-bold text-card-foreground group-hover:gradient-text transition-colors line-clamp-2\">\r\n            {title}\r\n          </h2>\r\n\r\n          {/* Excerpt */}\r\n          <p className=\"text-muted-foreground leading-relaxed line-clamp-3 flex-1\">\r\n            {excerpt}\r\n          </p>\r\n        </div>\r\n\r\n        {/* Footer */}\r\n        <div className=\"mt-4 pt-4 border-t border-border space-y-3\">\r\n          {/* Author and Date */}\r\n          <div className=\"flex items-center justify-between text-sm\">\r\n            <div className=\"flex items-center space-x-2 text-secondary-foreground\">\r\n              <User className=\"h-3 w-3\" />\r\n              <span>{author}</span>\r\n            </div>\r\n            <time className=\"text-secondary-foreground font-mono\">{date}</time>\r\n          </div>\r\n\r\n          {/* Tags */}\r\n          {tags.length > 0 && (\r\n            <div className=\"flex flex-wrap gap-2\">\r\n              {tags.slice(0, 3).map((tag) => (\r\n                <Badge key={tag} variant=\"outline\" className=\"text-xs\">\r\n                  {tag}\r\n                </Badge>\r\n              ))}\r\n              {tags.length > 3 && (\r\n                <Badge variant=\"outline\" className=\"text-xs\">\r\n                  +{tags.length - 3}\r\n                </Badge>\r\n              )}\r\n            </div>\r\n          )}\r\n        </div>\r\n      </article>\r\n    </Link>\r\n  );\r\n};\r\n\r\nexport default PostCard;\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAAA;AACA;;;;;AAgBA,MAAM,WAAW,CAAC,EAAE,IAAI,EAAiB;IACvC,MAAM,EACJ,KAAK,EACL,OAAO,EACP,IAAI,EACJ,IAAI,EACJ,MAAM,EACN,WAAW,SAAS,EACpB,OAAO,EAAE,EACT,YAAY,CAAC,EACd,GAAG;IACJ,qBACE,wPAAC,iLAAI;QAAC,MAAM,CAAC,UAAU,EAAE,MAAM;kBAC7B,cAAA,wPAAC;YAAQ,WAAU;;8BACjB,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAClC;;;;;;gCAEF,YAAY,mBACX,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,iNAAG;4CAAC,WAAU;;;;;;sDACf,wPAAC;sDAAM,UAAU,cAAc;;;;;;;;;;;;;;;;;;sCAMrC,wPAAC;4BAAG,WAAU;sCACX;;;;;;sCAIH,wPAAC;4BAAE,WAAU;sCACV;;;;;;;;;;;;8BAKL,wPAAC;oBAAI,WAAU;;sCAEb,wPAAC;4BAAI,WAAU;;8CACb,wPAAC;oCAAI,WAAU;;sDACb,wPAAC,oNAAI;4CAAC,WAAU;;;;;;sDAChB,wPAAC;sDAAM;;;;;;;;;;;;8CAET,wPAAC;oCAAK,WAAU;8CAAuC;;;;;;;;;;;;wBAIxD,KAAK,MAAM,GAAG,mBACb,wPAAC;4BAAI,WAAU;;gCACZ,KAAK,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,oBACrB,wPAAC,oJAAK;wCAAW,SAAQ;wCAAU,WAAU;kDAC1C;uCADS;;;;;gCAIb,KAAK,MAAM,GAAG,mBACb,wPAAC,oJAAK;oCAAC,SAAQ;oCAAU,WAAU;;wCAAU;wCACzC,KAAK,MAAM,GAAG;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AASlC;uCAEe", "debugId": null}}, {"offset": {"line": 239, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/lib/api.ts"], "sourcesContent": ["import { Post, ApiResponse } from '@/types/api';\n\n// Base API URL - adjust this to match your backend server\nconst API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:4000';\n\n/**\n * Generic API request function with error handling\n */\nasync function apiRequest<T>(\n  endpoint: string,\n  options: RequestInit = {}\n): Promise<ApiResponse<T>> {\n  try {\n    const url = `${API_BASE_URL}${endpoint}`;\n\n    const config: RequestInit = {\n      headers: {\n        'Content-Type': 'application/json',\n        ...options.headers,\n      },\n      // Include credentials for authentication cookies\n      credentials: 'include',\n      ...options,\n    };\n\n    console.log(`🌐 Making API request to: ${url}`);\n\n    const response = await fetch(url, config);\n\n    // Handle non-200 responses\n    if (!response.ok) {\n      const errorText = await response.text();\n      console.error(`❌ API Error (${response.status}):`, errorText);\n\n      return {\n        error: {\n          message:\n            errorText || `HTTP ${response.status}: ${response.statusText}`,\n          status: response.status,\n        },\n      };\n    }\n\n    const data = await response.json();\n    console.log(`✅ API Success:`, data);\n\n    return { data };\n  } catch (error) {\n    console.error('❌ Network Error:', error);\n\n    return {\n      error: {\n        message:\n          error instanceof Error ? error.message : 'Network error occurred',\n        status: 0,\n      },\n    };\n  }\n}\n\n/**\n * Fetch all posts from the backend\n */\nexport async function fetchPosts(): Promise<ApiResponse<Post[]>> {\n  return apiRequest<Post[]>('/api/posts');\n}\n\n/**\n * Fetch a single post by ID\n */\nexport async function fetchPost(id: string): Promise<ApiResponse<Post>> {\n  return apiRequest<Post>(`/api/posts/${id}`);\n}\n\n/**\n * Create a new post (requires admin authentication)\n */\nexport async function createPost(postData: {\n  title: string;\n  content: string;\n  published: boolean;\n  imageURL?: string;\n}): Promise<ApiResponse<Post>> {\n  return apiRequest<Post>('/api/posts', {\n    method: 'POST',\n    body: JSON.stringify(postData),\n  });\n}\n\n// Utility functions for data transformation\n\n/**\n * Create a slug from a title\n */\nfunction createSlug(title: string): string {\n  return title\n    .toLowerCase()\n    .replace(/[^a-z0-9]+/g, '-')\n    .replace(/(^-|-$)/g, '');\n}\n\n/**\n * Create an excerpt from content\n */\nfunction createExcerpt(\n  content: string | null,\n  maxLength: number = 150\n): string {\n  if (!content) return 'No content available.';\n\n  // Remove HTML tags if any\n  const plainText = content.replace(/<[^>]*>/g, '');\n\n  if (plainText.length <= maxLength) return plainText;\n\n  return plainText.substring(0, maxLength).trim() + '...';\n}\n\n/**\n * Format date for display\n */\nfunction formatDate(dateString: string | null): string {\n  if (!dateString) return 'No date';\n\n  const date = new Date(dateString);\n  return date.toLocaleDateString('en-US', {\n    year: 'numeric',\n    month: 'short',\n    day: 'numeric',\n  });\n}\n\n/**\n * Transform API Post to PostCard format\n */\nexport function transformPostForCard(post: Post) {\n  return {\n    id: post.id,\n    title: post.title,\n    excerpt: createExcerpt(post.content),\n    date: formatDate(post.publishedAt || post.createdAt),\n    slug: createSlug(post.title),\n    author: post.author.name || post.author.email,\n    category: 'General', // You can add category logic later\n    tags: [], // You can add tags logic later\n    viewCount: 0, // You can add view count from your backend later\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA,0DAA0D;AAC1D,MAAM,eAAe,6DAAmC;AAExD;;CAEC,GACD,eAAe,WACb,QAAgB,EAChB,UAAuB,CAAC,CAAC;IAEzB,IAAI;QACF,MAAM,MAAM,GAAG,eAAe,UAAU;QAExC,MAAM,SAAsB;YAC1B,SAAS;gBACP,gBAAgB;gBAChB,GAAG,QAAQ,OAAO;YACpB;YACA,iDAAiD;YACjD,aAAa;YACb,GAAG,OAAO;QACZ;QAEA,QAAQ,GAAG,CAAC,CAAC,0BAA0B,EAAE,KAAK;QAE9C,MAAM,WAAW,MAAM,MAAM,KAAK;QAElC,2BAA2B;QAC3B,IAAI,CAAC,SAAS,EAAE,EAAE;YAChB,MAAM,YAAY,MAAM,SAAS,IAAI;YACrC,QAAQ,KAAK,CAAC,CAAC,aAAa,EAAE,SAAS,MAAM,CAAC,EAAE,CAAC,EAAE;YAEnD,OAAO;gBACL,OAAO;oBACL,SACE,aAAa,CAAC,KAAK,EAAE,SAAS,MAAM,CAAC,EAAE,EAAE,SAAS,UAAU,EAAE;oBAChE,QAAQ,SAAS,MAAM;gBACzB;YACF;QACF;QAEA,MAAM,OAAO,MAAM,SAAS,IAAI;QAChC,QAAQ,GAAG,CAAC,CAAC,cAAc,CAAC,EAAE;QAE9B,OAAO;YAAE;QAAK;IAChB,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAElC,OAAO;YACL,OAAO;gBACL,SACE,iBAAiB,QAAQ,MAAM,OAAO,GAAG;gBAC3C,QAAQ;YACV;QACF;IACF;AACF;AAKO,eAAe;IACpB,OAAO,WAAmB;AAC5B;AAKO,eAAe,UAAU,EAAU;IACxC,OAAO,WAAiB,CAAC,WAAW,EAAE,IAAI;AAC5C;AAKO,eAAe,WAAW,QAKhC;IACC,OAAO,WAAiB,cAAc;QACpC,QAAQ;QACR,MAAM,KAAK,SAAS,CAAC;IACvB;AACF;AAEA,4CAA4C;AAE5C;;CAEC,GACD,SAAS,WAAW,KAAa;IAC/B,OAAO,MACJ,WAAW,GACX,OAAO,CAAC,eAAe,KACvB,OAAO,CAAC,YAAY;AACzB;AAEA;;CAEC,GACD,SAAS,cACP,OAAsB,EACtB,YAAoB,GAAG;IAEvB,IAAI,CAAC,SAAS,OAAO;IAErB,0BAA0B;IAC1B,MAAM,YAAY,QAAQ,OAAO,CAAC,YAAY;IAE9C,IAAI,UAAU,MAAM,IAAI,WAAW,OAAO;IAE1C,OAAO,UAAU,SAAS,CAAC,GAAG,WAAW,IAAI,KAAK;AACpD;AAEA;;CAEC,GACD,SAAS,WAAW,UAAyB;IAC3C,IAAI,CAAC,YAAY,OAAO;IAExB,MAAM,OAAO,IAAI,KAAK;IACtB,OAAO,KAAK,kBAAkB,CAAC,SAAS;QACtC,MAAM;QACN,OAAO;QACP,KAAK;IACP;AACF;AAKO,SAAS,qBAAqB,IAAU;IAC7C,OAAO;QACL,IAAI,KAAK,EAAE;QACX,OAAO,KAAK,KAAK;QACjB,SAAS,cAAc,KAAK,OAAO;QACnC,MAAM,WAAW,KAAK,WAAW,IAAI,KAAK,SAAS;QACnD,MAAM,WAAW,KAAK,KAAK;QAC3B,QAAQ,KAAK,MAAM,CAAC,IAAI,IAAI,KAAK,MAAM,CAAC,KAAK;QAC7C,UAAU;QACV,MAAM,EAAE;QACR,WAAW;IACb;AACF", "debugId": null}}, {"offset": {"line": 348, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/Internship-Projects/The-Salty-Devs/client/src/app/page.tsx"], "sourcesContent": ["import React from 'react';\r\nimport PostCard from '@/components/postCard/page';\r\nimport { fetchPosts, transformPostForCard } from '@/lib/api';\r\nimport { Post } from '@/types/api';\r\n\r\n// Server Component - runs on the server, great for SEO\r\nexport default async function Home() {\r\n  // Fetch posts on the server side\r\n  const { data: posts, error } = await fetchPosts();\r\n\r\n  // Handle error state\r\n  if (error) {\r\n    return (\r\n      <main className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-4xl font-bold mb-4 text-red-600\">\r\n            Error Loading Posts\r\n          </h1>\r\n          <p className=\"text-lg text-muted-foreground\">{error.message}</p>\r\n          <p className=\"text-sm text-muted-foreground mt-2\">\r\n            Make sure your backend server is running on http://localhost:4000\r\n          </p>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  // Handle empty state\r\n  if (!posts || posts.length === 0) {\r\n    return (\r\n      <main className=\"container mx-auto px-4 py-8\">\r\n        <div className=\"text-center\">\r\n          <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\r\n            Welcome to Our Blog\r\n          </h1>\r\n          <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto mb-8\">\r\n            Discover insights, tutorials, and thoughts on web development,\r\n            design, and technology.\r\n          </p>\r\n          <p className=\"text-lg text-muted-foreground\">\r\n            No posts available yet. Check back soon!\r\n          </p>\r\n        </div>\r\n      </main>\r\n    );\r\n  }\r\n\r\n  return (\r\n    <main className=\"container mx-auto px-4 py-8\">\r\n      <div className=\"text-center mb-12\">\r\n        <h1 className=\"text-4xl md:text-6xl font-bold mb-4\">\r\n          Welcome to Our Blog\r\n        </h1>\r\n        <p className=\"text-xl text-muted-foreground max-w-2xl mx-auto\">\r\n          Discover insights, tutorials, and thoughts on web development, design,\r\n          and technology.\r\n        </p>\r\n        <p className=\"text-sm text-muted-foreground mt-4\">\r\n          Showing {posts.length} article{posts.length !== 1 ? 's' : ''}\r\n        </p>\r\n      </div>\r\n\r\n      <div className=\"grid gap-6 md:grid-cols-2 lg:grid-cols-3\">\r\n        {posts.map((post: Post) => (\r\n          <PostCard key={post.id} post={transformPostForCard(post)} />\r\n        ))}\r\n      </div>\r\n    </main>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;;AACA;AACA;;;;AAIe,eAAe;IAC5B,iCAAiC;IACjC,MAAM,EAAE,MAAM,KAAK,EAAE,KAAK,EAAE,GAAG,MAAM,IAAA,yIAAU;IAE/C,qBAAqB;IACrB,IAAI,OAAO;QACT,qBACE,wPAAC;YAAK,WAAU;sBACd,cAAA,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAuC;;;;;;kCAGrD,wPAAC;wBAAE,WAAU;kCAAiC,MAAM,OAAO;;;;;;kCAC3D,wPAAC;wBAAE,WAAU;kCAAqC;;;;;;;;;;;;;;;;;IAM1D;IAEA,qBAAqB;IACrB,IAAI,CAAC,SAAS,MAAM,MAAM,KAAK,GAAG;QAChC,qBACE,wPAAC;YAAK,WAAU;sBACd,cAAA,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,wPAAC;wBAAE,WAAU;kCAAuD;;;;;;kCAIpE,wPAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAMrD;IAEA,qBACE,wPAAC;QAAK,WAAU;;0BACd,wPAAC;gBAAI,WAAU;;kCACb,wPAAC;wBAAG,WAAU;kCAAsC;;;;;;kCAGpD,wPAAC;wBAAE,WAAU;kCAAkD;;;;;;kCAI/D,wPAAC;wBAAE,WAAU;;4BAAqC;4BACvC,MAAM,MAAM;4BAAC;4BAAS,MAAM,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;0BAI9D,wPAAC;gBAAI,WAAU;0BACZ,MAAM,GAAG,CAAC,CAAC,qBACV,wPAAC,2JAAQ;wBAAe,MAAM,IAAA,mJAAoB,EAAC;uBAApC,KAAK,EAAE;;;;;;;;;;;;;;;;AAKhC", "debugId": null}}]}