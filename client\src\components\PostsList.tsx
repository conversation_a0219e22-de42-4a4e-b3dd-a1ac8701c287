'use client';

import React, { useState, useEffect } from 'react';
import PostCard from '@/components/postCard/page';
import { fetchPosts, transformPostForCard } from '@/lib/api';
import { Post } from '@/types/api';
import { Loader2, RefreshCw } from 'lucide-react';
import { Button } from '@/components/ui/button';

interface PostsListProps {
  initialPosts?: Post[];
}

export default function PostsList({ initialPosts }: PostsListProps) {
  const [posts, setPosts] = useState<Post[]>(initialPosts || []);
  const [loading, setLoading] = useState(!initialPosts);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Fetch posts function
  const loadPosts = async (isRefresh = false) => {
    if (isRefresh) {
      setRefreshing(true);
    } else {
      setLoading(true);
    }
    
    setError(null);

    try {
      const { data, error: apiError } = await fetchPosts();
      
      if (apiError) {
        setError(apiError.message);
      } else if (data) {
        setPosts(data);
      }
    } catch (err) {
      setError('Failed to load posts');
      console.error('Error loading posts:', err);
    } finally {
      setLoading(false);
      setRefreshing(false);
    }
  };

  // Load posts on component mount (only if no initial posts)
  useEffect(() => {
    if (!initialPosts) {
      loadPosts();
    }
  }, [initialPosts]);

  // Handle refresh
  const handleRefresh = () => {
    loadPosts(true);
  };

  // Loading state
  if (loading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p className="text-muted-foreground">Loading posts...</p>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4 text-red-600">
          Error Loading Posts
        </h2>
        <p className="text-muted-foreground mb-6">{error}</p>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Try Again
        </Button>
      </div>
    );
  }

  // Empty state
  if (posts.length === 0) {
    return (
      <div className="text-center py-12">
        <h2 className="text-2xl font-bold mb-4">No Posts Found</h2>
        <p className="text-muted-foreground mb-6">
          No posts are available at the moment.
        </p>
        <Button onClick={handleRefresh} variant="outline">
          <RefreshCw className="h-4 w-4 mr-2" />
          Refresh
        </Button>
      </div>
    );
  }

  // Success state
  return (
    <div className="space-y-6">
      {/* Header with refresh button */}
      <div className="flex items-center justify-between">
        <p className="text-sm text-muted-foreground">
          Showing {posts.length} article{posts.length !== 1 ? 's' : ''}
        </p>
        <Button
          onClick={handleRefresh}
          variant="ghost"
          size="sm"
          disabled={refreshing}
        >
          <RefreshCw className={`h-4 w-4 mr-2 ${refreshing ? 'animate-spin' : ''}`} />
          Refresh
        </Button>
      </div>

      {/* Posts grid */}
      <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
        {posts.map((post: Post) => (
          <PostCard key={post.id} post={transformPostForCard(post)} />
        ))}
      </div>
    </div>
  );
}
