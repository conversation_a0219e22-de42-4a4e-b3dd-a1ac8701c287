var Wc=Object.create;var Jr=Object.defineProperty;var Kc=Object.getOwnPropertyDescriptor;var zc=Object.getOwnPropertyNames;var Yc=Object.getPrototypeOf,Zc=Object.prototype.hasOwnProperty;var ge=(e,t)=>()=>(e&&(t=e(e=0)),t);var ue=(e,t)=>()=>(t||e((t={exports:{}}).exports,t),t.exports),Gt=(e,t)=>{for(var r in t)Jr(e,r,{get:t[r],enumerable:!0})},Lo=(e,t,r,n)=>{if(t&&typeof t=="object"||typeof t=="function")for(let i of zc(t))!Zc.call(e,i)&&i!==r&&Jr(e,i,{get:()=>t[i],enumerable:!(n=Kc(t,i))||n.enumerable});return e};var $e=(e,t,r)=>(r=e!=null?Wc(Yc(e)):{},Lo(t||!e||!e.__esModule?Jr(r,"default",{value:e,enumerable:!0}):r,e)),Xc=e=>Lo(Jr({},"__esModule",{value:!0}),e);function ii(e,t){if(t=t.toLowerCase(),t==="utf8"||t==="utf-8")return new h(nu.encode(e));if(t==="base64"||t==="base64url")return e=e.replace(/-/g,"+").replace(/_/g,"/"),e=e.replace(/[^A-Za-z0-9+/]/g,""),new h([...atob(e)].map(r=>r.charCodeAt(0)));if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return new h([...e].map(r=>r.charCodeAt(0)));if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let r=new h(e.length*2),n=new DataView(r.buffer);for(let i=0;i<e.length;i++)n.setUint16(i*2,e.charCodeAt(i),!0);return r}if(t==="hex"){let r=new h(e.length/2);for(let n=0,i=0;i<e.length;i+=2,n++)r[n]=parseInt(e.slice(i,i+2),16);return r}Fo(`encoding "${t}"`)}function eu(e){let r=Object.getOwnPropertyNames(DataView.prototype).filter(a=>a.startsWith("get")||a.startsWith("set")),n=r.map(a=>a.replace("get","read").replace("set","write")),i=(a,f)=>function(E=0){return Y(E,"offset"),pe(E,"offset"),X(E,"offset",this.length-1),new DataView(this.buffer)[r[a]](E,f)},o=(a,f)=>function(E,A=0){let R=r[a].match(/set(\w+\d+)/)[1].toLowerCase(),S=ru[R];return Y(A,"offset"),pe(A,"offset"),X(A,"offset",this.length-1),tu(E,"value",S[0],S[1]),new DataView(this.buffer)[r[a]](A,E,f),A+parseInt(r[a].match(/\d+/)[0])/8},s=a=>{a.forEach(f=>{f.includes("Uint")&&(e[f.replace("Uint","UInt")]=e[f]),f.includes("Float64")&&(e[f.replace("Float64","Double")]=e[f]),f.includes("Float32")&&(e[f.replace("Float32","Float")]=e[f])})};n.forEach((a,f)=>{a.startsWith("read")&&(e[a]=i(f,!1),e[a+"LE"]=i(f,!0),e[a+"BE"]=i(f,!1)),a.startsWith("write")&&(e[a]=o(f,!1),e[a+"LE"]=o(f,!0),e[a+"BE"]=o(f,!1)),s([a,a+"LE",a+"BE"])})}function Fo(e){throw new Error(`Buffer polyfill does not implement "${e}"`)}function Wr(e,t){if(!(e instanceof Uint8Array))throw new TypeError(`The "${t}" argument must be an instance of Buffer or Uint8Array`)}function X(e,t,r=su+1){if(e<0||e>r){let n=new RangeError(`The value of "${t}" is out of range. It must be >= 0 && <= ${r}. Received ${e}`);throw n.code="ERR_OUT_OF_RANGE",n}}function Y(e,t){if(typeof e!="number"){let r=new TypeError(`The "${t}" argument must be of type number. Received type ${typeof e}.`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function pe(e,t){if(!Number.isInteger(e)||Number.isNaN(e)){let r=new RangeError(`The value of "${t}" is out of range. It must be an integer. Received ${e}`);throw r.code="ERR_OUT_OF_RANGE",r}}function tu(e,t,r,n){if(e<r||e>n){let i=new RangeError(`The value of "${t}" is out of range. It must be >= ${r} and <= ${n}. Received ${e}`);throw i.code="ERR_OUT_OF_RANGE",i}}function Uo(e,t){if(typeof e!="string"){let r=new TypeError(`The "${t}" argument must be of type string. Received type ${typeof e}`);throw r.code="ERR_INVALID_ARG_TYPE",r}}function au(e,t="utf8"){return h.from(e,t)}var h,ru,nu,iu,ou,su,y,oi,c=ge(()=>{"use strict";h=class e extends Uint8Array{_isBuffer=!0;get offset(){return this.byteOffset}static alloc(t,r=0,n="utf8"){return Uo(n,"encoding"),e.allocUnsafe(t).fill(r,n)}static allocUnsafe(t){return e.from(t)}static allocUnsafeSlow(t){return e.from(t)}static isBuffer(t){return t&&!!t._isBuffer}static byteLength(t,r="utf8"){if(typeof t=="string")return ii(t,r).byteLength;if(t&&t.byteLength)return t.byteLength;let n=new TypeError('The "string" argument must be of type string or an instance of Buffer or ArrayBuffer.');throw n.code="ERR_INVALID_ARG_TYPE",n}static isEncoding(t){return ou.includes(t)}static compare(t,r){Wr(t,"buff1"),Wr(r,"buff2");for(let n=0;n<t.length;n++){if(t[n]<r[n])return-1;if(t[n]>r[n])return 1}return t.length===r.length?0:t.length>r.length?1:-1}static from(t,r="utf8"){if(t&&typeof t=="object"&&t.type==="Buffer")return new e(t.data);if(typeof t=="number")return new e(new Uint8Array(t));if(typeof t=="string")return ii(t,r);if(ArrayBuffer.isView(t)){let{byteOffset:n,byteLength:i,buffer:o}=t;return"map"in t&&typeof t.map=="function"?new e(t.map(s=>s%256),n,i):new e(o,n,i)}if(t&&typeof t=="object"&&("length"in t||"byteLength"in t||"buffer"in t))return new e(t);throw new TypeError("First argument must be a string, Buffer, ArrayBuffer, Array, or array-like object.")}static concat(t,r){if(t.length===0)return e.alloc(0);let n=[].concat(...t.map(o=>[...o])),i=e.alloc(r!==void 0?r:n.length);return i.set(r!==void 0?n.slice(0,r):n),i}slice(t=0,r=this.length){return this.subarray(t,r)}subarray(t=0,r=this.length){return Object.setPrototypeOf(super.subarray(t,r),e.prototype)}reverse(){return super.reverse(),this}readIntBE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return n.getUint8(0)&128&&(i-=Math.pow(256,r)),i}readIntLE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return n.getUint8(r-1)&128&&(i-=Math.pow(256,r)),i}readUIntBE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i=i*256+n.getUint8(o);return i}readUintBE(t,r){return this.readUIntBE(t,r)}readUIntLE(t,r){Y(t,"offset"),pe(t,"offset"),X(t,"offset",this.length-1),Y(r,"byteLength"),pe(r,"byteLength");let n=new DataView(this.buffer,t,r),i=0;for(let o=0;o<r;o++)i+=n.getUint8(o)*Math.pow(256,o);return i}readUintLE(t,r){return this.readUIntLE(t,r)}writeIntBE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntBE(t,r,n)}writeIntLE(t,r,n){return t=t<0?t+Math.pow(256,n):t,this.writeUIntLE(t,r,n)}writeUIntBE(t,r,n){Y(r,"offset"),pe(r,"offset"),X(r,"offset",this.length-1),Y(n,"byteLength"),pe(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=n-1;o>=0;o--)i.setUint8(o,t&255),t=t/256;return r+n}writeUintBE(t,r,n){return this.writeUIntBE(t,r,n)}writeUIntLE(t,r,n){Y(r,"offset"),pe(r,"offset"),X(r,"offset",this.length-1),Y(n,"byteLength"),pe(n,"byteLength");let i=new DataView(this.buffer,r,n);for(let o=0;o<n;o++)i.setUint8(o,t&255),t=t/256;return r+n}writeUintLE(t,r,n){return this.writeUIntLE(t,r,n)}toJSON(){return{type:"Buffer",data:Array.from(this)}}swap16(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=2)t.setUint16(r,t.getUint16(r,!0),!1);return this}swap32(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=4)t.setUint32(r,t.getUint32(r,!0),!1);return this}swap64(){let t=new DataView(this.buffer,this.byteOffset,this.byteLength);for(let r=0;r<this.length;r+=8)t.setBigUint64(r,t.getBigUint64(r,!0),!1);return this}compare(t,r=0,n=t.length,i=0,o=this.length){return Wr(t,"target"),Y(r,"targetStart"),Y(n,"targetEnd"),Y(i,"sourceStart"),Y(o,"sourceEnd"),X(r,"targetStart"),X(n,"targetEnd",t.length),X(i,"sourceStart"),X(o,"sourceEnd",this.length),e.compare(this.slice(i,o),t.slice(r,n))}equals(t){return Wr(t,"otherBuffer"),this.length===t.length&&this.every((r,n)=>r===t[n])}copy(t,r=0,n=0,i=this.length){X(r,"targetStart"),X(n,"sourceStart",this.length),X(i,"sourceEnd"),r>>>=0,n>>>=0,i>>>=0;let o=0;for(;n<i&&!(this[n]===void 0||t[r]===void 0);)t[r]=this[n],o++,n++,r++;return o}write(t,r,n,i="utf8"){let o=typeof r=="string"?0:r??0,s=typeof n=="string"?this.length-o:n??this.length-o;return i=typeof r=="string"?r:typeof n=="string"?n:i,Y(o,"offset"),Y(s,"length"),X(o,"offset",this.length),X(s,"length",this.length),(i==="ucs2"||i==="ucs-2"||i==="utf16le"||i==="utf-16le")&&(s=s-s%2),ii(t,i).copy(this,o,0,s)}fill(t=0,r=0,n=this.length,i="utf-8"){let o=typeof r=="string"?0:r,s=typeof n=="string"?this.length:n;if(i=typeof r=="string"?r:typeof n=="string"?n:i,t=e.from(typeof t=="number"?[t]:t??[],i),Uo(i,"encoding"),X(o,"offset",this.length),X(s,"end",this.length),t.length!==0)for(let a=o;a<s;a+=t.length)super.set(t.slice(0,t.length+a>=this.length?this.length-a:t.length),a);return this}includes(t,r=null,n="utf-8"){return this.indexOf(t,r,n)!==-1}lastIndexOf(t,r=null,n="utf-8"){return this.indexOf(t,r,n,!0)}indexOf(t,r=null,n="utf-8",i=!1){let o=i?this.findLastIndex.bind(this):this.findIndex.bind(this);n=typeof r=="string"?r:n;let s=e.from(typeof t=="number"?[t]:t,n),a=typeof r=="string"?0:r;return a=typeof r=="number"?a:null,a=Number.isNaN(a)?null:a,a??=i?this.length:0,a=a<0?this.length+a:a,s.length===0&&i===!1?a>=this.length?this.length:a:s.length===0&&i===!0?(a>=this.length?this.length:a)||this.length:o((f,E)=>(i?E<=a:E>=a)&&this[E]===s[0]&&s.every((R,S)=>this[E+S]===R))}toString(t="utf8",r=0,n=this.length){if(r=r<0?0:r,t=t.toString().toLowerCase(),n<=0)return"";if(t==="utf8"||t==="utf-8")return iu.decode(this.slice(r,n));if(t==="base64"||t==="base64url"){let i=btoa(this.reduce((o,s)=>o+oi(s),""));return t==="base64url"?i.replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,""):i}if(t==="binary"||t==="ascii"||t==="latin1"||t==="latin-1")return this.slice(r,n).reduce((i,o)=>i+oi(o&(t==="ascii"?127:255)),"");if(t==="ucs2"||t==="ucs-2"||t==="utf16le"||t==="utf-16le"){let i=new DataView(this.buffer.slice(r,n));return Array.from({length:i.byteLength/2},(o,s)=>s*2+1<i.byteLength?oi(i.getUint16(s*2,!0)):"").join("")}if(t==="hex")return this.slice(r,n).reduce((i,o)=>i+o.toString(16).padStart(2,"0"),"");Fo(`encoding "${t}"`)}toLocaleString(){return this.toString()}inspect(){return`<Buffer ${this.toString("hex").match(/.{1,2}/g).join(" ")}>`}};ru={int8:[-128,127],int16:[-32768,32767],int32:[-2147483648,2147483647],uint8:[0,255],uint16:[0,65535],uint32:[0,4294967295],float32:[-1/0,1/0],float64:[-1/0,1/0],bigint64:[-0x8000000000000000n,0x7fffffffffffffffn],biguint64:[0n,0xffffffffffffffffn]},nu=new TextEncoder,iu=new TextDecoder,ou=["utf8","utf-8","hex","base64","ascii","binary","base64url","ucs2","ucs-2","utf16le","utf-16le","latin1","latin-1"],su=4294967295;eu(h.prototype);y=new Proxy(au,{construct(e,[t,r]){return h.from(t,r)},get(e,t){return h[t]}}),oi=String.fromCodePoint});var g,x,u=ge(()=>{"use strict";g={nextTick:(e,...t)=>{setTimeout(()=>{e(...t)},0)},env:{},version:"",cwd:()=>"/",stderr:{},argv:["/bin/node"],pid:1e4},{cwd:x}=g});var w,p=ge(()=>{"use strict";w=globalThis.performance??(()=>{let e=Date.now();return{now:()=>Date.now()-e}})()});var b,m=ge(()=>{"use strict";b=()=>{};b.prototype=b});var d=ge(()=>{"use strict"});function Bo(e,t){var r,n,i,o,s,a,f,E,A=e.constructor,R=A.precision;if(!e.s||!t.s)return t.s||(t=new A(e)),W?q(t,R):t;if(f=e.d,E=t.d,s=e.e,i=t.e,f=f.slice(),o=s-i,o){for(o<0?(n=f,o=-o,a=E.length):(n=E,i=s,a=f.length),s=Math.ceil(R/H),a=s>a?s+1:a+1,o>a&&(o=a,n.length=1),n.reverse();o--;)n.push(0);n.reverse()}for(a=f.length,o=E.length,a-o<0&&(o=a,n=E,E=f,f=n),r=0;o;)r=(f[--o]=f[o]+E[o]+r)/te|0,f[o]%=te;for(r&&(f.unshift(r),++i),a=f.length;f[--a]==0;)f.pop();return t.d=f,t.e=i,W?q(t,R):t}function Re(e,t,r){if(e!==~~e||e<t||e>r)throw Error(ze+e)}function Ae(e){var t,r,n,i=e.length-1,o="",s=e[0];if(i>0){for(o+=s,t=1;t<i;t++)n=e[t]+"",r=H-n.length,r&&(o+=Ve(r)),o+=n;s=e[t],n=s+"",r=H-n.length,r&&(o+=Ve(r))}else if(s===0)return"0";for(;s%10===0;)s/=10;return o+s}function jo(e,t){var r,n,i,o,s,a,f=0,E=0,A=e.constructor,R=A.precision;if(Z(e)>16)throw Error(ai+Z(e));if(!e.s)return new A(ye);for(t==null?(W=!1,a=R):a=t,s=new A(.03125);e.abs().gte(.1);)e=e.times(s),E+=5;for(n=Math.log(Ke(2,E))/Math.LN10*2+5|0,a+=n,r=i=o=new A(ye),A.precision=a;;){if(i=q(i.times(e),a),r=r.times(++f),s=o.plus(_e(i,r,a)),Ae(s.d).slice(0,a)===Ae(o.d).slice(0,a)){for(;E--;)o=q(o.times(o),a);return A.precision=R,t==null?(W=!0,q(o,R)):o}o=s}}function Z(e){for(var t=e.e*H,r=e.d[0];r>=10;r/=10)t++;return t}function si(e,t,r){if(t>e.LN10.sd())throw W=!0,r&&(e.precision=r),Error(be+"LN10 precision limit exceeded");return q(new e(e.LN10),t)}function Ve(e){for(var t="";e--;)t+="0";return t}function Jt(e,t){var r,n,i,o,s,a,f,E,A,R=1,S=10,C=e,L=C.d,k=C.constructor,M=k.precision;if(C.s<1)throw Error(be+(C.s?"NaN":"-Infinity"));if(C.eq(ye))return new k(0);if(t==null?(W=!1,E=M):E=t,C.eq(10))return t==null&&(W=!0),si(k,E);if(E+=S,k.precision=E,r=Ae(L),n=r.charAt(0),o=Z(C),Math.abs(o)<15e14){for(;n<7&&n!=1||n==1&&r.charAt(1)>3;)C=C.times(e),r=Ae(C.d),n=r.charAt(0),R++;o=Z(C),n>1?(C=new k("0."+r),o++):C=new k(n+"."+r.slice(1))}else return f=si(k,E+2,M).times(o+""),C=Jt(new k(n+"."+r.slice(1)),E-S).plus(f),k.precision=M,t==null?(W=!0,q(C,M)):C;for(a=s=C=_e(C.minus(ye),C.plus(ye),E),A=q(C.times(C),E),i=3;;){if(s=q(s.times(A),E),f=a.plus(_e(s,new k(i),E)),Ae(f.d).slice(0,E)===Ae(a.d).slice(0,E))return a=a.times(2),o!==0&&(a=a.plus(si(k,E+2,M).times(o+""))),a=_e(a,new k(R),E),k.precision=M,t==null?(W=!0,q(a,M)):a;a=f,i+=2}}function $o(e,t){var r,n,i;for((r=t.indexOf("."))>-1&&(t=t.replace(".","")),(n=t.search(/e/i))>0?(r<0&&(r=n),r+=+t.slice(n+1),t=t.substring(0,n)):r<0&&(r=t.length),n=0;t.charCodeAt(n)===48;)++n;for(i=t.length;t.charCodeAt(i-1)===48;)--i;if(t=t.slice(n,i),t){if(i-=n,r=r-n-1,e.e=mt(r/H),e.d=[],n=(r+1)%H,r<0&&(n+=H),n<i){for(n&&e.d.push(+t.slice(0,n)),i-=H;n<i;)e.d.push(+t.slice(n,n+=H));t=t.slice(n),n=H-t.length}else n-=i;for(;n--;)t+="0";if(e.d.push(+t),W&&(e.e>Kr||e.e<-Kr))throw Error(ai+r)}else e.s=0,e.e=0,e.d=[0];return e}function q(e,t,r){var n,i,o,s,a,f,E,A,R=e.d;for(s=1,o=R[0];o>=10;o/=10)s++;if(n=t-s,n<0)n+=H,i=t,E=R[A=0];else{if(A=Math.ceil((n+1)/H),o=R.length,A>=o)return e;for(E=o=R[A],s=1;o>=10;o/=10)s++;n%=H,i=n-H+s}if(r!==void 0&&(o=Ke(10,s-i-1),a=E/o%10|0,f=t<0||R[A+1]!==void 0||E%o,f=r<4?(a||f)&&(r==0||r==(e.s<0?3:2)):a>5||a==5&&(r==4||f||r==6&&(n>0?i>0?E/Ke(10,s-i):0:R[A-1])%10&1||r==(e.s<0?8:7))),t<1||!R[0])return f?(o=Z(e),R.length=1,t=t-o-1,R[0]=Ke(10,(H-t%H)%H),e.e=mt(-t/H)||0):(R.length=1,R[0]=e.e=e.s=0),e;if(n==0?(R.length=A,o=1,A--):(R.length=A+1,o=Ke(10,H-n),R[A]=i>0?(E/Ke(10,s-i)%Ke(10,i)|0)*o:0),f)for(;;)if(A==0){(R[0]+=o)==te&&(R[0]=1,++e.e);break}else{if(R[A]+=o,R[A]!=te)break;R[A--]=0,o=1}for(n=R.length;R[--n]===0;)R.pop();if(W&&(e.e>Kr||e.e<-Kr))throw Error(ai+Z(e));return e}function Qo(e,t){var r,n,i,o,s,a,f,E,A,R,S=e.constructor,C=S.precision;if(!e.s||!t.s)return t.s?t.s=-t.s:t=new S(e),W?q(t,C):t;if(f=e.d,R=t.d,n=t.e,E=e.e,f=f.slice(),s=E-n,s){for(A=s<0,A?(r=f,s=-s,a=R.length):(r=R,n=E,a=f.length),i=Math.max(Math.ceil(C/H),a)+2,s>i&&(s=i,r.length=1),r.reverse(),i=s;i--;)r.push(0);r.reverse()}else{for(i=f.length,a=R.length,A=i<a,A&&(a=i),i=0;i<a;i++)if(f[i]!=R[i]){A=f[i]<R[i];break}s=0}for(A&&(r=f,f=R,R=r,t.s=-t.s),a=f.length,i=R.length-a;i>0;--i)f[a++]=0;for(i=R.length;i>s;){if(f[--i]<R[i]){for(o=i;o&&f[--o]===0;)f[o]=te-1;--f[o],f[i]+=te}f[i]-=R[i]}for(;f[--a]===0;)f.pop();for(;f[0]===0;f.shift())--n;return f[0]?(t.d=f,t.e=n,W?q(t,C):t):new S(0)}function Ye(e,t,r){var n,i=Z(e),o=Ae(e.d),s=o.length;return t?(r&&(n=r-s)>0?o=o.charAt(0)+"."+o.slice(1)+Ve(n):s>1&&(o=o.charAt(0)+"."+o.slice(1)),o=o+(i<0?"e":"e+")+i):i<0?(o="0."+Ve(-i-1)+o,r&&(n=r-s)>0&&(o+=Ve(n))):i>=s?(o+=Ve(i+1-s),r&&(n=r-i-1)>0&&(o=o+"."+Ve(n))):((n=i+1)<s&&(o=o.slice(0,n)+"."+o.slice(n)),r&&(n=r-s)>0&&(i+1===s&&(o+="."),o+=Ve(n))),e.s<0?"-"+o:o}function Vo(e,t){if(e.length>t)return e.length=t,!0}function Ho(e){var t,r,n;function i(o){var s=this;if(!(s instanceof i))return new i(o);if(s.constructor=i,o instanceof i){s.s=o.s,s.e=o.e,s.d=(o=o.d)?o.slice():o;return}if(typeof o=="number"){if(o*0!==0)throw Error(ze+o);if(o>0)s.s=1;else if(o<0)o=-o,s.s=-1;else{s.s=0,s.e=0,s.d=[0];return}if(o===~~o&&o<1e7){s.e=0,s.d=[o];return}return $o(s,o.toString())}else if(typeof o!="string")throw Error(ze+o);if(o.charCodeAt(0)===45?(o=o.slice(1),s.s=-1):s.s=1,cu.test(o))$o(s,o);else throw Error(ze+o)}if(i.prototype=I,i.ROUND_UP=0,i.ROUND_DOWN=1,i.ROUND_CEIL=2,i.ROUND_FLOOR=3,i.ROUND_HALF_UP=4,i.ROUND_HALF_DOWN=5,i.ROUND_HALF_EVEN=6,i.ROUND_HALF_CEIL=7,i.ROUND_HALF_FLOOR=8,i.clone=Ho,i.config=i.set=uu,e===void 0&&(e={}),e)for(n=["precision","rounding","toExpNeg","toExpPos","LN10"],t=0;t<n.length;)e.hasOwnProperty(r=n[t++])||(e[r]=this[r]);return i.config(e),i}function uu(e){if(!e||typeof e!="object")throw Error(be+"Object expected");var t,r,n,i=["precision",1,pt,"rounding",0,8,"toExpNeg",-1/0,0,"toExpPos",0,1/0];for(t=0;t<i.length;t+=3)if((n=e[r=i[t]])!==void 0)if(mt(n)===n&&n>=i[t+1]&&n<=i[t+2])this[r]=n;else throw Error(ze+r+": "+n);if((n=e[r="LN10"])!==void 0)if(n==Math.LN10)this[r]=new this(n);else throw Error(ze+r+": "+n);return this}var pt,lu,li,W,be,ze,ai,mt,Ke,cu,ye,te,H,qo,Kr,I,_e,li,zr,Go=ge(()=>{"use strict";c();u();p();m();d();l();pt=1e9,lu={precision:20,rounding:4,toExpNeg:-7,toExpPos:21,LN10:"2.302585092994045684017991454684364207601101488628772976033327900967572609677352480235997205089598298341967784042286"},W=!0,be="[DecimalError] ",ze=be+"Invalid argument: ",ai=be+"Exponent out of range: ",mt=Math.floor,Ke=Math.pow,cu=/^(\d+(\.\d*)?|\.\d+)(e[+-]?\d+)?$/i,te=1e7,H=7,qo=9007199254740991,Kr=mt(qo/H),I={};I.absoluteValue=I.abs=function(){var e=new this.constructor(this);return e.s&&(e.s=1),e};I.comparedTo=I.cmp=function(e){var t,r,n,i,o=this;if(e=new o.constructor(e),o.s!==e.s)return o.s||-e.s;if(o.e!==e.e)return o.e>e.e^o.s<0?1:-1;for(n=o.d.length,i=e.d.length,t=0,r=n<i?n:i;t<r;++t)if(o.d[t]!==e.d[t])return o.d[t]>e.d[t]^o.s<0?1:-1;return n===i?0:n>i^o.s<0?1:-1};I.decimalPlaces=I.dp=function(){var e=this,t=e.d.length-1,r=(t-e.e)*H;if(t=e.d[t],t)for(;t%10==0;t/=10)r--;return r<0?0:r};I.dividedBy=I.div=function(e){return _e(this,new this.constructor(e))};I.dividedToIntegerBy=I.idiv=function(e){var t=this,r=t.constructor;return q(_e(t,new r(e),0,1),r.precision)};I.equals=I.eq=function(e){return!this.cmp(e)};I.exponent=function(){return Z(this)};I.greaterThan=I.gt=function(e){return this.cmp(e)>0};I.greaterThanOrEqualTo=I.gte=function(e){return this.cmp(e)>=0};I.isInteger=I.isint=function(){return this.e>this.d.length-2};I.isNegative=I.isneg=function(){return this.s<0};I.isPositive=I.ispos=function(){return this.s>0};I.isZero=function(){return this.s===0};I.lessThan=I.lt=function(e){return this.cmp(e)<0};I.lessThanOrEqualTo=I.lte=function(e){return this.cmp(e)<1};I.logarithm=I.log=function(e){var t,r=this,n=r.constructor,i=n.precision,o=i+5;if(e===void 0)e=new n(10);else if(e=new n(e),e.s<1||e.eq(ye))throw Error(be+"NaN");if(r.s<1)throw Error(be+(r.s?"NaN":"-Infinity"));return r.eq(ye)?new n(0):(W=!1,t=_e(Jt(r,o),Jt(e,o),o),W=!0,q(t,i))};I.minus=I.sub=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Qo(t,e):Bo(t,(e.s=-e.s,e))};I.modulo=I.mod=function(e){var t,r=this,n=r.constructor,i=n.precision;if(e=new n(e),!e.s)throw Error(be+"NaN");return r.s?(W=!1,t=_e(r,e,0,1).times(e),W=!0,r.minus(t)):q(new n(r),i)};I.naturalExponential=I.exp=function(){return jo(this)};I.naturalLogarithm=I.ln=function(){return Jt(this)};I.negated=I.neg=function(){var e=new this.constructor(this);return e.s=-e.s||0,e};I.plus=I.add=function(e){var t=this;return e=new t.constructor(e),t.s==e.s?Bo(t,e):Qo(t,(e.s=-e.s,e))};I.precision=I.sd=function(e){var t,r,n,i=this;if(e!==void 0&&e!==!!e&&e!==1&&e!==0)throw Error(ze+e);if(t=Z(i)+1,n=i.d.length-1,r=n*H+1,n=i.d[n],n){for(;n%10==0;n/=10)r--;for(n=i.d[0];n>=10;n/=10)r++}return e&&t>r?t:r};I.squareRoot=I.sqrt=function(){var e,t,r,n,i,o,s,a=this,f=a.constructor;if(a.s<1){if(!a.s)return new f(0);throw Error(be+"NaN")}for(e=Z(a),W=!1,i=Math.sqrt(+a),i==0||i==1/0?(t=Ae(a.d),(t.length+e)%2==0&&(t+="0"),i=Math.sqrt(t),e=mt((e+1)/2)-(e<0||e%2),i==1/0?t="5e"+e:(t=i.toExponential(),t=t.slice(0,t.indexOf("e")+1)+e),n=new f(t)):n=new f(i.toString()),r=f.precision,i=s=r+3;;)if(o=n,n=o.plus(_e(a,o,s+2)).times(.5),Ae(o.d).slice(0,s)===(t=Ae(n.d)).slice(0,s)){if(t=t.slice(s-3,s+1),i==s&&t=="4999"){if(q(o,r+1,0),o.times(o).eq(a)){n=o;break}}else if(t!="9999")break;s+=4}return W=!0,q(n,r)};I.times=I.mul=function(e){var t,r,n,i,o,s,a,f,E,A=this,R=A.constructor,S=A.d,C=(e=new R(e)).d;if(!A.s||!e.s)return new R(0);for(e.s*=A.s,r=A.e+e.e,f=S.length,E=C.length,f<E&&(o=S,S=C,C=o,s=f,f=E,E=s),o=[],s=f+E,n=s;n--;)o.push(0);for(n=E;--n>=0;){for(t=0,i=f+n;i>n;)a=o[i]+C[n]*S[i-n-1]+t,o[i--]=a%te|0,t=a/te|0;o[i]=(o[i]+t)%te|0}for(;!o[--s];)o.pop();return t?++r:o.shift(),e.d=o,e.e=r,W?q(e,R.precision):e};I.toDecimalPlaces=I.todp=function(e,t){var r=this,n=r.constructor;return r=new n(r),e===void 0?r:(Re(e,0,pt),t===void 0?t=n.rounding:Re(t,0,8),q(r,e+Z(r)+1,t))};I.toExponential=function(e,t){var r,n=this,i=n.constructor;return e===void 0?r=Ye(n,!0):(Re(e,0,pt),t===void 0?t=i.rounding:Re(t,0,8),n=q(new i(n),e+1,t),r=Ye(n,!0,e+1)),r};I.toFixed=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?Ye(i):(Re(e,0,pt),t===void 0?t=o.rounding:Re(t,0,8),n=q(new o(i),e+Z(i)+1,t),r=Ye(n.abs(),!1,e+Z(n)+1),i.isneg()&&!i.isZero()?"-"+r:r)};I.toInteger=I.toint=function(){var e=this,t=e.constructor;return q(new t(e),Z(e)+1,t.rounding)};I.toNumber=function(){return+this};I.toPower=I.pow=function(e){var t,r,n,i,o,s,a=this,f=a.constructor,E=12,A=+(e=new f(e));if(!e.s)return new f(ye);if(a=new f(a),!a.s){if(e.s<1)throw Error(be+"Infinity");return a}if(a.eq(ye))return a;if(n=f.precision,e.eq(ye))return q(a,n);if(t=e.e,r=e.d.length-1,s=t>=r,o=a.s,s){if((r=A<0?-A:A)<=qo){for(i=new f(ye),t=Math.ceil(n/H+4),W=!1;r%2&&(i=i.times(a),Vo(i.d,t)),r=mt(r/2),r!==0;)a=a.times(a),Vo(a.d,t);return W=!0,e.s<0?new f(ye).div(i):q(i,n)}}else if(o<0)throw Error(be+"NaN");return o=o<0&&e.d[Math.max(t,r)]&1?-1:1,a.s=1,W=!1,i=e.times(Jt(a,n+E)),W=!0,i=jo(i),i.s=o,i};I.toPrecision=function(e,t){var r,n,i=this,o=i.constructor;return e===void 0?(r=Z(i),n=Ye(i,r<=o.toExpNeg||r>=o.toExpPos)):(Re(e,1,pt),t===void 0?t=o.rounding:Re(t,0,8),i=q(new o(i),e,t),r=Z(i),n=Ye(i,e<=r||r<=o.toExpNeg,e)),n};I.toSignificantDigits=I.tosd=function(e,t){var r=this,n=r.constructor;return e===void 0?(e=n.precision,t=n.rounding):(Re(e,1,pt),t===void 0?t=n.rounding:Re(t,0,8)),q(new n(r),e,t)};I.toString=I.valueOf=I.val=I.toJSON=I[Symbol.for("nodejs.util.inspect.custom")]=function(){var e=this,t=Z(e),r=e.constructor;return Ye(e,t<=r.toExpNeg||t>=r.toExpPos)};_e=function(){function e(n,i){var o,s=0,a=n.length;for(n=n.slice();a--;)o=n[a]*i+s,n[a]=o%te|0,s=o/te|0;return s&&n.unshift(s),n}function t(n,i,o,s){var a,f;if(o!=s)f=o>s?1:-1;else for(a=f=0;a<o;a++)if(n[a]!=i[a]){f=n[a]>i[a]?1:-1;break}return f}function r(n,i,o){for(var s=0;o--;)n[o]-=s,s=n[o]<i[o]?1:0,n[o]=s*te+n[o]-i[o];for(;!n[0]&&n.length>1;)n.shift()}return function(n,i,o,s){var a,f,E,A,R,S,C,L,k,M,De,le,B,ce,We,ni,xe,Hr,Gr=n.constructor,Jc=n.s==i.s?1:-1,ve=n.d,z=i.d;if(!n.s)return new Gr(n);if(!i.s)throw Error(be+"Division by zero");for(f=n.e-i.e,xe=z.length,We=ve.length,C=new Gr(Jc),L=C.d=[],E=0;z[E]==(ve[E]||0);)++E;if(z[E]>(ve[E]||0)&&--f,o==null?le=o=Gr.precision:s?le=o+(Z(n)-Z(i))+1:le=o,le<0)return new Gr(0);if(le=le/H+2|0,E=0,xe==1)for(A=0,z=z[0],le++;(E<We||A)&&le--;E++)B=A*te+(ve[E]||0),L[E]=B/z|0,A=B%z|0;else{for(A=te/(z[0]+1)|0,A>1&&(z=e(z,A),ve=e(ve,A),xe=z.length,We=ve.length),ce=xe,k=ve.slice(0,xe),M=k.length;M<xe;)k[M++]=0;Hr=z.slice(),Hr.unshift(0),ni=z[0],z[1]>=te/2&&++ni;do A=0,a=t(z,k,xe,M),a<0?(De=k[0],xe!=M&&(De=De*te+(k[1]||0)),A=De/ni|0,A>1?(A>=te&&(A=te-1),R=e(z,A),S=R.length,M=k.length,a=t(R,k,S,M),a==1&&(A--,r(R,xe<S?Hr:z,S))):(A==0&&(a=A=1),R=z.slice()),S=R.length,S<M&&R.unshift(0),r(k,R,M),a==-1&&(M=k.length,a=t(z,k,xe,M),a<1&&(A++,r(k,xe<M?Hr:z,M))),M=k.length):a===0&&(A++,k=[0]),L[E++]=A,a&&k[0]?k[M++]=ve[ce]||0:(k=[ve[ce]],M=1);while((ce++<We||k[0]!==void 0)&&le--)}return L[0]||L.shift(),C.e=f,q(C,s?o+Z(C)+1:o)}}();li=Ho(lu);ye=new li(1);zr=li});var v,me,l=ge(()=>{"use strict";Go();v=class extends zr{static isDecimal(t){return t instanceof zr}static random(t=20){{let n=globalThis.crypto.getRandomValues(new Uint8Array(t)).reduce((i,o)=>i+o,"");return new zr(`0.${n.slice(0,t)}`)}}},me=v});function yu(){return!1}function pi(){return{dev:0,ino:0,mode:0,nlink:0,uid:0,gid:0,rdev:0,size:0,blksize:0,blocks:0,atimeMs:0,mtimeMs:0,ctimeMs:0,birthtimeMs:0,atime:new Date,mtime:new Date,ctime:new Date,birthtime:new Date}}function hu(){return pi()}function wu(){return[]}function bu(e){e(null,[])}function Eu(){return""}function xu(){return""}function Pu(){}function Tu(){}function vu(){}function Au(){}function Ru(){}function Cu(){}function Su(){}function Iu(){}function ku(){return{close:()=>{},on:()=>{},removeAllListeners:()=>{}}}function Ou(e,t){t(null,pi())}var Du,_u,ps,ms=ge(()=>{"use strict";c();u();p();m();d();l();Du={},_u={existsSync:yu,lstatSync:pi,stat:Ou,statSync:hu,readdirSync:wu,readdir:bu,readlinkSync:Eu,realpathSync:xu,chmodSync:Pu,renameSync:Tu,mkdirSync:vu,rmdirSync:Au,rmSync:Ru,unlinkSync:Cu,watchFile:Su,unwatchFile:Iu,watch:ku,promises:Du},ps=_u});var ds=ue(()=>{"use strict";c();u();p();m();d();l()});function Mu(...e){return e.join("/")}function Nu(...e){return e.join("/")}function Lu(e){let t=fs(e),r=gs(e),[n,i]=t.split(".");return{root:"/",dir:r,base:t,ext:i,name:n}}function fs(e){let t=e.split("/");return t[t.length-1]}function gs(e){return e.split("/").slice(0,-1).join("/")}function Fu(e){let t=e.split("/").filter(i=>i!==""&&i!=="."),r=[];for(let i of t)i===".."?r.pop():r.push(i);let n=r.join("/");return e.startsWith("/")?"/"+n:n}var ys,Uu,$u,Vu,en,hs=ge(()=>{"use strict";c();u();p();m();d();l();ys="/",Uu=":";$u={sep:ys},Vu={basename:fs,delimiter:Uu,dirname:gs,join:Nu,normalize:Fu,parse:Lu,posix:$u,resolve:Mu,sep:ys},en=Vu});var ws=ue((Hy,qu)=>{qu.exports={name:"@prisma/internals",version:"6.16.2",description:"This package is intended for Prisma's internal use",main:"dist/index.js",types:"dist/index.d.ts",repository:{type:"git",url:"https://github.com/prisma/prisma.git",directory:"packages/internals"},homepage:"https://www.prisma.io",author:"Tim Suchanek <<EMAIL>>",bugs:"https://github.com/prisma/prisma/issues",license:"Apache-2.0",scripts:{dev:"DEV=true tsx helpers/build.ts",build:"tsx helpers/build.ts",test:"dotenv -e ../../.db.env -- jest --silent",prepublishOnly:"pnpm run build"},files:["README.md","dist","!**/libquery_engine*","!dist/get-generators/engines/*","scripts"],devDependencies:{"@babel/helper-validator-identifier":"7.25.9","@opentelemetry/api":"1.9.0","@swc/core":"1.11.5","@swc/jest":"0.2.37","@types/babel__helper-validator-identifier":"7.15.2","@types/jest":"29.5.14","@types/node":"18.19.76","@types/resolve":"1.20.6",archiver:"6.0.2","checkpoint-client":"1.1.33","cli-truncate":"4.0.0",dotenv:"16.5.0",empathic:"2.0.0","escape-string-regexp":"5.0.0",execa:"5.1.1","fast-glob":"3.3.3","find-up":"7.0.0","fp-ts":"2.16.9","fs-extra":"11.3.0","fs-jetpack":"5.1.0","global-directory":"4.0.0",globby:"11.1.0","identifier-regex":"1.0.0","indent-string":"4.0.0","is-windows":"1.0.2","is-wsl":"3.1.0",jest:"29.7.0","jest-junit":"16.0.0",kleur:"4.1.5","mock-stdin":"1.0.0","new-github-issue-url":"0.2.1","node-fetch":"3.3.2","npm-packlist":"5.1.3",open:"7.4.2","p-map":"4.0.0",resolve:"1.22.10","string-width":"7.2.0","strip-indent":"4.0.0","temp-dir":"2.0.0",tempy:"1.0.1","terminal-link":"4.0.0",tmp:"0.2.3","ts-pattern":"5.6.2","ts-toolbelt":"9.6.0",typescript:"5.4.5",yarn:"1.22.22"},dependencies:{"@prisma/config":"workspace:*","@prisma/debug":"workspace:*","@prisma/dmmf":"workspace:*","@prisma/driver-adapter-utils":"workspace:*","@prisma/engines":"workspace:*","@prisma/fetch-engine":"workspace:*","@prisma/generator":"workspace:*","@prisma/generator-helper":"workspace:*","@prisma/get-platform":"workspace:*","@prisma/prisma-schema-wasm":"6.16.0-7.****************************************","@prisma/schema-engine-wasm":"6.16.0-7.****************************************","@prisma/schema-files-loader":"workspace:*",arg:"5.0.2",prompts:"2.4.2"},peerDependencies:{typescript:">=5.1.0"},peerDependenciesMeta:{typescript:{optional:!0}},sideEffects:!1}});var di={};Gt(di,{Hash:()=>zt,createHash:()=>bs,default:()=>gt,randomFillSync:()=>nn,randomUUID:()=>rn,webcrypto:()=>Yt});function rn(){return globalThis.crypto.randomUUID()}function nn(e,t,r){return t!==void 0&&(r!==void 0?e=e.subarray(t,t+r):e=e.subarray(t)),globalThis.crypto.getRandomValues(e)}function bs(e){return new zt(e)}var Yt,zt,gt,Ze=ge(()=>{"use strict";c();u();p();m();d();l();Yt=globalThis.crypto;zt=class{#t=[];#e;constructor(t){this.#e=t}update(t){this.#t.push(t)}async digest(){let t=new Uint8Array(this.#t.reduce((i,o)=>i+o.length,0)),r=0;for(let i of this.#t)t.set(i,r),r+=i.length;let n=await globalThis.crypto.subtle.digest(this.#e,t);return new Uint8Array(n)}},gt={webcrypto:Yt,randomUUID:rn,randomFillSync:nn,createHash:bs,Hash:zt}});var fi=ue((Uh,Hu)=>{Hu.exports={name:"@prisma/engines-version",version:"6.16.0-7.****************************************",main:"index.js",types:"index.d.ts",license:"Apache-2.0",author:"Tim Suchanek <<EMAIL>>",prisma:{enginesVersion:"****************************************"},repository:{type:"git",url:"https://github.com/prisma/engines-wrapper.git",directory:"packages/engines-version"},devDependencies:{"@types/node":"18.19.76",typescript:"4.9.5"},files:["index.js","index.d.ts"],scripts:{build:"tsc -d"}}});var Es=ue(on=>{"use strict";c();u();p();m();d();l();Object.defineProperty(on,"__esModule",{value:!0});on.enginesVersion=void 0;on.enginesVersion=fi().prisma.enginesVersion});var Ts=ue((Zh,Ps)=>{"use strict";c();u();p();m();d();l();Ps.exports=(e,t=1,r)=>{if(r={indent:" ",includeEmptyLines:!1,...r},typeof e!="string")throw new TypeError(`Expected \`input\` to be a \`string\`, got \`${typeof e}\``);if(typeof t!="number")throw new TypeError(`Expected \`count\` to be a \`number\`, got \`${typeof t}\``);if(typeof r.indent!="string")throw new TypeError(`Expected \`options.indent\` to be a \`string\`, got \`${typeof r.indent}\``);if(t===0)return e;let n=r.includeEmptyLines?/^/gm:/^(?!\s*$)/gm;return e.replace(n,r.indent.repeat(t))}});var As=ue((Mw,ln)=>{"use strict";c();u();p();m();d();l();ln.exports=(e={})=>{let t;if(e.repoUrl)t=e.repoUrl;else if(e.user&&e.repo)t=`https://github.com/${e.user}/${e.repo}`;else throw new Error("You need to specify either the `repoUrl` option or both the `user` and `repo` options");let r=new URL(`${t}/issues/new`),n=["body","title","labels","template","milestone","assignee","projects"];for(let i of n){let o=e[i];if(o!==void 0){if(i==="labels"||i==="projects"){if(!Array.isArray(o))throw new TypeError(`The \`${i}\` option should be an array`);o=o.join(",")}r.searchParams.set(i,o)}}return r.toString()};ln.exports.default=ln.exports});var xi=ue((RP,Is)=>{"use strict";c();u();p();m();d();l();Is.exports=function(){function e(t,r,n,i,o){return t<r||n<r?t>n?n+1:t+1:i===o?r:r+1}return function(t,r){if(t===r)return 0;if(t.length>r.length){var n=t;t=r,r=n}for(var i=t.length,o=r.length;i>0&&t.charCodeAt(i-1)===r.charCodeAt(o-1);)i--,o--;for(var s=0;s<i&&t.charCodeAt(s)===r.charCodeAt(s);)s++;if(i-=s,o-=s,i===0||o<3)return o;var a=0,f,E,A,R,S,C,L,k,M,De,le,B,ce=[];for(f=0;f<i;f++)ce.push(f+1),ce.push(t.charCodeAt(s+f));for(var We=ce.length-1;a<o-3;)for(M=r.charCodeAt(s+(E=a)),De=r.charCodeAt(s+(A=a+1)),le=r.charCodeAt(s+(R=a+2)),B=r.charCodeAt(s+(S=a+3)),C=a+=4,f=0;f<We;f+=2)L=ce[f],k=ce[f+1],E=e(L,E,A,M,k),A=e(E,A,R,De,k),R=e(A,R,S,le,k),C=e(R,S,C,B,k),ce[f]=C,S=R,R=A,A=E,E=L;for(;a<o;)for(M=r.charCodeAt(s+(E=a)),C=++a,f=0;f<We;f+=2)L=ce[f],ce[f]=C=e(L,E,C,M,ce[f+1]),E=L;return C}}()});var Ms=ge(()=>{"use strict";c();u();p();m();d();l()});var Ns=ge(()=>{"use strict";c();u();p();m();d();l()});var Cn,ra=ge(()=>{"use strict";c();u();p();m();d();l();Cn=class{events={};on(t,r){return this.events[t]||(this.events[t]=[]),this.events[t].push(r),this}emit(t,...r){return this.events[t]?(this.events[t].forEach(n=>{n(...r)}),!0):!1}}});var Gi=ue(rt=>{"use strict";c();u();p();m();d();l();Object.defineProperty(rt,"__esModule",{value:!0});rt.anumber=Hi;rt.abytes=Za;rt.ahash=Om;rt.aexists=Dm;rt.aoutput=_m;function Hi(e){if(!Number.isSafeInteger(e)||e<0)throw new Error("positive integer expected, got "+e)}function km(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Za(e,...t){if(!km(e))throw new Error("Uint8Array expected");if(t.length>0&&!t.includes(e.length))throw new Error("Uint8Array expected of length "+t+", got length="+e.length)}function Om(e){if(typeof e!="function"||typeof e.create!="function")throw new Error("Hash should be wrapped by utils.wrapConstructor");Hi(e.outputLen),Hi(e.blockLen)}function Dm(e,t=!0){if(e.destroyed)throw new Error("Hash instance has been destroyed");if(t&&e.finished)throw new Error("Hash#digest() has already been called")}function _m(e,t){Za(e);let r=t.outputLen;if(e.length<r)throw new Error("digestInto() expects output buffer of length at least "+r)}});var El=ue(_=>{"use strict";c();u();p();m();d();l();Object.defineProperty(_,"__esModule",{value:!0});_.add5L=_.add5H=_.add4H=_.add4L=_.add3H=_.add3L=_.rotlBL=_.rotlBH=_.rotlSL=_.rotlSH=_.rotr32L=_.rotr32H=_.rotrBL=_.rotrBH=_.rotrSL=_.rotrSH=_.shrSL=_.shrSH=_.toBig=void 0;_.fromBig=Wi;_.split=Xa;_.add=dl;var Nn=BigInt(2**32-1),Ji=BigInt(32);function Wi(e,t=!1){return t?{h:Number(e&Nn),l:Number(e>>Ji&Nn)}:{h:Number(e>>Ji&Nn)|0,l:Number(e&Nn)|0}}function Xa(e,t=!1){let r=new Uint32Array(e.length),n=new Uint32Array(e.length);for(let i=0;i<e.length;i++){let{h:o,l:s}=Wi(e[i],t);[r[i],n[i]]=[o,s]}return[r,n]}var el=(e,t)=>BigInt(e>>>0)<<Ji|BigInt(t>>>0);_.toBig=el;var tl=(e,t,r)=>e>>>r;_.shrSH=tl;var rl=(e,t,r)=>e<<32-r|t>>>r;_.shrSL=rl;var nl=(e,t,r)=>e>>>r|t<<32-r;_.rotrSH=nl;var il=(e,t,r)=>e<<32-r|t>>>r;_.rotrSL=il;var ol=(e,t,r)=>e<<64-r|t>>>r-32;_.rotrBH=ol;var sl=(e,t,r)=>e>>>r-32|t<<64-r;_.rotrBL=sl;var al=(e,t)=>t;_.rotr32H=al;var ll=(e,t)=>e;_.rotr32L=ll;var cl=(e,t,r)=>e<<r|t>>>32-r;_.rotlSH=cl;var ul=(e,t,r)=>t<<r|e>>>32-r;_.rotlSL=ul;var pl=(e,t,r)=>t<<r-32|e>>>64-r;_.rotlBH=pl;var ml=(e,t,r)=>e<<r-32|t>>>64-r;_.rotlBL=ml;function dl(e,t,r,n){let i=(t>>>0)+(n>>>0);return{h:e+r+(i/2**32|0)|0,l:i|0}}var fl=(e,t,r)=>(e>>>0)+(t>>>0)+(r>>>0);_.add3L=fl;var gl=(e,t,r,n)=>t+r+n+(e/2**32|0)|0;_.add3H=gl;var yl=(e,t,r,n)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0);_.add4L=yl;var hl=(e,t,r,n,i)=>t+r+n+i+(e/2**32|0)|0;_.add4H=hl;var wl=(e,t,r,n,i)=>(e>>>0)+(t>>>0)+(r>>>0)+(n>>>0)+(i>>>0);_.add5L=wl;var bl=(e,t,r,n,i,o)=>t+r+n+i+o+(e/2**32|0)|0;_.add5H=bl;var Mm={fromBig:Wi,split:Xa,toBig:el,shrSH:tl,shrSL:rl,rotrSH:nl,rotrSL:il,rotrBH:ol,rotrBL:sl,rotr32H:al,rotr32L:ll,rotlSH:cl,rotlSL:ul,rotlBH:pl,rotlBL:ml,add:dl,add3L:fl,add3H:gl,add4L:yl,add4H:hl,add5H:bl,add5L:wl};_.default=Mm});var xl=ue(Ln=>{"use strict";c();u();p();m();d();l();Object.defineProperty(Ln,"__esModule",{value:!0});Ln.crypto=void 0;var Qe=(Ze(),Xc(di));Ln.crypto=Qe&&typeof Qe=="object"&&"webcrypto"in Qe?Qe.webcrypto:Qe&&typeof Qe=="object"&&"randomBytes"in Qe?Qe:void 0});var vl=ue(U=>{"use strict";c();u();p();m();d();l();Object.defineProperty(U,"__esModule",{value:!0});U.Hash=U.nextTick=U.byteSwapIfBE=U.isLE=void 0;U.isBytes=Nm;U.u8=Lm;U.u32=Um;U.createView=Fm;U.rotr=$m;U.rotl=Vm;U.byteSwap=Yi;U.byteSwap32=qm;U.bytesToHex=jm;U.hexToBytes=Qm;U.asyncLoop=Gm;U.utf8ToBytes=Tl;U.toBytes=Un;U.concatBytes=Jm;U.checkOpts=Wm;U.wrapConstructor=Km;U.wrapConstructorWithOpts=zm;U.wrapXOFConstructorWithOpts=Ym;U.randomBytes=Zm;var Dt=xl(),zi=Gi();function Nm(e){return e instanceof Uint8Array||ArrayBuffer.isView(e)&&e.constructor.name==="Uint8Array"}function Lm(e){return new Uint8Array(e.buffer,e.byteOffset,e.byteLength)}function Um(e){return new Uint32Array(e.buffer,e.byteOffset,Math.floor(e.byteLength/4))}function Fm(e){return new DataView(e.buffer,e.byteOffset,e.byteLength)}function $m(e,t){return e<<32-t|e>>>t}function Vm(e,t){return e<<t|e>>>32-t>>>0}U.isLE=new Uint8Array(new Uint32Array([287454020]).buffer)[0]===68;function Yi(e){return e<<24&4278190080|e<<8&16711680|e>>>8&65280|e>>>24&255}U.byteSwapIfBE=U.isLE?e=>e:e=>Yi(e);function qm(e){for(let t=0;t<e.length;t++)e[t]=Yi(e[t])}var Bm=Array.from({length:256},(e,t)=>t.toString(16).padStart(2,"0"));function jm(e){(0,zi.abytes)(e);let t="";for(let r=0;r<e.length;r++)t+=Bm[e[r]];return t}var Le={_0:48,_9:57,A:65,F:70,a:97,f:102};function Pl(e){if(e>=Le._0&&e<=Le._9)return e-Le._0;if(e>=Le.A&&e<=Le.F)return e-(Le.A-10);if(e>=Le.a&&e<=Le.f)return e-(Le.a-10)}function Qm(e){if(typeof e!="string")throw new Error("hex string expected, got "+typeof e);let t=e.length,r=t/2;if(t%2)throw new Error("hex string expected, got unpadded hex of length "+t);let n=new Uint8Array(r);for(let i=0,o=0;i<r;i++,o+=2){let s=Pl(e.charCodeAt(o)),a=Pl(e.charCodeAt(o+1));if(s===void 0||a===void 0){let f=e[o]+e[o+1];throw new Error('hex string expected, got non-hex character "'+f+'" at index '+o)}n[i]=s*16+a}return n}var Hm=async()=>{};U.nextTick=Hm;async function Gm(e,t,r){let n=Date.now();for(let i=0;i<e;i++){r(i);let o=Date.now()-n;o>=0&&o<t||(await(0,U.nextTick)(),n+=o)}}function Tl(e){if(typeof e!="string")throw new Error("utf8ToBytes expected string, got "+typeof e);return new Uint8Array(new TextEncoder().encode(e))}function Un(e){return typeof e=="string"&&(e=Tl(e)),(0,zi.abytes)(e),e}function Jm(...e){let t=0;for(let n=0;n<e.length;n++){let i=e[n];(0,zi.abytes)(i),t+=i.length}let r=new Uint8Array(t);for(let n=0,i=0;n<e.length;n++){let o=e[n];r.set(o,i),i+=o.length}return r}var Ki=class{clone(){return this._cloneInto()}};U.Hash=Ki;function Wm(e,t){if(t!==void 0&&{}.toString.call(t)!=="[object Object]")throw new Error("Options should be object or undefined");return Object.assign(e,t)}function Km(e){let t=n=>e().update(Un(n)).digest(),r=e();return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=()=>e(),t}function zm(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Ym(e){let t=(n,i)=>e(i).update(Un(n)).digest(),r=e({});return t.outputLen=r.outputLen,t.blockLen=r.blockLen,t.create=n=>e(n),t}function Zm(e=32){if(Dt.crypto&&typeof Dt.crypto.getRandomValues=="function")return Dt.crypto.getRandomValues(new Uint8Array(e));if(Dt.crypto&&typeof Dt.crypto.randomBytes=="function")return Dt.crypto.randomBytes(e);throw new Error("crypto.getRandomValues must be defined")}});var Dl=ue(J=>{"use strict";c();u();p();m();d();l();Object.defineProperty(J,"__esModule",{value:!0});J.shake256=J.shake128=J.keccak_512=J.keccak_384=J.keccak_256=J.keccak_224=J.sha3_512=J.sha3_384=J.sha3_256=J.sha3_224=J.Keccak=void 0;J.keccakP=kl;var _t=Gi(),br=El(),Ue=vl(),Cl=[],Sl=[],Il=[],Xm=BigInt(0),wr=BigInt(1),ed=BigInt(2),td=BigInt(7),rd=BigInt(256),nd=BigInt(113);for(let e=0,t=wr,r=1,n=0;e<24;e++){[r,n]=[n,(2*r+3*n)%5],Cl.push(2*(5*n+r)),Sl.push((e+1)*(e+2)/2%64);let i=Xm;for(let o=0;o<7;o++)t=(t<<wr^(t>>td)*nd)%rd,t&ed&&(i^=wr<<(wr<<BigInt(o))-wr);Il.push(i)}var[id,od]=(0,br.split)(Il,!0),Al=(e,t,r)=>r>32?(0,br.rotlBH)(e,t,r):(0,br.rotlSH)(e,t,r),Rl=(e,t,r)=>r>32?(0,br.rotlBL)(e,t,r):(0,br.rotlSL)(e,t,r);function kl(e,t=24){let r=new Uint32Array(10);for(let n=24-t;n<24;n++){for(let s=0;s<10;s++)r[s]=e[s]^e[s+10]^e[s+20]^e[s+30]^e[s+40];for(let s=0;s<10;s+=2){let a=(s+8)%10,f=(s+2)%10,E=r[f],A=r[f+1],R=Al(E,A,1)^r[a],S=Rl(E,A,1)^r[a+1];for(let C=0;C<50;C+=10)e[s+C]^=R,e[s+C+1]^=S}let i=e[2],o=e[3];for(let s=0;s<24;s++){let a=Sl[s],f=Al(i,o,a),E=Rl(i,o,a),A=Cl[s];i=e[A],o=e[A+1],e[A]=f,e[A+1]=E}for(let s=0;s<50;s+=10){for(let a=0;a<10;a++)r[a]=e[s+a];for(let a=0;a<10;a++)e[s+a]^=~r[(a+2)%10]&r[(a+4)%10]}e[0]^=id[n],e[1]^=od[n]}r.fill(0)}var Er=class e extends Ue.Hash{constructor(t,r,n,i=!1,o=24){if(super(),this.blockLen=t,this.suffix=r,this.outputLen=n,this.enableXOF=i,this.rounds=o,this.pos=0,this.posOut=0,this.finished=!1,this.destroyed=!1,(0,_t.anumber)(n),0>=this.blockLen||this.blockLen>=200)throw new Error("Sha3 supports only keccak-f1600 function");this.state=new Uint8Array(200),this.state32=(0,Ue.u32)(this.state)}keccak(){Ue.isLE||(0,Ue.byteSwap32)(this.state32),kl(this.state32,this.rounds),Ue.isLE||(0,Ue.byteSwap32)(this.state32),this.posOut=0,this.pos=0}update(t){(0,_t.aexists)(this);let{blockLen:r,state:n}=this;t=(0,Ue.toBytes)(t);let i=t.length;for(let o=0;o<i;){let s=Math.min(r-this.pos,i-o);for(let a=0;a<s;a++)n[this.pos++]^=t[o++];this.pos===r&&this.keccak()}return this}finish(){if(this.finished)return;this.finished=!0;let{state:t,suffix:r,pos:n,blockLen:i}=this;t[n]^=r,(r&128)!==0&&n===i-1&&this.keccak(),t[i-1]^=128,this.keccak()}writeInto(t){(0,_t.aexists)(this,!1),(0,_t.abytes)(t),this.finish();let r=this.state,{blockLen:n}=this;for(let i=0,o=t.length;i<o;){this.posOut>=n&&this.keccak();let s=Math.min(n-this.posOut,o-i);t.set(r.subarray(this.posOut,this.posOut+s),i),this.posOut+=s,i+=s}return t}xofInto(t){if(!this.enableXOF)throw new Error("XOF is not possible for this instance");return this.writeInto(t)}xof(t){return(0,_t.anumber)(t),this.xofInto(new Uint8Array(t))}digestInto(t){if((0,_t.aoutput)(t,this),this.finished)throw new Error("digest() was already called");return this.writeInto(t),this.destroy(),t}digest(){return this.digestInto(new Uint8Array(this.outputLen))}destroy(){this.destroyed=!0,this.state.fill(0)}_cloneInto(t){let{blockLen:r,suffix:n,outputLen:i,rounds:o,enableXOF:s}=this;return t||(t=new e(r,n,i,s,o)),t.state32.set(this.state32),t.pos=this.pos,t.posOut=this.posOut,t.finished=this.finished,t.rounds=o,t.suffix=n,t.outputLen=i,t.enableXOF=s,t.destroyed=this.destroyed,t}};J.Keccak=Er;var He=(e,t,r)=>(0,Ue.wrapConstructor)(()=>new Er(t,e,r));J.sha3_224=He(6,144,224/8);J.sha3_256=He(6,136,256/8);J.sha3_384=He(6,104,384/8);J.sha3_512=He(6,72,512/8);J.keccak_224=He(1,144,224/8);J.keccak_256=He(1,136,256/8);J.keccak_384=He(1,104,384/8);J.keccak_512=He(1,72,512/8);var Ol=(e,t,r)=>(0,Ue.wrapXOFConstructorWithOpts)((n={})=>new Er(t,e,n.dkLen===void 0?r:n.dkLen,!0));J.shake128=Ol(31,168,128/8);J.shake256=Ol(31,136,256/8)});var Vl=ue((vL,Ge)=>{"use strict";c();u();p();m();d();l();var{sha3_512:sd}=Dl(),Ml=24,xr=32,Zi=(e=4,t=Math.random)=>{let r="";for(;r.length<e;)r=r+Math.floor(t()*36).toString(36);return r};function Nl(e){let t=8n,r=0n;for(let n of e.values()){let i=BigInt(n);r=(r<<t)+i}return r}var Ll=(e="")=>Nl(sd(e)).toString(36).slice(1),_l=Array.from({length:26},(e,t)=>String.fromCharCode(t+97)),ad=e=>_l[Math.floor(e()*_l.length)],Ul=({globalObj:e=typeof globalThis<"u"?globalThis:typeof window<"u"?window:{},random:t=Math.random}={})=>{let r=Object.keys(e).toString(),n=r.length?r+Zi(xr,t):Zi(xr,t);return Ll(n).substring(0,xr)},Fl=e=>()=>e++,ld=476782367,$l=({random:e=Math.random,counter:t=Fl(Math.floor(e()*ld)),length:r=Ml,fingerprint:n=Ul({random:e})}={})=>function(){let o=ad(e),s=Date.now().toString(36),a=t().toString(36),f=Zi(r,e),E=`${s+f+a+n}`;return`${o+Ll(E).substring(1,r)}`},cd=$l(),ud=(e,{minLength:t=2,maxLength:r=xr}={})=>{let n=e.length,i=/^[0-9a-z]+$/;try{if(typeof e=="string"&&n>=t&&n<=r&&i.test(e))return!0}finally{}return!1};Ge.exports.getConstants=()=>({defaultLength:Ml,bigLength:xr});Ge.exports.init=$l;Ge.exports.createId=cd;Ge.exports.bufToBigInt=Nl;Ge.exports.createCounter=Fl;Ge.exports.createFingerprint=Ul;Ge.exports.isCuid=ud});var ql=ue((OL,Pr)=>{"use strict";c();u();p();m();d();l();var{createId:pd,init:md,getConstants:dd,isCuid:fd}=Vl();Pr.exports.createId=pd;Pr.exports.init=md;Pr.exports.getConstants=dd;Pr.exports.isCuid=fd});c();u();p();m();d();l();var Ko={};Gt(Ko,{defineExtension:()=>Jo,getExtensionContext:()=>Wo});c();u();p();m();d();l();c();u();p();m();d();l();function Jo(e){return typeof e=="function"?e:t=>t.$extends(e)}c();u();p();m();d();l();function Wo(e){return e}var Yo={};Gt(Yo,{validator:()=>zo});c();u();p();m();d();l();c();u();p();m();d();l();function zo(...e){return t=>t}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var ci,Zo,Xo,es,ts=!0;typeof g<"u"&&({FORCE_COLOR:ci,NODE_DISABLE_COLORS:Zo,NO_COLOR:Xo,TERM:es}=g.env||{},ts=g.stdout&&g.stdout.isTTY);var pu={enabled:!Zo&&Xo==null&&es!=="dumb"&&(ci!=null&&ci!=="0"||ts)};function j(e,t){let r=new RegExp(`\\x1b\\[${t}m`,"g"),n=`\x1B[${e}m`,i=`\x1B[${t}m`;return function(o){return!pu.enabled||o==null?o:n+(~(""+o).indexOf(i)?o.replace(r,i+n):o)+i}}var Ng=j(0,0),Yr=j(1,22),Zr=j(2,22),Lg=j(3,23),Xr=j(4,24),Ug=j(7,27),Fg=j(8,28),$g=j(9,29),Vg=j(30,39),dt=j(31,39),rs=j(32,39),ns=j(33,39),is=j(34,39),qg=j(35,39),os=j(36,39),Bg=j(37,39),ss=j(90,39),jg=j(90,39),Qg=j(40,49),Hg=j(41,49),Gg=j(42,49),Jg=j(43,49),Wg=j(44,49),Kg=j(45,49),zg=j(46,49),Yg=j(47,49);c();u();p();m();d();l();var mu=100,as=["green","yellow","blue","magenta","cyan","red"],Wt=[],ls=Date.now(),du=0,ui=typeof g<"u"?g.env:{};globalThis.DEBUG??=ui.DEBUG??"";globalThis.DEBUG_COLORS??=ui.DEBUG_COLORS?ui.DEBUG_COLORS==="true":!0;var Kt={enable(e){typeof e=="string"&&(globalThis.DEBUG=e)},disable(){let e=globalThis.DEBUG;return globalThis.DEBUG="",e},enabled(e){let t=globalThis.DEBUG.split(",").map(i=>i.replace(/[.+?^${}()|[\]\\]/g,"\\$&")),r=t.some(i=>i===""||i[0]==="-"?!1:e.match(RegExp(i.split("*").join(".*")+"$"))),n=t.some(i=>i===""||i[0]!=="-"?!1:e.match(RegExp(i.slice(1).split("*").join(".*")+"$")));return r&&!n},log:(...e)=>{let[t,r,...n]=e;(console.warn??console.log)(`${t} ${r}`,...n)},formatters:{}};function fu(e){let t={color:as[du++%as.length],enabled:Kt.enabled(e),namespace:e,log:Kt.log,extend:()=>{}},r=(...n)=>{let{enabled:i,namespace:o,color:s,log:a}=t;if(n.length!==0&&Wt.push([o,...n]),Wt.length>mu&&Wt.shift(),Kt.enabled(o)||i){let f=n.map(A=>typeof A=="string"?A:gu(A)),E=`+${Date.now()-ls}ms`;ls=Date.now(),a(o,...f,E)}};return new Proxy(r,{get:(n,i)=>t[i],set:(n,i,o)=>t[i]=o})}var K=new Proxy(fu,{get:(e,t)=>Kt[t],set:(e,t,r)=>Kt[t]=r});function gu(e,t=2){let r=new Set;return JSON.stringify(e,(n,i)=>{if(typeof i=="object"&&i!==null){if(r.has(i))return"[Circular *]";r.add(i)}else if(typeof i=="bigint")return i.toString();return i},t)}function cs(e=7500){let t=Wt.map(([r,...n])=>`${r} ${n.map(i=>typeof i=="string"?i:JSON.stringify(i)).join(" ")}`).join(`
`);return t.length<e?t:t.slice(-e)}function us(){Wt.length=0}c();u();p();m();d();l();c();u();p();m();d();l();var Bu=ws(),mi=Bu.version;c();u();p();m();d();l();function ft(e){let t=ju();return t||(e?.config.engineType==="library"?"library":e?.config.engineType==="binary"?"binary":e?.config.engineType==="client"?"client":Qu())}function ju(){let e=g.env.PRISMA_CLIENT_ENGINE_TYPE;return e==="library"?"library":e==="binary"?"binary":e==="client"?"client":void 0}function Qu(){return"library"}c();u();p();m();d();l();c();u();p();m();d();l();function tn(e){return e.name==="DriverAdapterError"&&typeof e.cause=="object"}c();u();p();m();d();l();var O={Int32:0,Int64:1,Float:2,Double:3,Numeric:4,Boolean:5,Character:6,Text:7,Date:8,Time:9,DateTime:10,Json:11,Enum:12,Bytes:13,Set:14,Uuid:15,Int32Array:64,Int64Array:65,FloatArray:66,DoubleArray:67,NumericArray:68,BooleanArray:69,CharacterArray:70,TextArray:71,DateArray:72,TimeArray:73,DateTimeArray:74,JsonArray:75,EnumArray:76,BytesArray:77,UuidArray:78,UnknownNumber:128};c();u();p();m();d();l();var xs="prisma+postgres",sn=`${xs}:`;function an(e){return e?.toString().startsWith(`${sn}//`)??!1}function gi(e){if(!an(e))return!1;let{host:t}=new URL(e);return t.includes("localhost")||t.includes("127.0.0.1")||t.includes("[::1]")}var Xt={};Gt(Xt,{error:()=>Wu,info:()=>Ju,log:()=>Gu,query:()=>Ku,should:()=>vs,tags:()=>Zt,warn:()=>yi});c();u();p();m();d();l();var Zt={error:dt("prisma:error"),warn:ns("prisma:warn"),info:os("prisma:info"),query:is("prisma:query")},vs={warn:()=>!g.env.PRISMA_DISABLE_WARNINGS};function Gu(...e){console.log(...e)}function yi(e,...t){vs.warn()&&console.warn(`${Zt.warn} ${e}`,...t)}function Ju(e,...t){console.info(`${Zt.info} ${e}`,...t)}function Wu(e,...t){console.error(`${Zt.error} ${e}`,...t)}function Ku(e,...t){console.log(`${Zt.query} ${e}`,...t)}c();u();p();m();d();l();function Me(e,t){throw new Error(t)}c();u();p();m();d();l();c();u();p();m();d();l();function hi({onlyFirst:e=!1}={}){let r=["[\\u001B\\u009B][[\\]()#;?]*(?:(?:(?:(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]+)*|[a-zA-Z\\d]+(?:;[-a-zA-Z\\d\\/#&.:=?%@~_]*)*)?(?:\\u0007|\\u001B\\u005C|\\u009C))","(?:(?:\\d{1,4}(?:;\\d{0,4})*)?[\\dA-PR-TZcf-nq-uy=><~]))"].join("|");return new RegExp(r,e?void 0:"g")}var zu=hi();function yt(e){if(typeof e!="string")throw new TypeError(`Expected a \`string\`, got \`${typeof e}\``);return e.replace(zu,"")}c();u();p();m();d();l();function wi(e,t){return Object.prototype.hasOwnProperty.call(e,t)}c();u();p();m();d();l();function cn(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}c();u();p();m();d();l();function bi(e,t){if(e.length===0)return;let r=e[0];for(let n=1;n<e.length;n++)t(r,e[n])<0&&(r=e[n]);return r}c();u();p();m();d();l();function D(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var Rs=new Set,un=(e,t,...r)=>{Rs.has(e)||(Rs.add(e),yi(t,...r))};var $=class e extends Error{clientVersion;errorCode;retryable;constructor(t,r,n){super(t),this.name="PrismaClientInitializationError",this.clientVersion=r,this.errorCode=n,Error.captureStackTrace(e)}get[Symbol.toStringTag](){return"PrismaClientInitializationError"}};D($,"PrismaClientInitializationError");c();u();p();m();d();l();var ee=class extends Error{code;meta;clientVersion;batchRequestIdx;constructor(t,{code:r,clientVersion:n,meta:i,batchRequestIdx:o}){super(t),this.name="PrismaClientKnownRequestError",this.code=r,this.clientVersion=n,this.meta=i,Object.defineProperty(this,"batchRequestIdx",{value:o,enumerable:!1,writable:!0})}get[Symbol.toStringTag](){return"PrismaClientKnownRequestError"}};D(ee,"PrismaClientKnownRequestError");c();u();p();m();d();l();var de=class extends Error{clientVersion;constructor(t,r){super(t),this.name="PrismaClientRustPanicError",this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientRustPanicError"}};D(de,"PrismaClientRustPanicError");c();u();p();m();d();l();var ie=class extends Error{clientVersion;batchRequestIdx;constructor(t,{clientVersion:r,batchRequestIdx:n}){super(t),this.name="PrismaClientUnknownRequestError",this.clientVersion=r,Object.defineProperty(this,"batchRequestIdx",{value:n,writable:!0,enumerable:!1})}get[Symbol.toStringTag](){return"PrismaClientUnknownRequestError"}};D(ie,"PrismaClientUnknownRequestError");c();u();p();m();d();l();var oe=class extends Error{name="PrismaClientValidationError";clientVersion;constructor(t,{clientVersion:r}){super(t),this.clientVersion=r}get[Symbol.toStringTag](){return"PrismaClientValidationError"}};D(oe,"PrismaClientValidationError");c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Ce=class{_map=new Map;get(t){return this._map.get(t)?.value}set(t,r){this._map.set(t,{value:r})}getOrCreate(t,r){let n=this._map.get(t);if(n)return n.value;let i=r();return this.set(t,i),i}};c();u();p();m();d();l();function qe(e){return e.substring(0,1).toLowerCase()+e.substring(1)}c();u();p();m();d();l();function Ss(e,t){let r={};for(let n of e){let i=n[t];r[i]=n}return r}c();u();p();m();d();l();function er(e){let t;return{get(){return t||(t={value:e()}),t.value}}}c();u();p();m();d();l();function Yu(e){return{models:Ei(e.models),enums:Ei(e.enums),types:Ei(e.types)}}function Ei(e){let t={};for(let{name:r,...n}of e)t[r]=n;return t}c();u();p();m();d();l();function ht(e){return e instanceof Date||Object.prototype.toString.call(e)==="[object Date]"}function pn(e){return e.toString()!=="Invalid Date"}c();u();p();m();d();l();l();function wt(e){return v.isDecimal(e)?!0:e!==null&&typeof e=="object"&&typeof e.s=="number"&&typeof e.e=="number"&&typeof e.toFixed=="function"&&Array.isArray(e.d)}c();u();p();m();d();l();c();u();p();m();d();l();var mn={};Gt(mn,{ModelAction:()=>tr,datamodelEnumToSchemaEnum:()=>Zu});c();u();p();m();d();l();c();u();p();m();d();l();function Zu(e){return{name:e.name,values:e.values.map(t=>t.name)}}c();u();p();m();d();l();var tr=(B=>(B.findUnique="findUnique",B.findUniqueOrThrow="findUniqueOrThrow",B.findFirst="findFirst",B.findFirstOrThrow="findFirstOrThrow",B.findMany="findMany",B.create="create",B.createMany="createMany",B.createManyAndReturn="createManyAndReturn",B.update="update",B.updateMany="updateMany",B.updateManyAndReturn="updateManyAndReturn",B.upsert="upsert",B.delete="delete",B.deleteMany="deleteMany",B.groupBy="groupBy",B.count="count",B.aggregate="aggregate",B.findRaw="findRaw",B.aggregateRaw="aggregateRaw",B))(tr||{});var Xu=$e(Ts());var ep={red:dt,gray:ss,dim:Zr,bold:Yr,underline:Xr,highlightSource:e=>e.highlight()},tp={red:e=>e,gray:e=>e,dim:e=>e,bold:e=>e,underline:e=>e,highlightSource:e=>e};function rp({message:e,originalMethod:t,isPanic:r,callArguments:n}){return{functionName:`prisma.${t}()`,message:e,isPanic:r??!1,callArguments:n}}function np({functionName:e,location:t,message:r,isPanic:n,contextLines:i,callArguments:o},s){let a=[""],f=t?" in":":";if(n?(a.push(s.red(`Oops, an unknown error occurred! This is ${s.bold("on us")}, you did nothing wrong.`)),a.push(s.red(`It occurred in the ${s.bold(`\`${e}\``)} invocation${f}`))):a.push(s.red(`Invalid ${s.bold(`\`${e}\``)} invocation${f}`)),t&&a.push(s.underline(ip(t))),i){a.push("");let E=[i.toString()];o&&(E.push(o),E.push(s.dim(")"))),a.push(E.join("")),o&&a.push("")}else a.push(""),o&&a.push(o),a.push("");return a.push(r),a.join(`
`)}function ip(e){let t=[e.fileName];return e.lineNumber&&t.push(String(e.lineNumber)),e.columnNumber&&t.push(String(e.columnNumber)),t.join(":")}function dn(e){let t=e.showColors?ep:tp,r;return typeof $getTemplateParameters<"u"?r=$getTemplateParameters(e,t):r=rp(e),np(r,t)}c();u();p();m();d();l();var Us=$e(xi());c();u();p();m();d();l();function Ds(e,t,r){let n=_s(e),i=op(n),o=ap(i);o?fn(o,t,r):t.addErrorMessage(()=>"Unknown error")}function _s(e){return e.errors.flatMap(t=>t.kind==="Union"?_s(t):[t])}function op(e){let t=new Map,r=[];for(let n of e){if(n.kind!=="InvalidArgumentType"){r.push(n);continue}let i=`${n.selectionPath.join(".")}:${n.argumentPath.join(".")}`,o=t.get(i);o?t.set(i,{...n,argument:{...n.argument,typeNames:sp(o.argument.typeNames,n.argument.typeNames)}}):t.set(i,n)}return r.push(...t.values()),r}function sp(e,t){return[...new Set(e.concat(t))]}function ap(e){return bi(e,(t,r)=>{let n=ks(t),i=ks(r);return n!==i?n-i:Os(t)-Os(r)})}function ks(e){let t=0;return Array.isArray(e.selectionPath)&&(t+=e.selectionPath.length),Array.isArray(e.argumentPath)&&(t+=e.argumentPath.length),t}function Os(e){switch(e.kind){case"InvalidArgumentValue":case"ValueTooLarge":return 20;case"InvalidArgumentType":return 10;case"RequiredArgumentMissing":return-10;default:return 0}}c();u();p();m();d();l();var he=class{constructor(t,r){this.name=t;this.value=r}isRequired=!1;makeRequired(){return this.isRequired=!0,this}write(t){let{colors:{green:r}}=t.context;t.addMarginSymbol(r(this.isRequired?"+":"?")),t.write(r(this.name)),this.isRequired||t.write(r("?")),t.write(r(": ")),typeof this.value=="string"?t.write(r(this.value)):t.write(this.value)}};c();u();p();m();d();l();c();u();p();m();d();l();Ns();c();u();p();m();d();l();var bt=class{constructor(t=0,r){this.context=r;this.currentIndent=t}lines=[];currentLine="";currentIndent=0;marginSymbol;afterNextNewLineCallback;write(t){return typeof t=="string"?this.currentLine+=t:t.write(this),this}writeJoined(t,r,n=(i,o)=>o.write(i)){let i=r.length-1;for(let o=0;o<r.length;o++)n(r[o],this),o!==i&&this.write(t);return this}writeLine(t){return this.write(t).newLine()}newLine(){this.lines.push(this.indentedCurrentLine()),this.currentLine="",this.marginSymbol=void 0;let t=this.afterNextNewLineCallback;return this.afterNextNewLineCallback=void 0,t?.(),this}withIndent(t){return this.indent(),t(this),this.unindent(),this}afterNextNewline(t){return this.afterNextNewLineCallback=t,this}indent(){return this.currentIndent++,this}unindent(){return this.currentIndent>0&&this.currentIndent--,this}addMarginSymbol(t){return this.marginSymbol=t,this}toString(){return this.lines.concat(this.indentedCurrentLine()).join(`
`)}getCurrentLineLength(){return this.currentLine.length}indentedCurrentLine(){let t=this.currentLine.padStart(this.currentLine.length+2*this.currentIndent);return this.marginSymbol?this.marginSymbol+t.slice(1):t}};Ms();c();u();p();m();d();l();c();u();p();m();d();l();var gn=class{constructor(t){this.value=t}write(t){t.write(this.value)}markAsError(){this.value.markAsError()}};c();u();p();m();d();l();var yn=e=>e,hn={bold:yn,red:yn,green:yn,dim:yn,enabled:!1},Ls={bold:Yr,red:dt,green:rs,dim:Zr,enabled:!0},Et={write(e){e.writeLine(",")}};c();u();p();m();d();l();var Se=class{constructor(t){this.contents=t}isUnderlined=!1;color=t=>t;underline(){return this.isUnderlined=!0,this}setColor(t){return this.color=t,this}write(t){let r=t.getCurrentLineLength();t.write(this.color(this.contents)),this.isUnderlined&&t.afterNextNewline(()=>{t.write(" ".repeat(r)).writeLine(this.color("~".repeat(this.contents.length)))})}};c();u();p();m();d();l();var Be=class{hasError=!1;markAsError(){return this.hasError=!0,this}};var xt=class extends Be{items=[];addItem(t){return this.items.push(new gn(t)),this}getField(t){return this.items[t]}getPrintWidth(){return this.items.length===0?2:Math.max(...this.items.map(r=>r.value.getPrintWidth()))+2}write(t){if(this.items.length===0){this.writeEmpty(t);return}this.writeWithItems(t)}writeEmpty(t){let r=new Se("[]");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithItems(t){let{colors:r}=t.context;t.writeLine("[").withIndent(()=>t.writeJoined(Et,this.items).newLine()).write("]"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(r.red("~".repeat(this.getPrintWidth())))})}asObject(){}};var Pt=class e extends Be{fields={};suggestions=[];addField(t){this.fields[t.name]=t}addSuggestion(t){this.suggestions.push(t)}getField(t){return this.fields[t]}getDeepField(t){let[r,...n]=t,i=this.getField(r);if(!i)return;let o=i;for(let s of n){let a;if(o.value instanceof e?a=o.value.getField(s):o.value instanceof xt&&(a=o.value.getField(Number(s))),!a)return;o=a}return o}getDeepFieldValue(t){return t.length===0?this:this.getDeepField(t)?.value}hasField(t){return!!this.getField(t)}removeAllFields(){this.fields={}}removeField(t){delete this.fields[t]}getFields(){return this.fields}isEmpty(){return Object.keys(this.fields).length===0}getFieldValue(t){return this.getField(t)?.value}getDeepSubSelectionValue(t){let r=this;for(let n of t){if(!(r instanceof e))return;let i=r.getSubSelectionValue(n);if(!i)return;r=i}return r}getDeepSelectionParent(t){let r=this.getSelectionParent();if(!r)return;let n=r;for(let i of t){let o=n.value.getFieldValue(i);if(!o||!(o instanceof e))return;let s=o.getSelectionParent();if(!s)return;n=s}return n}getSelectionParent(){let t=this.getField("select")?.value.asObject();if(t)return{kind:"select",value:t};let r=this.getField("include")?.value.asObject();if(r)return{kind:"include",value:r}}getSubSelectionValue(t){return this.getSelectionParent()?.value.fields[t].value}getPrintWidth(){let t=Object.values(this.fields);return t.length==0?2:Math.max(...t.map(n=>n.getPrintWidth()))+2}write(t){let r=Object.values(this.fields);if(r.length===0&&this.suggestions.length===0){this.writeEmpty(t);return}this.writeWithContents(t,r)}asObject(){return this}writeEmpty(t){let r=new Se("{}");this.hasError&&r.setColor(t.context.colors.red).underline(),t.write(r)}writeWithContents(t,r){t.writeLine("{").withIndent(()=>{t.writeJoined(Et,[...r,...this.suggestions]).newLine()}),t.write("}"),this.hasError&&t.afterNextNewline(()=>{t.writeLine(t.context.colors.red("~".repeat(this.getPrintWidth())))})}};c();u();p();m();d();l();var re=class extends Be{constructor(r){super();this.text=r}getPrintWidth(){return this.text.length}write(r){let n=new Se(this.text);this.hasError&&n.underline().setColor(r.context.colors.red),r.write(n)}asObject(){}};c();u();p();m();d();l();var rr=class{fields=[];addField(t,r){return this.fields.push({write(n){let{green:i,dim:o}=n.context.colors;n.write(i(o(`${t}: ${r}`))).addMarginSymbol(i(o("+")))}}),this}write(t){let{colors:{green:r}}=t.context;t.writeLine(r("{")).withIndent(()=>{t.writeJoined(Et,this.fields).newLine()}).write(r("}")).addMarginSymbol(r("+"))}};function fn(e,t,r){switch(e.kind){case"MutuallyExclusiveFields":lp(e,t);break;case"IncludeOnScalar":cp(e,t);break;case"EmptySelection":up(e,t,r);break;case"UnknownSelectionField":fp(e,t);break;case"InvalidSelectionValue":gp(e,t);break;case"UnknownArgument":yp(e,t);break;case"UnknownInputField":hp(e,t);break;case"RequiredArgumentMissing":wp(e,t);break;case"InvalidArgumentType":bp(e,t);break;case"InvalidArgumentValue":Ep(e,t);break;case"ValueTooLarge":xp(e,t);break;case"SomeFieldsMissing":Pp(e,t);break;case"TooManyFieldsGiven":Tp(e,t);break;case"Union":Ds(e,t,r);break;default:throw new Error("not implemented: "+e.kind)}}function lp(e,t){let r=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();r&&(r.getField(e.firstField)?.markAsError(),r.getField(e.secondField)?.markAsError()),t.addErrorMessage(n=>`Please ${n.bold("either")} use ${n.green(`\`${e.firstField}\``)} or ${n.green(`\`${e.secondField}\``)}, but ${n.red("not both")} at the same time.`)}function cp(e,t){let[r,n]=Tt(e.selectionPath),i=e.outputType,o=t.arguments.getDeepSelectionParent(r)?.value;if(o&&(o.getField(n)?.markAsError(),i))for(let s of i.fields)s.isRelation&&o.addSuggestion(new he(s.name,"true"));t.addErrorMessage(s=>{let a=`Invalid scalar field ${s.red(`\`${n}\``)} for ${s.bold("include")} statement`;return i?a+=` on model ${s.bold(i.name)}. ${nr(s)}`:a+=".",a+=`
Note that ${s.bold("include")} statements only accept relation fields.`,a})}function up(e,t,r){let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getField("omit")?.value.asObject();if(i){pp(e,t,i);return}if(n.hasField("select")){mp(e,t);return}}if(r?.[qe(e.outputType.name)]){dp(e,t);return}t.addErrorMessage(()=>`Unknown field at "${e.selectionPath.join(".")} selection"`)}function pp(e,t,r){r.removeAllFields();for(let n of e.outputType.fields)r.addSuggestion(new he(n.name,"false"));t.addErrorMessage(n=>`The ${n.red("omit")} statement includes every field of the model ${n.bold(e.outputType.name)}. At least one field must be included in the result`)}function mp(e,t){let r=e.outputType,n=t.arguments.getDeepSelectionParent(e.selectionPath)?.value,i=n?.isEmpty()??!1;n&&(n.removeAllFields(),Vs(n,r)),t.addErrorMessage(o=>i?`The ${o.red("`select`")} statement for type ${o.bold(r.name)} must not be empty. ${nr(o)}`:`The ${o.red("`select`")} statement for type ${o.bold(r.name)} needs ${o.bold("at least one truthy value")}.`)}function dp(e,t){let r=new rr;for(let i of e.outputType.fields)i.isRelation||r.addField(i.name,"false");let n=new he("omit",r).makeRequired();if(e.selectionPath.length===0)t.arguments.addSuggestion(n);else{let[i,o]=Tt(e.selectionPath),a=t.arguments.getDeepSelectionParent(i)?.value.asObject()?.getField(o);if(a){let f=a?.value.asObject()??new Pt;f.addSuggestion(n),a.value=f}}t.addErrorMessage(i=>`The global ${i.red("omit")} configuration excludes every field of the model ${i.bold(e.outputType.name)}. At least one field must be included in the result`)}function fp(e,t){let r=qs(e.selectionPath,t);if(r.parentKind!=="unknown"){r.field.markAsError();let n=r.parent;switch(r.parentKind){case"select":Vs(n,e.outputType);break;case"include":vp(n,e.outputType);break;case"omit":Ap(n,e.outputType);break}}t.addErrorMessage(n=>{let i=[`Unknown field ${n.red(`\`${r.fieldName}\``)}`];return r.parentKind!=="unknown"&&i.push(`for ${n.bold(r.parentKind)} statement`),i.push(`on model ${n.bold(`\`${e.outputType.name}\``)}.`),i.push(nr(n)),i.join(" ")})}function gp(e,t){let r=qs(e.selectionPath,t);r.parentKind!=="unknown"&&r.field.value.markAsError(),t.addErrorMessage(n=>`Invalid value for selection field \`${n.red(r.fieldName)}\`: ${e.underlyingError}`)}function yp(e,t){let r=e.argumentPath[0],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&(n.getField(r)?.markAsError(),Rp(n,e.arguments)),t.addErrorMessage(i=>Fs(i,r,e.arguments.map(o=>o.name)))}function hp(e,t){let[r,n]=Tt(e.argumentPath),i=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(i){i.getDeepField(e.argumentPath)?.markAsError();let o=i.getDeepFieldValue(r)?.asObject();o&&Bs(o,e.inputType)}t.addErrorMessage(o=>Fs(o,n,e.inputType.fields.map(s=>s.name)))}function Fs(e,t,r){let n=[`Unknown argument \`${e.red(t)}\`.`],i=Sp(t,r);return i&&n.push(`Did you mean \`${e.green(i)}\`?`),r.length>0&&n.push(nr(e)),n.join(" ")}function wp(e,t){let r;t.addErrorMessage(f=>r?.value instanceof re&&r.value.text==="null"?`Argument \`${f.green(o)}\` must not be ${f.red("null")}.`:`Argument \`${f.green(o)}\` is missing.`);let n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(!n)return;let[i,o]=Tt(e.argumentPath),s=new rr,a=n.getDeepFieldValue(i)?.asObject();if(a){if(r=a.getField(o),r&&a.removeField(o),e.inputTypes.length===1&&e.inputTypes[0].kind==="object"){for(let f of e.inputTypes[0].fields)s.addField(f.name,f.typeNames.join(" | "));a.addSuggestion(new he(o,s).makeRequired())}else{let f=e.inputTypes.map($s).join(" | ");a.addSuggestion(new he(o,f).makeRequired())}if(e.dependentArgumentPath){n.getDeepField(e.dependentArgumentPath)?.markAsError();let[,f]=Tt(e.dependentArgumentPath);t.addErrorMessage(E=>`Argument \`${E.green(o)}\` is required because argument \`${E.green(f)}\` was provided.`)}}}function $s(e){return e.kind==="list"?`${$s(e.elementType)}[]`:e.name}function bp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=wn("or",e.argument.typeNames.map(s=>i.green(s)));return`Argument \`${i.bold(r)}\`: Invalid value provided. Expected ${o}, provided ${i.red(e.inferredType)}.`})}function Ep(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();n&&n.getDeepFieldValue(e.argumentPath)?.markAsError(),t.addErrorMessage(i=>{let o=[`Invalid value for argument \`${i.bold(r)}\``];if(e.underlyingError&&o.push(`: ${e.underlyingError}`),o.push("."),e.argument.typeNames.length>0){let s=wn("or",e.argument.typeNames.map(a=>i.green(a)));o.push(` Expected ${s}.`)}return o.join("")})}function xp(e,t){let r=e.argument.name,n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i;if(n){let s=n.getDeepField(e.argumentPath)?.value;s?.markAsError(),s instanceof re&&(i=s.text)}t.addErrorMessage(o=>{let s=["Unable to fit value"];return i&&s.push(o.red(i)),s.push(`into a 64-bit signed integer for field \`${o.bold(r)}\``),s.join(" ")})}function Pp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject();if(n){let i=n.getDeepFieldValue(e.argumentPath)?.asObject();i&&Bs(i,e.inputType)}t.addErrorMessage(i=>{let o=[`Argument \`${i.bold(r)}\` of type ${i.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1?e.constraints.requiredFields?o.push(`${i.green("at least one of")} ${wn("or",e.constraints.requiredFields.map(s=>`\`${i.bold(s)}\``))} arguments.`):o.push(`${i.green("at least one")} argument.`):o.push(`${i.green(`at least ${e.constraints.minFieldCount}`)} arguments.`),o.push(nr(i)),o.join(" ")})}function Tp(e,t){let r=e.argumentPath[e.argumentPath.length-1],n=t.arguments.getDeepSubSelectionValue(e.selectionPath)?.asObject(),i=[];if(n){let o=n.getDeepFieldValue(e.argumentPath)?.asObject();o&&(o.markAsError(),i=Object.keys(o.getFields()))}t.addErrorMessage(o=>{let s=[`Argument \`${o.bold(r)}\` of type ${o.bold(e.inputType.name)} needs`];return e.constraints.minFieldCount===1&&e.constraints.maxFieldCount==1?s.push(`${o.green("exactly one")} argument,`):e.constraints.maxFieldCount==1?s.push(`${o.green("at most one")} argument,`):s.push(`${o.green(`at most ${e.constraints.maxFieldCount}`)} arguments,`),s.push(`but you provided ${wn("and",i.map(a=>o.red(a)))}. Please choose`),e.constraints.maxFieldCount===1?s.push("one."):s.push(`${e.constraints.maxFieldCount}.`),s.join(" ")})}function Vs(e,t){for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,"true"))}function vp(e,t){for(let r of t.fields)r.isRelation&&!e.hasField(r.name)&&e.addSuggestion(new he(r.name,"true"))}function Ap(e,t){for(let r of t.fields)!e.hasField(r.name)&&!r.isRelation&&e.addSuggestion(new he(r.name,"true"))}function Rp(e,t){for(let r of t)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function qs(e,t){let[r,n]=Tt(e),i=t.arguments.getDeepSubSelectionValue(r)?.asObject();if(!i)return{parentKind:"unknown",fieldName:n};let o=i.getFieldValue("select")?.asObject(),s=i.getFieldValue("include")?.asObject(),a=i.getFieldValue("omit")?.asObject(),f=o?.getField(n);return o&&f?{parentKind:"select",parent:o,field:f,fieldName:n}:(f=s?.getField(n),s&&f?{parentKind:"include",field:f,parent:s,fieldName:n}:(f=a?.getField(n),a&&f?{parentKind:"omit",field:f,parent:a,fieldName:n}:{parentKind:"unknown",fieldName:n}))}function Bs(e,t){if(t.kind==="object")for(let r of t.fields)e.hasField(r.name)||e.addSuggestion(new he(r.name,r.typeNames.join(" | ")))}function Tt(e){let t=[...e],r=t.pop();if(!r)throw new Error("unexpected empty path");return[t,r]}function nr({green:e,enabled:t}){return"Available options are "+(t?`listed in ${e("green")}`:"marked with ?")+"."}function wn(e,t){if(t.length===1)return t[0];let r=[...t],n=r.pop();return`${r.join(", ")} ${e} ${n}`}var Cp=3;function Sp(e,t){let r=1/0,n;for(let i of t){let o=(0,Us.default)(e,i);o>Cp||o<r&&(r=o,n=i)}return n}c();u();p();m();d();l();c();u();p();m();d();l();var ir=class{modelName;name;typeName;isList;isEnum;constructor(t,r,n,i,o){this.modelName=t,this.name=r,this.typeName=n,this.isList=i,this.isEnum=o}_toGraphQLInputType(){let t=this.isList?"List":"",r=this.isEnum?"Enum":"";return`${t}${r}${this.typeName}FieldRefInput<${this.modelName}>`}};function vt(e){return e instanceof ir}c();u();p();m();d();l();var bn=Symbol(),Ti=new WeakMap,Ne=class{constructor(t){t===bn?Ti.set(this,`Prisma.${this._getName()}`):Ti.set(this,`new Prisma.${this._getNamespace()}.${this._getName()}()`)}_getName(){return this.constructor.name}toString(){return Ti.get(this)}},or=class extends Ne{_getNamespace(){return"NullTypes"}},sr=class extends or{#t};Ai(sr,"DbNull");var ar=class extends or{#t};Ai(ar,"JsonNull");var lr=class extends or{#t};Ai(lr,"AnyNull");var vi={classes:{DbNull:sr,JsonNull:ar,AnyNull:lr},instances:{DbNull:new sr(bn),JsonNull:new ar(bn),AnyNull:new lr(bn)}};function Ai(e,t){Object.defineProperty(e,"name",{value:t,configurable:!0})}c();u();p();m();d();l();var js=": ",En=class{constructor(t,r){this.name=t;this.value=r}hasError=!1;markAsError(){this.hasError=!0}getPrintWidth(){return this.name.length+this.value.getPrintWidth()+js.length}write(t){let r=new Se(this.name);this.hasError&&r.underline().setColor(t.context.colors.red),t.write(r).write(js).write(this.value)}};var Ri=class{arguments;errorMessages=[];constructor(t){this.arguments=t}write(t){t.write(this.arguments)}addErrorMessage(t){this.errorMessages.push(t)}renderAllMessages(t){return this.errorMessages.map(r=>r(t)).join(`
`)}};function At(e){return new Ri(Qs(e))}function Qs(e){let t=new Pt;for(let[r,n]of Object.entries(e)){let i=new En(r,Hs(n));t.addField(i)}return t}function Hs(e){if(typeof e=="string")return new re(JSON.stringify(e));if(typeof e=="number"||typeof e=="boolean")return new re(String(e));if(typeof e=="bigint")return new re(`${e}n`);if(e===null)return new re("null");if(e===void 0)return new re("undefined");if(wt(e))return new re(`new Prisma.Decimal("${e.toFixed()}")`);if(e instanceof Uint8Array)return y.isBuffer(e)?new re(`Buffer.alloc(${e.byteLength})`):new re(`new Uint8Array(${e.byteLength})`);if(e instanceof Date){let t=pn(e)?e.toISOString():"Invalid Date";return new re(`new Date("${t}")`)}return e instanceof Ne?new re(`Prisma.${e._getName()}`):vt(e)?new re(`prisma.${qe(e.modelName)}.$fields.${e.name}`):Array.isArray(e)?Ip(e):typeof e=="object"?Qs(e):new re(Object.prototype.toString.call(e))}function Ip(e){let t=new xt;for(let r of e)t.addItem(Hs(r));return t}function xn(e,t){let r=t==="pretty"?Ls:hn,n=e.renderAllMessages(r),i=new bt(0,{colors:r}).write(e).toString();return{message:n,args:i}}function Pn({args:e,errors:t,errorFormat:r,callsite:n,originalMethod:i,clientVersion:o,globalOmit:s}){let a=At(e);for(let R of t)fn(R,a,s);let{message:f,args:E}=xn(a,r),A=dn({message:f,callsite:n,originalMethod:i,showColors:r==="pretty",callArguments:E});throw new oe(A,{clientVersion:o})}c();u();p();m();d();l();c();u();p();m();d();l();function Ie(e){return e.replace(/^./,t=>t.toLowerCase())}c();u();p();m();d();l();function Js(e,t,r){let n=Ie(r);return!t.result||!(t.result.$allModels||t.result[n])?e:kp({...e,...Gs(t.name,e,t.result.$allModels),...Gs(t.name,e,t.result[n])})}function kp(e){let t=new Ce,r=(n,i)=>t.getOrCreate(n,()=>i.has(n)?[n]:(i.add(n),e[n]?e[n].needs.flatMap(o=>r(o,i)):[n]));return cn(e,n=>({...n,needs:r(n.name,new Set)}))}function Gs(e,t,r){return r?cn(r,({needs:n,compute:i},o)=>({name:o,needs:n?Object.keys(n).filter(s=>n[s]):[],compute:Op(t,o,i)})):{}}function Op(e,t,r){let n=e?.[t]?.compute;return n?i=>r({...i,[t]:n(i)}):r}function Ws(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(e[n.name])for(let i of n.needs)r[i]=!0;return r}function Ks(e,t){if(!t)return e;let r={...e};for(let n of Object.values(t))if(!e[n.name])for(let i of n.needs)delete r[i];return r}var Tn=class{constructor(t,r){this.extension=t;this.previous=r}computedFieldsCache=new Ce;modelExtensionsCache=new Ce;queryCallbacksCache=new Ce;clientExtensions=er(()=>this.extension.client?{...this.previous?.getAllClientExtensions(),...this.extension.client}:this.previous?.getAllClientExtensions());batchCallbacks=er(()=>{let t=this.previous?.getAllBatchQueryCallbacks()??[],r=this.extension.query?.$__internalBatch;return r?t.concat(r):t});getAllComputedFields(t){return this.computedFieldsCache.getOrCreate(t,()=>Js(this.previous?.getAllComputedFields(t),this.extension,t))}getAllClientExtensions(){return this.clientExtensions.get()}getAllModelExtensions(t){return this.modelExtensionsCache.getOrCreate(t,()=>{let r=Ie(t);return!this.extension.model||!(this.extension.model[r]||this.extension.model.$allModels)?this.previous?.getAllModelExtensions(t):{...this.previous?.getAllModelExtensions(t),...this.extension.model.$allModels,...this.extension.model[r]}})}getAllQueryCallbacks(t,r){return this.queryCallbacksCache.getOrCreate(`${t}:${r}`,()=>{let n=this.previous?.getAllQueryCallbacks(t,r)??[],i=[],o=this.extension.query;return!o||!(o[t]||o.$allModels||o[r]||o.$allOperations)?n:(o[t]!==void 0&&(o[t][r]!==void 0&&i.push(o[t][r]),o[t].$allOperations!==void 0&&i.push(o[t].$allOperations)),t!=="$none"&&o.$allModels!==void 0&&(o.$allModels[r]!==void 0&&i.push(o.$allModels[r]),o.$allModels.$allOperations!==void 0&&i.push(o.$allModels.$allOperations)),o[r]!==void 0&&i.push(o[r]),o.$allOperations!==void 0&&i.push(o.$allOperations),n.concat(i))})}getAllBatchQueryCallbacks(){return this.batchCallbacks.get()}},Rt=class e{constructor(t){this.head=t}static empty(){return new e}static single(t){return new e(new Tn(t))}isEmpty(){return this.head===void 0}append(t){return new e(new Tn(t,this.head))}getAllComputedFields(t){return this.head?.getAllComputedFields(t)}getAllClientExtensions(){return this.head?.getAllClientExtensions()}getAllModelExtensions(t){return this.head?.getAllModelExtensions(t)}getAllQueryCallbacks(t,r){return this.head?.getAllQueryCallbacks(t,r)??[]}getAllBatchQueryCallbacks(){return this.head?.getAllBatchQueryCallbacks()??[]}};c();u();p();m();d();l();var vn=class{constructor(t){this.name=t}};function zs(e){return e instanceof vn}function Dp(e){return new vn(e)}c();u();p();m();d();l();c();u();p();m();d();l();var Ys=Symbol(),cr=class{constructor(t){if(t!==Ys)throw new Error("Skip instance can not be constructed directly")}ifUndefined(t){return t===void 0?Ci:t}},Ci=new cr(Ys);function ke(e){return e instanceof cr}var _p={findUnique:"findUnique",findUniqueOrThrow:"findUniqueOrThrow",findFirst:"findFirst",findFirstOrThrow:"findFirstOrThrow",findMany:"findMany",count:"aggregate",create:"createOne",createMany:"createMany",createManyAndReturn:"createManyAndReturn",update:"updateOne",updateMany:"updateMany",updateManyAndReturn:"updateManyAndReturn",upsert:"upsertOne",delete:"deleteOne",deleteMany:"deleteMany",executeRaw:"executeRaw",queryRaw:"queryRaw",aggregate:"aggregate",groupBy:"groupBy",runCommandRaw:"runCommandRaw",findRaw:"findRaw",aggregateRaw:"aggregateRaw"},Zs="explicitly `undefined` values are not allowed";function Ii({modelName:e,action:t,args:r,runtimeDataModel:n,extensions:i=Rt.empty(),callsite:o,clientMethod:s,errorFormat:a,clientVersion:f,previewFeatures:E,globalOmit:A}){let R=new Si({runtimeDataModel:n,modelName:e,action:t,rootArgs:r,callsite:o,extensions:i,selectionPath:[],argumentPath:[],originalMethod:s,errorFormat:a,clientVersion:f,previewFeatures:E,globalOmit:A});return{modelName:e,action:_p[t],query:ur(r,R)}}function ur({select:e,include:t,...r}={},n){let i=r.omit;return delete r.omit,{arguments:ea(r,n),selection:Mp(e,t,i,n)}}function Mp(e,t,r,n){return e?(t?n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"include",secondField:"select",selectionPath:n.getSelectionPath()}):r&&n.throwValidationError({kind:"MutuallyExclusiveFields",firstField:"omit",secondField:"select",selectionPath:n.getSelectionPath()}),Fp(e,n)):Np(n,t,r)}function Np(e,t,r){let n={};return e.modelOrType&&!e.isRawAction()&&(n.$composites=!0,n.$scalars=!0),t&&Lp(n,t,e),Up(n,r,e),n}function Lp(e,t,r){for(let[n,i]of Object.entries(t)){if(ke(i))continue;let o=r.nestSelection(n);if(ki(i,o),i===!1||i===void 0){e[n]=!1;continue}let s=r.findField(n);if(s&&s.kind!=="object"&&r.throwValidationError({kind:"IncludeOnScalar",selectionPath:r.getSelectionPath().concat(n),outputType:r.getOutputTypeDescription()}),s){e[n]=ur(i===!0?{}:i,o);continue}if(i===!0){e[n]=!0;continue}e[n]=ur(i,o)}}function Up(e,t,r){let n=r.getComputedFields(),i={...r.getGlobalOmit(),...t},o=Ks(i,n);for(let[s,a]of Object.entries(o)){if(ke(a))continue;ki(a,r.nestSelection(s));let f=r.findField(s);n?.[s]&&!f||(e[s]=!a)}}function Fp(e,t){let r={},n=t.getComputedFields(),i=Ws(e,n);for(let[o,s]of Object.entries(i)){if(ke(s))continue;let a=t.nestSelection(o);ki(s,a);let f=t.findField(o);if(!(n?.[o]&&!f)){if(s===!1||s===void 0||ke(s)){r[o]=!1;continue}if(s===!0){f?.kind==="object"?r[o]=ur({},a):r[o]=!0;continue}r[o]=ur(s,a)}}return r}function Xs(e,t){if(e===null)return null;if(typeof e=="string"||typeof e=="number"||typeof e=="boolean")return e;if(typeof e=="bigint")return{$type:"BigInt",value:String(e)};if(ht(e)){if(pn(e))return{$type:"DateTime",value:e.toISOString()};t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:["Date"]},underlyingError:"Provided Date object is invalid"})}if(zs(e))return{$type:"Param",value:e.name};if(vt(e))return{$type:"FieldRef",value:{_ref:e.name,_container:e.modelName}};if(Array.isArray(e))return $p(e,t);if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{$type:"Bytes",value:y.from(r,n,i).toString("base64")}}if(Vp(e))return e.values;if(wt(e))return{$type:"Decimal",value:e.toFixed()};if(e instanceof Ne){if(e!==vi.instances[e._getName()])throw new Error("Invalid ObjectEnumValue");return{$type:"Enum",value:e._getName()}}if(qp(e))return e.toJSON();if(typeof e=="object")return ea(e,t);t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:t.getSelectionPath(),argumentPath:t.getArgumentPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:`We could not serialize ${Object.prototype.toString.call(e)} value. Serialize the object to JSON or implement a ".toJSON()" method on it`})}function ea(e,t){if(e.$type)return{$type:"Raw",value:e};let r={};for(let n in e){let i=e[n],o=t.nestArgument(n);ke(i)||(i!==void 0?r[n]=Xs(i,o):t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidArgumentValue",argumentPath:o.getArgumentPath(),selectionPath:t.getSelectionPath(),argument:{name:t.getArgumentName(),typeNames:[]},underlyingError:Zs}))}return r}function $p(e,t){let r=[];for(let n=0;n<e.length;n++){let i=t.nestArgument(String(n)),o=e[n];if(o===void 0||ke(o)){let s=o===void 0?"undefined":"Prisma.skip";t.throwValidationError({kind:"InvalidArgumentValue",selectionPath:i.getSelectionPath(),argumentPath:i.getArgumentPath(),argument:{name:`${t.getArgumentName()}[${n}]`,typeNames:[]},underlyingError:`Can not use \`${s}\` value within array. Use \`null\` or filter out \`${s}\` values`})}r.push(Xs(o,i))}return r}function Vp(e){return typeof e=="object"&&e!==null&&e.__prismaRawParameters__===!0}function qp(e){return typeof e=="object"&&e!==null&&typeof e.toJSON=="function"}function ki(e,t){e===void 0&&t.isPreviewFeatureOn("strictUndefinedChecks")&&t.throwValidationError({kind:"InvalidSelectionValue",selectionPath:t.getSelectionPath(),underlyingError:Zs})}var Si=class e{constructor(t){this.params=t;this.params.modelName&&(this.modelOrType=this.params.runtimeDataModel.models[this.params.modelName]??this.params.runtimeDataModel.types[this.params.modelName])}modelOrType;throwValidationError(t){Pn({errors:[t],originalMethod:this.params.originalMethod,args:this.params.rootArgs??{},callsite:this.params.callsite,errorFormat:this.params.errorFormat,clientVersion:this.params.clientVersion,globalOmit:this.params.globalOmit})}getSelectionPath(){return this.params.selectionPath}getArgumentPath(){return this.params.argumentPath}getArgumentName(){return this.params.argumentPath[this.params.argumentPath.length-1]}getOutputTypeDescription(){if(!(!this.params.modelName||!this.modelOrType))return{name:this.params.modelName,fields:this.modelOrType.fields.map(t=>({name:t.name,typeName:"boolean",isRelation:t.kind==="object"}))}}isRawAction(){return["executeRaw","queryRaw","runCommandRaw","findRaw","aggregateRaw"].includes(this.params.action)}isPreviewFeatureOn(t){return this.params.previewFeatures.includes(t)}getComputedFields(){if(this.params.modelName)return this.params.extensions.getAllComputedFields(this.params.modelName)}findField(t){return this.modelOrType?.fields.find(r=>r.name===t)}nestSelection(t){let r=this.findField(t),n=r?.kind==="object"?r.type:void 0;return new e({...this.params,modelName:n,selectionPath:this.params.selectionPath.concat(t)})}getGlobalOmit(){return this.params.modelName&&this.shouldApplyGlobalOmit()?this.params.globalOmit?.[qe(this.params.modelName)]??{}:{}}shouldApplyGlobalOmit(){switch(this.params.action){case"findFirst":case"findFirstOrThrow":case"findUniqueOrThrow":case"findMany":case"upsert":case"findUnique":case"createManyAndReturn":case"create":case"update":case"updateManyAndReturn":case"delete":return!0;case"executeRaw":case"aggregateRaw":case"runCommandRaw":case"findRaw":case"createMany":case"deleteMany":case"groupBy":case"updateMany":case"count":case"aggregate":case"queryRaw":return!1;default:Me(this.params.action,"Unknown action")}}nestArgument(t){return new e({...this.params,argumentPath:this.params.argumentPath.concat(t)})}};c();u();p();m();d();l();function ta(e){if(!e._hasPreviewFlag("metrics"))throw new oe("`metrics` preview feature must be enabled in order to access metrics API",{clientVersion:e._clientVersion})}var pr=class{_client;constructor(t){this._client=t}prometheus(t){return ta(this._client),this._client._engine.metrics({format:"prometheus",...t})}json(t){return ta(this._client),this._client._engine.metrics({format:"json",...t})}};c();u();p();m();d();l();function Bp(e,t){let r=er(()=>jp(t));Object.defineProperty(e,"dmmf",{get:()=>r.get()})}function jp(e){throw new Error("Prisma.dmmf is not available when running in edge runtimes.")}function Oi(e){return Object.entries(e).map(([t,r])=>({name:t,...r}))}c();u();p();m();d();l();var Di=new WeakMap,An="$$PrismaTypedSql",mr=class{constructor(t,r){Di.set(this,{sql:t,values:r}),Object.defineProperty(this,An,{value:An})}get sql(){return Di.get(this).sql}get values(){return Di.get(this).values}};function Qp(e){return(...t)=>new mr(e,t)}function Rn(e){return e!=null&&e[An]===An}c();u();p();m();d();l();var Gc=$e(fi());c();u();p();m();d();l();ra();ms();hs();c();u();p();m();d();l();var we=class e{constructor(t,r){if(t.length-1!==r.length)throw t.length===0?new TypeError("Expected at least 1 string"):new TypeError(`Expected ${t.length} strings to have ${t.length-1} values`);let n=r.reduce((s,a)=>s+(a instanceof e?a.values.length:1),0);this.values=new Array(n),this.strings=new Array(n+1),this.strings[0]=t[0];let i=0,o=0;for(;i<r.length;){let s=r[i++],a=t[i];if(s instanceof e){this.strings[o]+=s.strings[0];let f=0;for(;f<s.values.length;)this.values[o++]=s.values[f++],this.strings[o]=s.strings[f];this.strings[o]+=a}else this.values[o++]=s,this.strings[o]=a}}get sql(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`?${this.strings[r++]}`;return n}get statement(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`:${r}${this.strings[r++]}`;return n}get text(){let t=this.strings.length,r=1,n=this.strings[0];for(;r<t;)n+=`$${r}${this.strings[r++]}`;return n}inspect(){return{sql:this.sql,statement:this.statement,text:this.text,values:this.values}}};function Hp(e,t=",",r="",n=""){if(e.length===0)throw new TypeError("Expected `join([])` to be called with an array of multiple elements, but got an empty array");return new we([r,...Array(e.length-1).fill(t),n],e)}function na(e){return new we([e],[])}var Gp=na("");function ia(e,...t){return new we(e,t)}c();u();p();m();d();l();c();u();p();m();d();l();function dr(e){return{getKeys(){return Object.keys(e)},getPropertyValue(t){return e[t]}}}c();u();p();m();d();l();function se(e,t){return{getKeys(){return[e]},getPropertyValue(){return t()}}}c();u();p();m();d();l();function Xe(e){let t=new Ce;return{getKeys(){return e.getKeys()},getPropertyValue(r){return t.getOrCreate(r,()=>e.getPropertyValue(r))},getPropertyDescriptor(r){return e.getPropertyDescriptor?.(r)}}}c();u();p();m();d();l();c();u();p();m();d();l();var Sn={enumerable:!0,configurable:!0,writable:!0};function In(e){let t=new Set(e);return{getPrototypeOf:()=>Object.prototype,getOwnPropertyDescriptor:()=>Sn,has:(r,n)=>t.has(n),set:(r,n,i)=>t.add(n)&&Reflect.set(r,n,i),ownKeys:()=>[...t]}}var oa=Symbol.for("nodejs.util.inspect.custom");function Pe(e,t){let r=Jp(t),n=new Set,i=new Proxy(e,{get(o,s){if(n.has(s))return o[s];let a=r.get(s);return a?a.getPropertyValue(s):o[s]},has(o,s){if(n.has(s))return!0;let a=r.get(s);return a?a.has?.(s)??!0:Reflect.has(o,s)},ownKeys(o){let s=sa(Reflect.ownKeys(o),r),a=sa(Array.from(r.keys()),r);return[...new Set([...s,...a,...n])]},set(o,s,a){return r.get(s)?.getPropertyDescriptor?.(s)?.writable===!1?!1:(n.add(s),Reflect.set(o,s,a))},getOwnPropertyDescriptor(o,s){let a=Reflect.getOwnPropertyDescriptor(o,s);if(a&&!a.configurable)return a;let f=r.get(s);return f?f.getPropertyDescriptor?{...Sn,...f?.getPropertyDescriptor(s)}:Sn:a},defineProperty(o,s,a){return n.add(s),Reflect.defineProperty(o,s,a)},getPrototypeOf:()=>Object.prototype});return i[oa]=function(){let o={...this};return delete o[oa],o},i}function Jp(e){let t=new Map;for(let r of e){let n=r.getKeys();for(let i of n)t.set(i,r)}return t}function sa(e,t){return e.filter(r=>t.get(r)?.has?.(r)??!0)}c();u();p();m();d();l();function Ct(e){return{getKeys(){return e},has(){return!1},getPropertyValue(){}}}c();u();p();m();d();l();function St(e,t){return{batch:e,transaction:t?.kind==="batch"?{isolationLevel:t.options.isolationLevel}:void 0}}c();u();p();m();d();l();function aa(e){if(e===void 0)return"";let t=At(e);return new bt(0,{colors:hn}).write(t).toString()}c();u();p();m();d();l();var Wp="P2037";function kn({error:e,user_facing_error:t},r,n){return t.error_code?new ee(Kp(t,n),{code:t.error_code,clientVersion:r,meta:t.meta,batchRequestIdx:t.batch_request_idx}):new ie(e,{clientVersion:r,batchRequestIdx:t.batch_request_idx})}function Kp(e,t){let r=e.message;return(t==="postgresql"||t==="postgres"||t==="mysql")&&e.error_code===Wp&&(r+=`
Prisma Accelerate has built-in connection pooling to prevent such errors: https://pris.ly/client/error-accelerate`),r}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var _i=class{getLocation(){return null}};function je(e){return typeof $EnabledCallSite=="function"&&e!=="minimal"?new $EnabledCallSite:new _i}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var la={_avg:!0,_count:!0,_sum:!0,_min:!0,_max:!0};function It(e={}){let t=Yp(e);return Object.entries(t).reduce((n,[i,o])=>(la[i]!==void 0?n.select[i]={select:o}:n[i]=o,n),{select:{}})}function Yp(e={}){return typeof e._count=="boolean"?{...e,_count:{_all:e._count}}:e}function On(e={}){return t=>(typeof e._count=="boolean"&&(t._count=t._count._all),t)}function ca(e,t){let r=On(e);return t({action:"aggregate",unpacker:r,argsMapper:It})(e)}c();u();p();m();d();l();function Zp(e={}){let{select:t,...r}=e;return typeof t=="object"?It({...r,_count:t}):It({...r,_count:{_all:!0}})}function Xp(e={}){return typeof e.select=="object"?t=>On(e)(t)._count:t=>On(e)(t)._count._all}function ua(e,t){return t({action:"count",unpacker:Xp(e),argsMapper:Zp})(e)}c();u();p();m();d();l();function em(e={}){let t=It(e);if(Array.isArray(t.by))for(let r of t.by)typeof r=="string"&&(t.select[r]=!0);else typeof t.by=="string"&&(t.select[t.by]=!0);return t}function tm(e={}){return t=>(typeof e?._count=="boolean"&&t.forEach(r=>{r._count=r._count._all}),t)}function pa(e,t){return t({action:"groupBy",unpacker:tm(e),argsMapper:em})(e)}function ma(e,t,r){if(t==="aggregate")return n=>ca(n,r);if(t==="count")return n=>ua(n,r);if(t==="groupBy")return n=>pa(n,r)}c();u();p();m();d();l();function da(e,t){let r=t.fields.filter(i=>!i.relationName),n=Ss(r,"name");return new Proxy({},{get(i,o){if(o in i||typeof o=="symbol")return i[o];let s=n[o];if(s)return new ir(e,o,s.type,s.isList,s.kind==="enum")},...In(Object.keys(n))})}c();u();p();m();d();l();c();u();p();m();d();l();var fa=e=>Array.isArray(e)?e:e.split("."),Mi=(e,t)=>fa(t).reduce((r,n)=>r&&r[n],e),ga=(e,t,r)=>fa(t).reduceRight((n,i,o,s)=>Object.assign({},Mi(e,s.slice(0,o)),{[i]:n}),r);function rm(e,t){return e===void 0||t===void 0?[]:[...t,"select",e]}function nm(e,t,r){return t===void 0?e??{}:ga(t,r,e||!0)}function Ni(e,t,r,n,i,o){let a=e._runtimeDataModel.models[t].fields.reduce((f,E)=>({...f,[E.name]:E}),{});return f=>{let E=je(e._errorFormat),A=rm(n,i),R=nm(f,o,A),S=r({dataPath:A,callsite:E})(R),C=im(e,t);return new Proxy(S,{get(L,k){if(!C.includes(k))return L[k];let De=[a[k].type,r,k],le=[A,R];return Ni(e,...De,...le)},...In([...C,...Object.getOwnPropertyNames(S)])})}}function im(e,t){return e._runtimeDataModel.models[t].fields.filter(r=>r.kind==="object").map(r=>r.name)}var om=["findUnique","findUniqueOrThrow","findFirst","findFirstOrThrow","create","update","upsert","delete"],sm=["aggregate","count","groupBy"];function Li(e,t){let r=e._extensions.getAllModelExtensions(t)??{},n=[am(e,t),cm(e,t),dr(r),se("name",()=>t),se("$name",()=>t),se("$parent",()=>e._appliedParent)];return Pe({},n)}function am(e,t){let r=Ie(t),n=Object.keys(tr).concat("count");return{getKeys(){return n},getPropertyValue(i){let o=i,s=a=>f=>{let E=je(e._errorFormat);return e._createPrismaPromise(A=>{let R={args:f,dataPath:[],action:o,model:t,clientMethod:`${r}.${i}`,jsModelName:r,transaction:A,callsite:E};return e._request({...R,...a})},{action:o,args:f,model:t})};return om.includes(o)?Ni(e,t,s):lm(i)?ma(e,i,s):s({})}}}function lm(e){return sm.includes(e)}function cm(e,t){return Xe(se("fields",()=>{let r=e._runtimeDataModel.models[t];return da(t,r)}))}c();u();p();m();d();l();function ya(e){return e.replace(/^./,t=>t.toUpperCase())}var Ui=Symbol();function fr(e){let t=[um(e),pm(e),se(Ui,()=>e),se("$parent",()=>e._appliedParent)],r=e._extensions.getAllClientExtensions();return r&&t.push(dr(r)),Pe(e,t)}function um(e){let t=Object.getPrototypeOf(e._originalClient),r=[...new Set(Object.getOwnPropertyNames(t))];return{getKeys(){return r},getPropertyValue(n){return e[n]}}}function pm(e){let t=Object.keys(e._runtimeDataModel.models),r=t.map(Ie),n=[...new Set(t.concat(r))];return Xe({getKeys(){return n},getPropertyValue(i){let o=ya(i);if(e._runtimeDataModel.models[o]!==void 0)return Li(e,o);if(e._runtimeDataModel.models[i]!==void 0)return Li(e,i)},getPropertyDescriptor(i){if(!r.includes(i))return{enumerable:!1}}})}function ha(e){return e[Ui]?e[Ui]:e}function wa(e){if(typeof e=="function")return e(this);if(e.client?.__AccelerateEngine){let r=e.client.__AccelerateEngine;this._originalClient._engine=new r(this._originalClient._accelerateEngineConfig)}let t=Object.create(this._originalClient,{_extensions:{value:this._extensions.append(e)},_appliedParent:{value:this,configurable:!0},$on:{value:void 0}});return fr(t)}c();u();p();m();d();l();c();u();p();m();d();l();function ba({result:e,modelName:t,select:r,omit:n,extensions:i}){let o=i.getAllComputedFields(t);if(!o)return e;let s=[],a=[];for(let f of Object.values(o)){if(n){if(n[f.name])continue;let E=f.needs.filter(A=>n[A]);E.length>0&&a.push(Ct(E))}else if(r){if(!r[f.name])continue;let E=f.needs.filter(A=>!r[A]);E.length>0&&a.push(Ct(E))}mm(e,f.needs)&&s.push(dm(f,Pe(e,s)))}return s.length>0||a.length>0?Pe(e,[...s,...a]):e}function mm(e,t){return t.every(r=>wi(e,r))}function dm(e,t){return Xe(se(e.name,()=>e.compute(t)))}c();u();p();m();d();l();function Dn({visitor:e,result:t,args:r,runtimeDataModel:n,modelName:i}){if(Array.isArray(t)){for(let s=0;s<t.length;s++)t[s]=Dn({result:t[s],args:r,modelName:i,runtimeDataModel:n,visitor:e});return t}let o=e(t,i,r)??t;return r.include&&Ea({includeOrSelect:r.include,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),r.select&&Ea({includeOrSelect:r.select,result:o,parentModelName:i,runtimeDataModel:n,visitor:e}),o}function Ea({includeOrSelect:e,result:t,parentModelName:r,runtimeDataModel:n,visitor:i}){for(let[o,s]of Object.entries(e)){if(!s||t[o]==null||ke(s))continue;let f=n.models[r].fields.find(A=>A.name===o);if(!f||f.kind!=="object"||!f.relationName)continue;let E=typeof s=="object"?s:{};t[o]=Dn({visitor:i,result:t[o],args:E,modelName:f.type,runtimeDataModel:n})}}function xa({result:e,modelName:t,args:r,extensions:n,runtimeDataModel:i,globalOmit:o}){return n.isEmpty()||e==null||typeof e!="object"||!i.models[t]?e:Dn({result:e,args:r??{},modelName:t,runtimeDataModel:i,visitor:(a,f,E)=>{let A=Ie(f);return ba({result:a,modelName:A,select:E.select,omit:E.select?void 0:{...o?.[A],...E.omit},extensions:n})}})}c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();var fm=["$connect","$disconnect","$on","$transaction","$extends"],Pa=fm;function Ta(e){if(e instanceof we)return gm(e);if(Rn(e))return ym(e);if(Array.isArray(e)){let r=[e[0]];for(let n=1;n<e.length;n++)r[n]=gr(e[n]);return r}let t={};for(let r in e)t[r]=gr(e[r]);return t}function gm(e){return new we(e.strings,e.values)}function ym(e){return new mr(e.sql,e.values)}function gr(e){if(typeof e!="object"||e==null||e instanceof Ne||vt(e))return e;if(wt(e))return new me(e.toFixed());if(ht(e))return new Date(+e);if(ArrayBuffer.isView(e))return e.slice(0);if(Array.isArray(e)){let t=e.length,r;for(r=Array(t);t--;)r[t]=gr(e[t]);return r}if(typeof e=="object"){let t={};for(let r in e)r==="__proto__"?Object.defineProperty(t,r,{value:gr(e[r]),configurable:!0,enumerable:!0,writable:!0}):t[r]=gr(e[r]);return t}Me(e,"Unknown value")}function Aa(e,t,r,n=0){return e._createPrismaPromise(i=>{let o=t.customDataProxyFetch;return"transaction"in t&&i!==void 0&&(t.transaction?.kind==="batch"&&t.transaction.lock.then(),t.transaction=i),n===r.length?e._executeRequest(t):r[n]({model:t.model,operation:t.model?t.action:t.clientMethod,args:Ta(t.args??{}),__internalParams:t,query:(s,a=t)=>{let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ia(o,f),a.args=s,Aa(e,a,r,n+1)}})})}function Ra(e,t){let{jsModelName:r,action:n,clientMethod:i}=t,o=r?n:i;if(e._extensions.isEmpty())return e._executeRequest(t);let s=e._extensions.getAllQueryCallbacks(r??"$none",o);return Aa(e,t,s)}function Ca(e){return t=>{let r={requests:t},n=t[0].extensions.getAllBatchQueryCallbacks();return n.length?Sa(r,n,0,e):e(r)}}function Sa(e,t,r,n){if(r===t.length)return n(e);let i=e.customDataProxyFetch,o=e.requests[0].transaction;return t[r]({args:{queries:e.requests.map(s=>({model:s.modelName,operation:s.action,args:s.args})),transaction:o?{isolationLevel:o.kind==="batch"?o.isolationLevel:void 0}:void 0},__internalParams:e,query(s,a=e){let f=a.customDataProxyFetch;return a.customDataProxyFetch=Ia(i,f),Sa(a,t,r+1,n)}})}var va=e=>e;function Ia(e=va,t=va){return r=>e(t(r))}c();u();p();m();d();l();var ka=K("prisma:client"),Oa={Vercel:"vercel","Netlify CI":"netlify"};function Da({postinstall:e,ciName:t,clientVersion:r,generator:n}){if(ka("checkPlatformCaching:postinstall",e),ka("checkPlatformCaching:ciName",t),e===!0&&!(n?.output&&typeof(n.output.fromEnvVar??n.output.value)=="string")&&t&&t in Oa){let i=`Prisma has detected that this project was built on ${t}, which caches dependencies. This leads to an outdated Prisma Client because Prisma's auto-generation isn't triggered. To fix this, make sure to run the \`prisma generate\` command during the build process.

Learn how: https://pris.ly/d/${Oa[t]}-build`;throw console.error(i),new $(i,r)}}c();u();p();m();d();l();function _a(e,t){return e?e.datasources?e.datasources:e.datasourceUrl?{[t[0]]:{url:e.datasourceUrl}}:{}:{}}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();function Ma(e){return e?e.replace(/".*"/g,'"X"').replace(/[\s:\[]([+-]?([0-9]*[.])?[0-9]+)/g,t=>`${t[0]}5`):""}c();u();p();m();d();l();function Na(e){return e.split(`
`).map(t=>t.replace(/^\d{4}-[01]\d-[0-3]\dT[0-2]\d:[0-5]\d:[0-5]\d\.\d+([+-][0-2]\d:[0-5]\d|Z)\s*/,"").replace(/\+\d+\s*ms$/,"")).join(`
`)}c();u();p();m();d();l();var La=$e(As());function Ua({title:e,user:t="prisma",repo:r="prisma",template:n="bug_report.yml",body:i}){return(0,La.default)({user:t,repo:r,template:n,title:e,body:i})}function Fa({version:e,binaryTarget:t,title:r,description:n,engineVersion:i,database:o,query:s}){let a=cs(6e3-(s?.length??0)),f=Na(yt(a)),E=n?`# Description
\`\`\`
${n}
\`\`\``:"",A=yt(`Hi Prisma Team! My Prisma Client just crashed. This is the report:
## Versions

| Name            | Version            |
|-----------------|--------------------|
| Node            | ${g.version?.padEnd(19)}| 
| OS              | ${t?.padEnd(19)}|
| Prisma Client   | ${e?.padEnd(19)}|
| Query Engine    | ${i?.padEnd(19)}|
| Database        | ${o?.padEnd(19)}|

${E}

## Logs
\`\`\`
${f}
\`\`\`

## Client Snippet
\`\`\`ts
// PLEASE FILL YOUR CODE SNIPPET HERE
\`\`\`

## Schema
\`\`\`prisma
// PLEASE ADD YOUR SCHEMA HERE IF POSSIBLE
\`\`\`

## Prisma Engine Query
\`\`\`
${s?Ma(s):""}
\`\`\`
`),R=Ua({title:r,body:A});return`${r}

This is a non-recoverable error which probably happens when the Prisma Query Engine has a panic.

${Xr(R)}

If you want the Prisma team to look into it, please open the link above \u{1F64F}
To increase the chance of success, please post your schema and a snippet of
how you used Prisma Client in the issue. 
`}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();l();c();u();p();m();d();l();l();function F(e,t){throw new Error(t)}function Fi(e,t){return e===t||e!==null&&t!==null&&typeof e=="object"&&typeof t=="object"&&Object.keys(e).length===Object.keys(t).length&&Object.keys(e).every(r=>Fi(e[r],t[r]))}function kt(e,t){let r=Object.keys(e),n=Object.keys(t);return(r.length<n.length?r:n).every(o=>{if(typeof e[o]==typeof t[o]&&typeof e[o]!="object")return e[o]===t[o];if(me.isDecimal(e[o])||me.isDecimal(t[o])){let s=$a(e[o]),a=$a(t[o]);return s&&a&&s.equals(a)}else if(e[o]instanceof Uint8Array||t[o]instanceof Uint8Array){let s=Va(e[o]),a=Va(t[o]);return s&&a&&s.equals(a)}else{if(e[o]instanceof Date||t[o]instanceof Date)return qa(e[o])?.getTime()===qa(t[o])?.getTime();if(typeof e[o]=="bigint"||typeof t[o]=="bigint")return Ba(e[o])===Ba(t[o]);if(typeof e[o]=="number"||typeof t[o]=="number")return ja(e[o])===ja(t[o])}return Fi(e[o],t[o])})}function $a(e){return me.isDecimal(e)?e:typeof e=="number"||typeof e=="string"?new me(e):void 0}function Va(e){return y.isBuffer(e)?e:e instanceof Uint8Array?y.from(e.buffer,e.byteOffset,e.byteLength):typeof e=="string"?y.from(e,"base64"):void 0}function qa(e){return e instanceof Date?e:typeof e=="string"||typeof e=="number"?new Date(e):void 0}function Ba(e){return typeof e=="bigint"?e:typeof e=="number"||typeof e=="string"?BigInt(e):void 0}function ja(e){return typeof e=="number"?e:typeof e=="string"?Number(e):void 0}function yr(e){return JSON.stringify(e,(t,r)=>typeof r=="bigint"?r.toString():ArrayBuffer.isView(r)?y.from(r.buffer,r.byteOffset,r.byteLength).toString("base64"):r)}function hm(e){return e!==null&&typeof e=="object"&&typeof e.$type=="string"}function wm(e,t){let r={};for(let n of Object.keys(e))r[n]=t(e[n],n);return r}function et(e){return e===null?e:Array.isArray(e)?e.map(et):typeof e=="object"?hm(e)?bm(e):e.constructor!==null&&e.constructor.name!=="Object"?e:wm(e,et):e}function bm({$type:e,value:t}){switch(e){case"BigInt":return BigInt(t);case"Bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"DateTime":return new Date(t);case"Decimal":return new v(t);case"Json":return JSON.parse(t);default:F(t,"Unknown tagged value")}}c();u();p();m();d();l();var ae=class extends Error{name="UserFacingError";code;meta;constructor(t,r,n){super(t),this.code=r,this.meta=n??{}}toQueryResponseErrorObject(){return{error:this.message,user_facing_error:{is_panic:!1,message:this.message,meta:this.meta,error_code:this.code}}}};function Ot(e){if(!tn(e))throw e;let t=Em(e),r=Qa(e);throw!t||!r?e:new ae(r,t,{driverAdapterError:e})}function Vi(e){throw tn(e)?new ae(`Raw query failed. Code: \`${e.cause.originalCode??"N/A"}\`. Message: \`${e.cause.originalMessage??Qa(e)}\``,"P2010",{driverAdapterError:e}):e}function Em(e){switch(e.cause.kind){case"AuthenticationFailed":return"P1000";case"DatabaseNotReachable":return"P1001";case"DatabaseDoesNotExist":return"P1003";case"SocketTimeout":return"P1008";case"DatabaseAlreadyExists":return"P1009";case"DatabaseAccessDenied":return"P1010";case"TlsConnectionError":return"P1011";case"ConnectionClosed":return"P1017";case"TransactionAlreadyClosed":return"P1018";case"LengthMismatch":return"P2000";case"UniqueConstraintViolation":return"P2002";case"ForeignKeyConstraintViolation":return"P2003";case"UnsupportedNativeDataType":return"P2010";case"NullConstraintViolation":return"P2011";case"ValueOutOfRange":return"P2020";case"TableDoesNotExist":return"P2021";case"ColumnNotFound":return"P2022";case"InvalidIsolationLevel":case"InconsistentColumnData":return"P2023";case"MissingFullTextSearchIndex":return"P2030";case"TransactionWriteConflict":return"P2034";case"GenericJs":return"P2036";case"TooManyConnections":return"P2037";case"postgres":case"sqlite":case"mysql":case"mssql":return;default:F(e.cause,`Unknown error: ${e.cause}`)}}function Qa(e){switch(e.cause.kind){case"AuthenticationFailed":return`Authentication failed against the database server, the provided database credentials for \`${e.cause.user??"(not available)"}\` are not valid`;case"DatabaseNotReachable":{let t=e.cause.host&&e.cause.port?`${e.cause.host}:${e.cause.port}`:e.cause.host;return`Can't reach database server${t?` at ${t}`:""}`}case"DatabaseDoesNotExist":return`Database \`${e.cause.db??"(not available)"}\` does not exist on the database server`;case"SocketTimeout":return"Operation has timed out";case"DatabaseAlreadyExists":return`Database \`${e.cause.db??"(not available)"}\` already exists on the database server`;case"DatabaseAccessDenied":return`User was denied access on the database \`${e.cause.db??"(not available)"}\``;case"TlsConnectionError":return`Error opening a TLS connection: ${e.cause.reason}`;case"ConnectionClosed":return"Server has closed the connection.";case"TransactionAlreadyClosed":return e.cause.cause;case"LengthMismatch":return`The provided value for the column is too long for the column's type. Column: ${e.cause.column??"(not available)"}`;case"UniqueConstraintViolation":return`Unique constraint failed on the ${$i(e.cause.constraint)}`;case"ForeignKeyConstraintViolation":return`Foreign key constraint violated on the ${$i(e.cause.constraint)}`;case"UnsupportedNativeDataType":return`Failed to deserialize column of type '${e.cause.type}'. If you're using $queryRaw and this column is explicitly marked as \`Unsupported\` in your Prisma schema, try casting this column to any supported Prisma type such as \`String\`.`;case"NullConstraintViolation":return`Null constraint violation on the ${$i(e.cause.constraint)}`;case"ValueOutOfRange":return`Value out of range for the type: ${e.cause.cause}`;case"TableDoesNotExist":return`The table \`${e.cause.table??"(not available)"}\` does not exist in the current database.`;case"ColumnNotFound":return`The column \`${e.cause.column??"(not available)"}\` does not exist in the current database.`;case"InvalidIsolationLevel":return`Error in connector: Conversion error: ${e.cause.level}`;case"InconsistentColumnData":return`Inconsistent column data: ${e.cause.cause}`;case"MissingFullTextSearchIndex":return"Cannot find a fulltext index to use for the native search, try adding a @@fulltext([Fields...]) to your schema";case"TransactionWriteConflict":return"Transaction failed due to a write conflict or a deadlock. Please retry your transaction";case"GenericJs":return`Error in external connector (id ${e.cause.id})`;case"TooManyConnections":return`Too many database connections opened: ${e.cause.cause}`;case"sqlite":case"postgres":case"mysql":case"mssql":return;default:F(e.cause,`Unknown error: ${e.cause}`)}}function $i(e){return e&&"fields"in e?`fields: (${e.fields.map(t=>`\`${t}\``).join(", ")})`:e&&"index"in e?`constraint: \`${e.index}\``:e&&"foreignKey"in e?"foreign key":"(not available)"}function Ha(e,t){let r=e.map(i=>t.keys.reduce((o,s)=>(o[s]=et(i[s]),o),{})),n=new Set(t.nestedSelection);return t.arguments.map(i=>{let o=r.findIndex(s=>kt(s,i));if(o===-1)return t.expectNonEmpty?new ae("An operation failed because it depends on one or more records that were required but not found","P2025"):null;{let s=Object.entries(e[o]).filter(([a])=>n.has(a));return Object.fromEntries(s)}})}c();u();p();m();d();l();l();var G=class extends Error{name="DataMapperError"};function Ja(e,t,r){switch(t.type){case"affectedRows":if(typeof e!="number")throw new G(`Expected an affected rows count, got: ${typeof e} (${e})`);return{count:e};case"object":return Bi(e,t.fields,r,t.skipNulls);case"field":return qi(e,"<result>",t.fieldType,r);default:F(t,`Invalid data mapping type: '${t.type}'`)}}function Bi(e,t,r,n){if(e===null)return null;if(Array.isArray(e)){let i=e;return n&&(i=i.filter(o=>o!==null)),i.map(o=>Ga(o,t,r))}if(typeof e=="object")return Ga(e,t,r);if(typeof e=="string"){let i;try{i=JSON.parse(e)}catch(o){throw new G("Expected an array or object, got a string that is not valid JSON",{cause:o})}return Bi(i,t,r,n)}throw new G(`Expected an array or an object, got: ${typeof e}`)}function Ga(e,t,r){if(typeof e!="object")throw new G(`Expected an object, but got '${typeof e}'`);let n={};for(let[i,o]of Object.entries(t))switch(o.type){case"affectedRows":throw new G(`Unexpected 'AffectedRows' node in data mapping for field '${i}'`);case"object":{if(o.serializedName!==null&&!Object.hasOwn(e,o.serializedName))throw new G(`Missing data field (Object): '${i}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`);let s=o.serializedName!==null?e[o.serializedName]:e;n[i]=Bi(s,o.fields,r,o.skipNulls);break}case"field":{let s=o.dbName;if(Object.hasOwn(e,s))n[i]=xm(e[s],s,o.fieldType,r);else throw new G(`Missing data field (Value): '${s}'; node: ${JSON.stringify(o)}; data: ${JSON.stringify(e)}`)}break;default:F(o,`DataMapper: Invalid data mapping node type: '${o.type}'`)}return n}function xm(e,t,r,n){return e===null?r.arity==="list"?[]:null:r.arity==="list"?e.map((o,s)=>qi(o,`${t}[${s}]`,r,n)):qi(e,t,r,n)}function qi(e,t,r,n){switch(r.type){case"unsupported":return e;case"string":{if(typeof e!="string")throw new G(`Expected a string in column '${t}', got ${typeof e}: ${e}`);return e}case"int":switch(typeof e){case"number":return Math.trunc(e);case"string":{let i=Math.trunc(Number(e));if(Number.isNaN(i)||!Number.isFinite(i))throw new G(`Expected an integer in column '${t}', got string: ${e}`);if(!Number.isSafeInteger(i))throw new G(`Integer value in column '${t}' is too large to represent as a JavaScript number without loss of precision, got: ${e}. Consider using BigInt type.`);return i}default:throw new G(`Expected an integer in column '${t}', got ${typeof e}: ${e}`)}case"bigint":{if(typeof e!="number"&&typeof e!="string")throw new G(`Expected a bigint in column '${t}', got ${typeof e}: ${e}`);return{$type:"BigInt",value:e}}case"float":{if(typeof e=="number")return e;if(typeof e=="string"){let i=Number(e);if(Number.isNaN(i)&&!/^[-+]?nan$/.test(e.toLowerCase()))throw new G(`Expected a float in column '${t}', got string: ${e}`);return i}throw new G(`Expected a float in column '${t}', got ${typeof e}: ${e}`)}case"boolean":{if(typeof e=="boolean")return e;if(typeof e=="number")return e===1;if(typeof e=="string"){if(e==="true"||e==="TRUE"||e==="1")return!0;if(e==="false"||e==="FALSE"||e==="0")return!1;throw new G(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}if(Array.isArray(e)){for(let i of e)if(i!==0)return!0;return!1}throw new G(`Expected a boolean in column '${t}', got ${typeof e}: ${e}`)}case"decimal":if(typeof e!="number"&&typeof e!="string"&&!me.isDecimal(e))throw new G(`Expected a decimal in column '${t}', got ${typeof e}: ${e}`);return{$type:"Decimal",value:e};case"datetime":{if(typeof e=="string")return{$type:"DateTime",value:Tm(e)};if(typeof e=="number"||e instanceof Date)return{$type:"DateTime",value:e};throw new G(`Expected a date in column '${t}', got ${typeof e}: ${e}`)}case"object":return{$type:"Json",value:yr(e)};case"json":return{$type:"Json",value:`${e}`};case"bytes":{switch(r.encoding){case"base64":if(typeof e!="string")throw new G(`Expected a base64-encoded byte array in column '${t}', got ${typeof e}: ${e}`);return{$type:"Bytes",value:e};case"hex":if(typeof e!="string"||!e.startsWith("\\x"))throw new G(`Expected a hex-encoded byte array in column '${t}', got ${typeof e}: ${e}`);return{$type:"Bytes",value:y.from(e.slice(2),"hex").toString("base64")};case"array":if(Array.isArray(e))return{$type:"Bytes",value:y.from(e).toString("base64")};if(e instanceof Uint8Array)return{$type:"Bytes",value:y.from(e).toString("base64")};throw new G(`Expected a byte array in column '${t}', got ${typeof e}: ${e}`);default:F(r.encoding,`DataMapper: Unknown bytes encoding: ${r.encoding}`)}break}case"enum":{let i=n[r.name];if(i===void 0)throw new G(`Unknown enum '${r.name}'`);let o=i[`${e}`];if(o===void 0)throw new G(`Value '${e}' not found in enum '${r.name}'`);return o}default:F(r,`DataMapper: Unknown result type: ${r.type}`)}}var Pm=/\d{2}:\d{2}:\d{2}(?:\.\d+)?(Z|[+-]\d{2}(:?\d{2})?)?$/;function Tm(e){let t=Pm.exec(e);if(t===null)return`${e}Z`;let r=e,[n,i,o]=t;return i!==void 0&&i!=="Z"&&o===void 0?r=`${e}:00`:i===void 0&&(r=`${e}Z`),n.length===e.length?`1970-01-01T${r}`:r}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var hr;(function(e){e[e.INTERNAL=0]="INTERNAL",e[e.SERVER=1]="SERVER",e[e.CLIENT=2]="CLIENT",e[e.PRODUCER=3]="PRODUCER",e[e.CONSUMER=4]="CONSUMER"})(hr||(hr={}));function vm(e){switch(e){case"postgresql":case"postgres":case"prisma+postgres":return"postgresql";case"sqlserver":return"mssql";case"mysql":case"sqlite":case"cockroachdb":case"mongodb":return e;default:F(e,`Unknown provider: ${e}`)}}async function _n({query:e,tracingHelper:t,provider:r,onQuery:n,execute:i}){return await t.runInChildSpan({name:"db_query",kind:hr.CLIENT,attributes:{"db.query.text":e.sql,"db.system.name":vm(r)}},async()=>{let o=new Date,s=w.now(),a=await i(),f=w.now();return n?.({timestamp:o,duration:f-s,query:e.sql,params:e.args}),a})}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();function tt(e,t){var r="000000000"+e;return r.substr(r.length-t)}var Wa=$e(ds(),1);function Am(){try{return Wa.default.hostname()}catch{return g.env._CLUSTER_NETWORK_NAME_||g.env.COMPUTERNAME||"hostname"}}var Ka=2,Rm=tt(g.pid.toString(36),Ka),za=Am(),Cm=za.length,Sm=tt(za.split("").reduce(function(e,t){return+e+t.charCodeAt(0)},+Cm+36).toString(36),Ka);function ji(){return Rm+Sm}c();u();p();m();d();l();c();u();p();m();d();l();function Mn(e){return typeof e=="string"&&/^c[a-z0-9]{20,32}$/.test(e)}function Qi(e){let n=Math.pow(36,4),i=0;function o(){return tt((Math.random()*n<<0).toString(36),4)}function s(){return i=i<n?i:0,i++,i-1}function a(){var f="c",E=new Date().getTime().toString(36),A=tt(s().toString(36),4),R=e(),S=o()+o();return f+E+A+R+S}return a.fingerprint=e,a.isCuid=Mn,a}var Im=Qi(ji);var Ya=Im;var Wl=$e(ql());c();u();p();m();d();l();Ze();c();u();p();m();d();l();var Bl="useandom-26T198340PX75pxJACKVERYMINDBUSHWOLF_GQZbfghjklqvwyzrict";var gd=128,nt,Mt;function yd(e){!nt||nt.length<e?(nt=y.allocUnsafe(e*gd),Yt.getRandomValues(nt),Mt=0):Mt+e>nt.length&&(Yt.getRandomValues(nt),Mt=0),Mt+=e}function Xi(e=21){yd(e|=0);let t="";for(let r=Mt-e;r<Mt;r++)t+=Bl[nt[r]&63];return t}c();u();p();m();d();l();Ze();var Ql="0123456789ABCDEFGHJKMNPQRSTVWXYZ",Tr=32;var hd=16,Hl=10,jl=0xffffffffffff;var it;(function(e){e.Base32IncorrectEncoding="B32_ENC_INVALID",e.DecodeTimeInvalidCharacter="DEC_TIME_CHAR",e.DecodeTimeValueMalformed="DEC_TIME_MALFORMED",e.EncodeTimeNegative="ENC_TIME_NEG",e.EncodeTimeSizeExceeded="ENC_TIME_SIZE_EXCEED",e.EncodeTimeValueMalformed="ENC_TIME_MALFORMED",e.PRNGDetectFailure="PRNG_DETECT",e.ULIDInvalid="ULID_INVALID",e.Unexpected="UNEXPECTED",e.UUIDInvalid="UUID_INVALID"})(it||(it={}));var ot=class extends Error{constructor(t,r){super(`${r} (${t})`),this.name="ULIDError",this.code=t}};function wd(e){let t=Math.floor(e()*Tr);return t===Tr&&(t=Tr-1),Ql.charAt(t)}function bd(e){let t=Ed(),r=t&&(t.crypto||t.msCrypto)||(typeof gt<"u"?gt:null);if(typeof r?.getRandomValues=="function")return()=>{let n=new Uint8Array(1);return r.getRandomValues(n),n[0]/255};if(typeof r?.randomBytes=="function")return()=>r.randomBytes(1).readUInt8()/255;if(gt?.randomBytes)return()=>gt.randomBytes(1).readUInt8()/255;throw new ot(it.PRNGDetectFailure,"Failed to find a reliable PRNG")}function Ed(){return Td()?self:typeof window<"u"?window:typeof globalThis<"u"||typeof globalThis<"u"?globalThis:null}function xd(e,t){let r="";for(;e>0;e--)r=wd(t)+r;return r}function Pd(e,t=Hl){if(isNaN(e))throw new ot(it.EncodeTimeValueMalformed,`Time must be a number: ${e}`);if(e>jl)throw new ot(it.EncodeTimeSizeExceeded,`Cannot encode a time larger than ${jl}: ${e}`);if(e<0)throw new ot(it.EncodeTimeNegative,`Time must be positive: ${e}`);if(Number.isInteger(e)===!1)throw new ot(it.EncodeTimeValueMalformed,`Time must be an integer: ${e}`);let r,n="";for(let i=t;i>0;i--)r=e%Tr,n=Ql.charAt(r)+n,e=(e-r)/Tr;return n}function Td(){return typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope}function Gl(e,t){let r=t||bd(),n=!e||isNaN(e)?Date.now():e;return Pd(n,Hl)+xd(hd,r)}c();u();p();m();d();l();c();u();p();m();d();l();var ne=[];for(let e=0;e<256;++e)ne.push((e+256).toString(16).slice(1));function Fn(e,t=0){return(ne[e[t+0]]+ne[e[t+1]]+ne[e[t+2]]+ne[e[t+3]]+"-"+ne[e[t+4]]+ne[e[t+5]]+"-"+ne[e[t+6]]+ne[e[t+7]]+"-"+ne[e[t+8]]+ne[e[t+9]]+"-"+ne[e[t+10]]+ne[e[t+11]]+ne[e[t+12]]+ne[e[t+13]]+ne[e[t+14]]+ne[e[t+15]]).toLowerCase()}c();u();p();m();d();l();Ze();var Vn=new Uint8Array(256),$n=Vn.length;function Nt(){return $n>Vn.length-16&&(nn(Vn),$n=0),Vn.slice($n,$n+=16)}c();u();p();m();d();l();c();u();p();m();d();l();Ze();var eo={randomUUID:rn};function vd(e,t,r){if(eo.randomUUID&&!t&&!e)return eo.randomUUID();e=e||{};let n=e.random??e.rng?.()??Nt();if(n.length<16)throw new Error("Random bytes length must be >= 16");if(n[6]=n[6]&15|64,n[8]=n[8]&63|128,t){if(r=r||0,r<0||r+16>t.length)throw new RangeError(`UUID byte range ${r}:${r+15} is out of buffer bounds`);for(let i=0;i<16;++i)t[r+i]=n[i];return t}return Fn(n)}var to=vd;c();u();p();m();d();l();var ro={};function Ad(e,t,r){let n;if(e)n=Jl(e.random??e.rng?.()??Nt(),e.msecs,e.seq,t,r);else{let i=Date.now(),o=Nt();Rd(ro,i,o),n=Jl(o,ro.msecs,ro.seq,t,r)}return t??Fn(n)}function Rd(e,t,r){return e.msecs??=-1/0,e.seq??=0,t>e.msecs?(e.seq=r[6]<<23|r[7]<<16|r[8]<<8|r[9],e.msecs=t):(e.seq=e.seq+1|0,e.seq===0&&e.msecs++),e}function Jl(e,t,r,n,i=0){if(e.length<16)throw new Error("Random bytes length must be >= 16");if(!n)n=new Uint8Array(16),i=0;else if(i<0||i+16>n.length)throw new RangeError(`UUID byte range ${i}:${i+15} is out of buffer bounds`);return t??=Date.now(),r??=e[6]*127<<24|e[7]<<16|e[8]<<8|e[9],n[i++]=t/1099511627776&255,n[i++]=t/4294967296&255,n[i++]=t/16777216&255,n[i++]=t/65536&255,n[i++]=t/256&255,n[i++]=t&255,n[i++]=112|r>>>28&15,n[i++]=r>>>20&255,n[i++]=128|r>>>14&63,n[i++]=r>>>6&255,n[i++]=r<<2&255|e[10]&3,n[i++]=e[11],n[i++]=e[12],n[i++]=e[13],n[i++]=e[14],n[i++]=e[15],n}var no=Ad;var qn=class{#t={};constructor(){this.register("uuid",new oo),this.register("cuid",new so),this.register("ulid",new ao),this.register("nanoid",new lo),this.register("product",new co)}snapshot(){return Object.create(this.#t,{now:{value:new io}})}register(t,r){this.#t[t]=r}},io=class{#t=new Date;generate(){return this.#t.toISOString()}},oo=class{generate(t){if(t===4)return to();if(t===7)return no();throw new Error("Invalid UUID generator arguments")}},so=class{generate(t){if(t===1)return Ya();if(t===2)return(0,Wl.createId)();throw new Error("Invalid CUID generator arguments")}},ao=class{generate(){return Gl()}},lo=class{generate(t){if(typeof t=="number")return Xi(t);if(t===void 0)return Xi();throw new Error("Invalid Nanoid generator arguments")}},co=class{generate(t,r){if(t===void 0||r===void 0)throw new Error("Invalid Product generator arguments");return Array.isArray(t)&&Array.isArray(r)?t.flatMap(n=>r.map(i=>[n,i])):Array.isArray(t)?t.map(n=>[n,r]):Array.isArray(r)?r.map(n=>[t,n]):[[t,r]]}};c();u();p();m();d();l();function Bn(e,t){return e==null?e:typeof e=="string"?Bn(JSON.parse(e),t):Array.isArray(e)?Sd(e,t):Cd(e,t)}function Cd(e,t){if(t.pagination){let{skip:r,take:n,cursor:i}=t.pagination;if(r!==null&&r>0||n===0||i!==null&&!kt(e,i))return null}return zl(e,t.nested)}function zl(e,t){for(let[r,n]of Object.entries(t))e[r]=Bn(e[r],n);return e}function Sd(e,t){if(t.distinct!==null){let r=t.linkingFields!==null?[...t.distinct,...t.linkingFields]:t.distinct;e=Id(e,r)}return t.pagination&&(e=kd(e,t.pagination,t.linkingFields)),t.reverse&&e.reverse(),Object.keys(t.nested).length===0?e:e.map(r=>zl(r,t.nested))}function Id(e,t){let r=new Set,n=[];for(let i of e){let o=vr(i,t);r.has(o)||(r.add(o),n.push(i))}return n}function kd(e,t,r){if(r===null)return Kl(e,t);let n=new Map;for(let o of e){let s=vr(o,r);n.has(s)||n.set(s,[]),n.get(s).push(o)}let i=Array.from(n.entries());return i.sort(([o],[s])=>o<s?-1:o>s?1:0),i.flatMap(([,o])=>Kl(o,t))}function Kl(e,{cursor:t,skip:r,take:n}){let i=t!==null?e.findIndex(a=>kt(a,t)):0;if(i===-1)return[];let o=i+(r??0),s=n!==null?o+n:e.length;return e.slice(o,s)}function vr(e,t){return JSON.stringify(t.map(r=>e[r]))}c();u();p();m();d();l();c();u();p();m();d();l();function uo(e){return typeof e=="object"&&e!==null&&e.prisma__type==="param"}function po(e){return typeof e=="object"&&e!==null&&e.prisma__type==="generatorCall"}function go(e,t,r,n){let i=e.args.map(o=>Te(o,t,r));switch(e.type){case"rawSql":return[_d(e.sql,i,e.argTypes)];case"templateSql":return(e.chunkable?Nd(e.fragments,i,n):[i]).map(s=>{if(n!==void 0&&s.length>n)throw new ae("The query parameter limit supported by your database is exceeded.","P2029");return Od(e.fragments,e.placeholderFormat,s,e.argTypes)});default:F(e.type,"Invalid query type")}}function Te(e,t,r){for(;Md(e);)if(uo(e)){let n=t[e.prisma__value.name];if(n===void 0)throw new Error(`Missing value for query variable ${e.prisma__value.name}`);e=n}else if(po(e)){let{name:n,args:i}=e.prisma__value,o=r[n];if(!o)throw new Error(`Encountered an unknown generator '${n}'`);e=o.generate(...i.map(s=>Te(s,t,r)))}else F(e,`Unexpected unevaluated value type: ${e}`);return Array.isArray(e)&&(e=e.map(n=>Te(n,t,r))),e}function Od(e,t,r,n){let i="",o={placeholderNumber:1},s=[],a=[];for(let f of fo(e,r,n)){if(i+=Dd(f,t,o),f.type==="stringChunk")continue;let E=s.length,A=s.push(...Yl(f))-E;if(f.argType.arity==="tuple"){if(A%f.argType.elements.length!==0)throw new Error(`Malformed query template. Expected the number of parameters to match the tuple arity, but got ${A} parameters for a tuple of arity ${f.argType.elements.length}.`);for(let R=0;R<A/f.argType.elements.length;R++)a.push(...f.argType.elements)}else for(let R=0;R<A;R++)a.push(f.argType)}return{sql:i,args:s,argTypes:a}}function Dd(e,t,r){let n=e.type;switch(n){case"parameter":return mo(t,r.placeholderNumber++);case"stringChunk":return e.chunk;case"parameterTuple":return`(${e.value.length==0?"NULL":e.value.map(()=>mo(t,r.placeholderNumber++)).join(",")})`;case"parameterTupleList":return e.value.map(i=>{let o=i.map(()=>mo(t,r.placeholderNumber++)).join(e.itemSeparator);return`${e.itemPrefix}${o}${e.itemSuffix}`}).join(e.groupSeparator);default:F(n,"Invalid fragment type")}}function mo(e,t){return e.hasNumbering?`${e.prefix}${t}`:e.prefix}function _d(e,t,r){return{sql:e,args:t,argTypes:r}}function Md(e){return uo(e)||po(e)}function*fo(e,t,r){let n=0;for(let i of e)switch(i.type){case"parameter":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);yield{...i,value:t[n],argType:r?.[n]},n++;break}case"stringChunk":{yield i;break}case"parameterTuple":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let o=t[n];yield{...i,value:Array.isArray(o)?o:[o],argType:r?.[n]},n++;break}case"parameterTupleList":{if(n>=t.length)throw new Error(`Malformed query template. Fragments attempt to read over ${t.length} parameters.`);let o=t[n];if(!Array.isArray(o))throw new Error("Malformed query template. Tuple list expected.");if(o.length===0)throw new Error("Malformed query template. Tuple list cannot be empty.");for(let s of o)if(!Array.isArray(s))throw new Error("Malformed query template. Tuple expected.");yield{...i,value:o,argType:r?.[n]},n++;break}}}function*Yl(e){switch(e.type){case"parameter":yield e.value;break;case"stringChunk":break;case"parameterTuple":yield*e.value;break;case"parameterTupleList":for(let t of e.value)yield*t;break}}function Nd(e,t,r){let n=0,i=0;for(let s of fo(e,t,void 0)){let a=0;for(let f of Yl(s))a++;i=Math.max(i,a),n+=a}let o=[[]];for(let s of fo(e,t,void 0))switch(s.type){case"parameter":{for(let a of o)a.push(s.value);break}case"stringChunk":break;case"parameterTuple":{let a=s.value.length,f=[];if(r&&o.length===1&&a===i&&n>r&&n-a<r){let E=r-(n-a);f=Ld(s.value,E)}else f=[s.value];o=o.flatMap(E=>f.map(A=>[...E,A]));break}case"parameterTupleList":{let a=s.value.reduce((R,S)=>R+S.length,0),f=[],E=[],A=0;for(let R of s.value)r&&o.length===1&&a===i&&E.length>0&&n-a+A+R.length>r&&(f.push(E),E=[],A=0),E.push(R),A+=R.length;E.length>0&&f.push(E),o=o.flatMap(R=>f.map(S=>[...R,S]));break}}return o}function Ld(e,t){let r=[];for(let n=0;n<e.length;n+=t)r.push(e.slice(n,n+t));return r}c();u();p();m();d();l();function Zl(e){return e.rows.map(t=>t.reduce((r,n,i)=>(r[e.columnNames[i]]=n,r),{}))}function Xl(e){return{columns:e.columnNames,types:e.columnTypes.map(t=>Ud(t)),rows:e.rows.map(t=>t.map((r,n)=>Lt(r,e.columnTypes[n])))}}function Lt(e,t){if(e===null)return null;switch(t){case O.Int32:switch(typeof e){case"number":return Math.trunc(e);case"string":return Math.trunc(Number(e));default:throw new Error(`Cannot serialize value of type ${typeof e} as Int32`)}case O.Int32Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int32Array`);return e.map(r=>Lt(r,O.Int32));case O.Int64:switch(typeof e){case"number":return BigInt(Math.trunc(e));case"string":return e;default:throw new Error(`Cannot serialize value of type ${typeof e} as Int64`)}case O.Int64Array:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as Int64Array`);return e.map(r=>Lt(r,O.Int64));case O.Json:switch(typeof e){case"string":return JSON.parse(e);default:throw new Error(`Cannot serialize value of type ${typeof e} as Json`)}case O.JsonArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as JsonArray`);return e.map(r=>Lt(r,O.Json));case O.Bytes:if(Array.isArray(e))return new Uint8Array(e);throw new Error(`Cannot serialize value of type ${typeof e} as Bytes`);case O.BytesArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BytesArray`);return e.map(r=>Lt(r,O.Bytes));case O.Boolean:switch(typeof e){case"boolean":return e;case"string":return e==="true"||e==="1";case"number":return e===1;default:throw new Error(`Cannot serialize value of type ${typeof e} as Boolean`)}case O.BooleanArray:if(!Array.isArray(e))throw new Error(`Cannot serialize value of type ${typeof e} as BooleanArray`);return e.map(r=>Lt(r,O.Boolean));default:return e}}function Ud(e){switch(e){case O.Int32:return"int";case O.Int64:return"bigint";case O.Float:return"float";case O.Double:return"double";case O.Text:return"string";case O.Enum:return"enum";case O.Bytes:return"bytes";case O.Boolean:return"bool";case O.Character:return"char";case O.Numeric:return"decimal";case O.Json:return"json";case O.Uuid:return"uuid";case O.DateTime:return"datetime";case O.Date:return"date";case O.Time:return"time";case O.Int32Array:return"int-array";case O.Int64Array:return"bigint-array";case O.FloatArray:return"float-array";case O.DoubleArray:return"double-array";case O.TextArray:return"string-array";case O.EnumArray:return"string-array";case O.BytesArray:return"bytes-array";case O.BooleanArray:return"bool-array";case O.CharacterArray:return"char-array";case O.NumericArray:return"decimal-array";case O.JsonArray:return"json-array";case O.UuidArray:return"uuid-array";case O.DateTimeArray:return"datetime-array";case O.DateArray:return"date-array";case O.TimeArray:return"time-array";case O.UnknownNumber:return"unknown";case O.Set:return"string";default:F(e,`Unexpected column type: ${e}`)}}c();u();p();m();d();l();function ec(e,t,r){if(!t.every(n=>yo(e,n))){let n=Fd(e,r),i=$d(r);throw new ae(n,i,r.context)}}function yo(e,t){switch(t.type){case"rowCountEq":return Array.isArray(e)?e.length===t.args:e===null?t.args===0:t.args===1;case"rowCountNeq":return Array.isArray(e)?e.length!==t.args:e===null?t.args!==0:t.args!==1;case"affectedRowCountEq":return e===t.args;case"never":return!1;default:F(t,`Unknown rule type: ${t.type}`)}}function Fd(e,t){switch(t.error_identifier){case"RELATION_VIOLATION":return`The change you are trying to make would violate the required relation '${t.context.relation}' between the \`${t.context.modelA}\` and \`${t.context.modelB}\` models.`;case"MISSING_RECORD":return`An operation failed because it depends on one or more records that were required but not found. No record was found for ${t.context.operation}.`;case"MISSING_RELATED_RECORD":{let r=t.context.neededFor?` (needed to ${t.context.neededFor})`:"";return`An operation failed because it depends on one or more records that were required but not found. No '${t.context.model}' record${r} was found for ${t.context.operation} on ${t.context.relationType} relation '${t.context.relation}'.`}case"INCOMPLETE_CONNECT_INPUT":return`An operation failed because it depends on one or more records that were required but not found. Expected ${t.context.expectedRows} records to be connected, found only ${Array.isArray(e)?e.length:e}.`;case"INCOMPLETE_CONNECT_OUTPUT":return`The required connected records were not found. Expected ${t.context.expectedRows} records to be connected after connect operation on ${t.context.relationType} relation '${t.context.relation}', found ${Array.isArray(e)?e.length:e}.`;case"RECORDS_NOT_CONNECTED":return`The records for relation \`${t.context.relation}\` between the \`${t.context.parent}\` and \`${t.context.child}\` models are not connected.`;default:F(t,`Unknown error identifier: ${t}`)}}function $d(e){switch(e.error_identifier){case"RELATION_VIOLATION":return"P2014";case"RECORDS_NOT_CONNECTED":return"P2017";case"INCOMPLETE_CONNECT_OUTPUT":return"P2018";case"MISSING_RECORD":case"MISSING_RELATED_RECORD":case"INCOMPLETE_CONNECT_INPUT":return"P2025";default:F(e,`Unknown error identifier: ${e}`)}}var Ar=class e{#t;#e;#r;#n=new qn;#s;#i;#a;#o;#c;constructor({transactionManager:t,placeholderValues:r,onQuery:n,tracingHelper:i,serializer:o,rawSerializer:s,provider:a,connectionInfo:f}){this.#t=t,this.#e=r,this.#r=n,this.#s=i,this.#i=o,this.#a=s??o,this.#o=a,this.#c=f}static forSql(t){return new e({transactionManager:t.transactionManager,placeholderValues:t.placeholderValues,onQuery:t.onQuery,tracingHelper:t.tracingHelper,serializer:Zl,rawSerializer:Xl,provider:t.provider,connectionInfo:t.connectionInfo})}async run(t,r){let{value:n}=await this.interpretNode(t,r,this.#e,this.#n.snapshot()).catch(i=>Ot(i));return n}async interpretNode(t,r,n,i){switch(t.type){case"value":return{value:Te(t.args,n,i)};case"seq":{let o;for(let s of t.args)o=await this.interpretNode(s,r,n,i);return o??{value:void 0}}case"get":return{value:n[t.args.name]};case"let":{let o=Object.create(n);for(let s of t.args.bindings){let{value:a}=await this.interpretNode(s.expr,r,o,i);o[s.name]=a}return this.interpretNode(t.args.expr,r,o,i)}case"getFirstNonEmpty":{for(let o of t.args.names){let s=n[o];if(!tc(s))return{value:s}}return{value:[]}}case"concat":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>s.concat(ho(a)),[]):[]}}case"sum":{let o=await Promise.all(t.args.map(s=>this.interpretNode(s,r,n,i).then(a=>a.value)));return{value:o.length>0?o.reduce((s,a)=>Oe(s)+Oe(a)):0}}case"execute":{let o=go(t.args,n,i,this.#l()),s=0;for(let a of o)s+=await this.#u(a,r,()=>r.executeRaw(a).catch(f=>t.args.type==="rawSql"?Vi(f):Ot(f)));return{value:s}}case"query":{let o=go(t.args,n,i,this.#l()),s;for(let a of o){let f=await this.#u(a,r,()=>r.queryRaw(a).catch(E=>t.args.type==="rawSql"?Vi(E):Ot(E)));s===void 0?s=f:(s.rows.push(...f.rows),s.lastInsertId=f.lastInsertId)}return{value:t.args.type==="rawSql"?this.#a(s):this.#i(s),lastInsertId:s?.lastInsertId}}case"reverse":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);return{value:Array.isArray(o)?o.reverse():o,lastInsertId:s}}case"unique":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(!Array.isArray(o))return{value:o,lastInsertId:s};if(o.length>1)throw new Error(`Expected zero or one element, got ${o.length}`);return{value:o[0]??null,lastInsertId:s}}case"required":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args,r,n,i);if(tc(o))throw new Error("Required value is empty");return{value:o,lastInsertId:s}}case"mapField":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.records,r,n,i);return{value:rc(o,t.args.field),lastInsertId:s}}case"join":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.parent,r,n,i);if(o===null)return{value:null,lastInsertId:s};let a=await Promise.all(t.args.children.map(async f=>({joinExpr:f,childRecords:(await this.interpretNode(f.child,r,n,i)).value})));return{value:Vd(o,a),lastInsertId:s}}case"transaction":{if(!this.#t.enabled)return this.interpretNode(t.args,r,n,i);let o=this.#t.manager,s=await o.startTransaction(),a=await o.getTransaction(s,"query");try{let f=await this.interpretNode(t.args,a,n,i);return await o.commitTransaction(s.id),f}catch(f){throw await o.rollbackTransaction(s.id),f}}case"dataMap":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Ja(o,t.args.structure,t.args.enums),lastInsertId:s}}case"validate":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return ec(o,t.args.rules,t.args),{value:o,lastInsertId:s}}case"if":{let{value:o}=await this.interpretNode(t.args.value,r,n,i);return yo(o,t.args.rule)?await this.interpretNode(t.args.then,r,n,i):await this.interpretNode(t.args.else,r,n,i)}case"unit":return{value:void 0};case"diff":{let{value:o}=await this.interpretNode(t.args.from,r,n,i),{value:s}=await this.interpretNode(t.args.to,r,n,i),a=new Set(ho(s).map(f=>JSON.stringify(f)));return{value:ho(o).filter(f=>!a.has(JSON.stringify(f)))}}case"process":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i);return{value:Bn(o,t.args.operations),lastInsertId:s}}case"initializeRecord":{let{lastInsertId:o}=await this.interpretNode(t.args.expr,r,n,i),s={};for(let[a,f]of Object.entries(t.args.fields))s[a]=qd(f,o,n,i);return{value:s,lastInsertId:o}}case"mapRecord":{let{value:o,lastInsertId:s}=await this.interpretNode(t.args.expr,r,n,i),a=o===null?{}:wo(o);for(let[f,E]of Object.entries(t.args.fields))a[f]=Bd(E,a[f],n,i);return{value:a,lastInsertId:s}}default:F(t,`Unexpected node type: ${t.type}`)}}#l(){return this.#c?.maxBindValues!==void 0?this.#c.maxBindValues:this.#p()}#p(){if(this.#o!==void 0)switch(this.#o){case"cockroachdb":case"postgres":case"postgresql":case"prisma+postgres":return 32766;case"mysql":return 65535;case"sqlite":return 999;case"sqlserver":return 2098;case"mongodb":return;default:F(this.#o,`Unexpected provider: ${this.#o}`)}}#u(t,r,n){return _n({query:t,execute:n,provider:this.#o??r.provider,tracingHelper:this.#s,onQuery:this.#r})}};function tc(e){return Array.isArray(e)?e.length===0:e==null}function ho(e){return Array.isArray(e)?e:[e]}function Oe(e){if(typeof e=="number")return e;if(typeof e=="string")return Number(e);throw new Error(`Expected number, got ${typeof e}`)}function wo(e){if(typeof e=="object"&&e!==null)return e;throw new Error(`Expected object, got ${typeof e}`)}function rc(e,t){return Array.isArray(e)?e.map(r=>rc(r,t)):typeof e=="object"&&e!==null?e[t]??null:e}function Vd(e,t){for(let{joinExpr:r,childRecords:n}of t){let i=r.on.map(([a])=>a),o=r.on.map(([,a])=>a),s={};for(let a of Array.isArray(e)?e:[e]){let f=wo(a),E=vr(f,i);s[E]||(s[E]=[]),s[E].push(f),r.isRelationUnique?f[r.parentField]=null:f[r.parentField]=[]}for(let a of Array.isArray(n)?n:[n]){if(a===null)continue;let f=vr(wo(a),o);for(let E of s[f]??[])r.isRelationUnique?E[r.parentField]=a:E[r.parentField].push(a)}}return e}function qd(e,t,r,n){switch(e.type){case"value":return Te(e.value,r,n);case"lastInsertId":return t;default:F(e,`Unexpected field initializer type: ${e.type}`)}}function Bd(e,t,r,n){switch(e.type){case"set":return Te(e.value,r,n);case"add":return Oe(t)+Oe(Te(e.value,r,n));case"subtract":return Oe(t)-Oe(Te(e.value,r,n));case"multiply":return Oe(t)*Oe(Te(e.value,r,n));case"divide":{let i=Oe(t),o=Oe(Te(e.value,r,n));return o===0?null:i/o}default:F(e,`Unexpected field operation type: ${e.type}`)}}c();u();p();m();d();l();c();u();p();m();d();l();async function jd(){return globalThis.crypto??await Promise.resolve().then(()=>(Ze(),di))}async function nc(){return(await jd()).randomUUID()}c();u();p();m();d();l();var Ee=class extends ae{name="TransactionManagerError";constructor(t,r){super("Transaction API error: "+t,"P2028",r)}},Rr=class extends Ee{constructor(){super("Transaction not found. Transaction ID is invalid, refers to an old closed transaction Prisma doesn't have information about anymore, or was obtained before disconnecting.")}},jn=class extends Ee{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a committed transaction.`)}},Qn=class extends Ee{constructor(t){super(`Transaction already closed: A ${t} cannot be executed on a transaction that was rolled back.`)}},Hn=class extends Ee{constructor(){super("Unable to start a transaction in the given time.")}},Gn=class extends Ee{constructor(t,{timeout:r,timeTaken:n}){super(`A ${t} cannot be executed on an expired transaction. The timeout for this transaction was ${r} ms, however ${n} ms passed since the start of the transaction. Consider increasing the interactive transaction timeout or doing less work in the transaction.`,{operation:t,timeout:r,timeTaken:n})}},Ut=class extends Ee{constructor(t){super(`Internal Consistency Error: ${t}`)}},Jn=class extends Ee{constructor(t){super(`Invalid isolation level: ${t}`,{isolationLevel:t})}};var Qd=100,Cr=K("prisma:client:transactionManager"),Hd=()=>({sql:"COMMIT",args:[],argTypes:[]}),Gd=()=>({sql:"ROLLBACK",args:[],argTypes:[]}),Jd=()=>({sql:'-- Implicit "COMMIT" query via underlying driver',args:[],argTypes:[]}),Wd=()=>({sql:'-- Implicit "ROLLBACK" query via underlying driver',args:[],argTypes:[]}),Sr=class{transactions=new Map;closedTransactions=[];driverAdapter;transactionOptions;tracingHelper;#t;#e;constructor({driverAdapter:t,transactionOptions:r,tracingHelper:n,onQuery:i,provider:o}){this.driverAdapter=t,this.transactionOptions=r,this.tracingHelper=n,this.#t=i,this.#e=o}async startTransaction(t){return await this.tracingHelper.runInChildSpan("start_transaction",()=>this.#r(t))}async#r(t){let r=t!==void 0?this.#a(t):this.transactionOptions,n={id:await nc(),status:"waiting",timer:void 0,timeout:r.timeout,startedAt:Date.now(),transaction:void 0};this.transactions.set(n.id,n);let i=!1,o=setTimeout(()=>i=!0,r.maxWait);switch(o.unref?.(),n.transaction=await this.driverAdapter.startTransaction(r.isolationLevel).catch(Ot),clearTimeout(o),n.status){case"waiting":if(i)throw await this.#i(n,"timed_out"),new Hn;return n.status="running",n.timer=this.#s(n.id,r.timeout),{id:n.id};case"timed_out":case"running":case"committed":case"rolled_back":throw new Ut(`Transaction in invalid state ${n.status} although it just finished startup.`);default:F(n.status,"Unknown transaction status.")}}async commitTransaction(t){return await this.tracingHelper.runInChildSpan("commit_transaction",async()=>{let r=this.#n(t,"commit");await this.#i(r,"committed")})}async rollbackTransaction(t){return await this.tracingHelper.runInChildSpan("rollback_transaction",async()=>{let r=this.#n(t,"rollback");await this.#i(r,"rolled_back")})}async getTransaction(t,r){let n=this.#n(t.id,r);if(n.status==="closing"&&(await n.closing,n=this.#n(t.id,r)),!n.transaction)throw new Rr;return n.transaction}#n(t,r){let n=this.transactions.get(t);if(!n){let i=this.closedTransactions.find(o=>o.id===t);if(i)switch(Cr("Transaction already closed.",{transactionId:t,status:i.status}),i.status){case"closing":case"waiting":case"running":throw new Ut("Active transaction found in closed transactions list.");case"committed":throw new jn(r);case"rolled_back":throw new Qn(r);case"timed_out":throw new Gn(r,{timeout:i.timeout,timeTaken:Date.now()-i.startedAt})}else throw Cr("Transaction not found.",t),new Rr}if(["committed","rolled_back","timed_out"].includes(n.status))throw new Ut("Closed transaction found in active transactions map.");return n}async cancelAllTransactions(){await Promise.allSettled([...this.transactions.values()].map(t=>this.#i(t,"rolled_back")))}#s(t,r){let n=Date.now(),i=setTimeout(async()=>{Cr("Transaction timed out.",{transactionId:t,timeoutStartedAt:n,timeout:r});let o=this.transactions.get(t);o&&["running","waiting"].includes(o.status)?await this.#i(o,"timed_out"):Cr("Transaction already committed or rolled back when timeout happened.",t)},r);return i.unref?.(),i}async#i(t,r){let n=async()=>{Cr("Closing transaction.",{transactionId:t.id,status:r});try{if(t.transaction&&r==="committed")if(t.transaction.options.usePhantomQuery)await this.#o(Jd(),t.transaction,()=>t.transaction.commit());else{let i=Hd();await this.#o(i,t.transaction,()=>t.transaction.executeRaw(i)),await t.transaction.commit()}else if(t.transaction)if(t.transaction.options.usePhantomQuery)await this.#o(Wd(),t.transaction,()=>t.transaction.rollback());else{let i=Gd();await this.#o(i,t.transaction,()=>t.transaction.executeRaw(i)),await t.transaction.rollback()}}finally{t.status=r,clearTimeout(t.timer),t.timer=void 0,this.transactions.delete(t.id),this.closedTransactions.push(t),this.closedTransactions.length>Qd&&this.closedTransactions.shift()}};t.status==="closing"?(await t.closing,this.#n(t.id,r==="committed"?"commit":"rollback")):await Object.assign(t,{status:"closing",reason:r,closing:n()}).closing}#a(t){if(!t.timeout)throw new Ee("timeout is required");if(!t.maxWait)throw new Ee("maxWait is required");if(t.isolationLevel==="SNAPSHOT")throw new Jn(t.isolationLevel);return{...t,timeout:t.timeout,maxWait:t.maxWait}}#o(t,r,n){return _n({query:t,execute:n,provider:this.#e??r.provider,tracingHelper:this.tracingHelper,onQuery:this.#t})}};var Wn="6.16.2";c();u();p();m();d();l();var Kn=class e{#t;#e;#r;#n;constructor(t,r,n){this.#t=t,this.#e=r,this.#r=n,this.#n=r.getConnectionInfo?.()}static async connect(t){let r,n;try{r=await t.driverAdapterFactory.connect(),n=new Sr({driverAdapter:r,transactionOptions:t.transactionOptions,tracingHelper:t.tracingHelper,onQuery:t.onQuery,provider:t.provider})}catch(i){throw await r?.dispose(),i}return new e(t,r,n)}getConnectionInfo(){let t=this.#n??{supportsRelationJoins:!1};return Promise.resolve({provider:this.#e.provider,connectionInfo:t})}async execute({plan:t,placeholderValues:r,transaction:n,batchIndex:i}){let o=n?await this.#r.getTransaction(n,i!==void 0?"batch query":"query"):this.#e;return await Ar.forSql({transactionManager:n?{enabled:!1}:{enabled:!0,manager:this.#r},placeholderValues:r,onQuery:this.#t.onQuery,tracingHelper:this.#t.tracingHelper,provider:this.#t.provider,connectionInfo:this.#n}).run(t,o)}async startTransaction(t){return{...await this.#r.startTransaction(t),payload:void 0}}async commitTransaction(t){await this.#r.commitTransaction(t.id)}async rollbackTransaction(t){await this.#r.rollbackTransaction(t.id)}async disconnect(){try{await this.#r.cancelAllTransactions()}finally{await this.#e.dispose()}}apiKey(){return null}};c();u();p();m();d();l();c();u();p();m();d();l();var zn=/^[\u0009\u0020-\u007E\u0080-\u00FF]+$/;function ic(e,t,r){let n=r||{},i=n.encode||encodeURIComponent;if(typeof i!="function")throw new TypeError("option encode is invalid");if(!zn.test(e))throw new TypeError("argument name is invalid");let o=i(t);if(o&&!zn.test(o))throw new TypeError("argument val is invalid");let s=e+"="+o;if(n.maxAge!==void 0&&n.maxAge!==null){let a=n.maxAge-0;if(Number.isNaN(a)||!Number.isFinite(a))throw new TypeError("option maxAge is invalid");s+="; Max-Age="+Math.floor(a)}if(n.domain){if(!zn.test(n.domain))throw new TypeError("option domain is invalid");s+="; Domain="+n.domain}if(n.path){if(!zn.test(n.path))throw new TypeError("option path is invalid");s+="; Path="+n.path}if(n.expires){if(!zd(n.expires)||Number.isNaN(n.expires.valueOf()))throw new TypeError("option expires is invalid");s+="; Expires="+n.expires.toUTCString()}if(n.httpOnly&&(s+="; HttpOnly"),n.secure&&(s+="; Secure"),n.priority)switch(typeof n.priority=="string"?n.priority.toLowerCase():n.priority){case"low":{s+="; Priority=Low";break}case"medium":{s+="; Priority=Medium";break}case"high":{s+="; Priority=High";break}default:throw new TypeError("option priority is invalid")}if(n.sameSite)switch(typeof n.sameSite=="string"?n.sameSite.toLowerCase():n.sameSite){case!0:{s+="; SameSite=Strict";break}case"lax":{s+="; SameSite=Lax";break}case"strict":{s+="; SameSite=Strict";break}case"none":{s+="; SameSite=None";break}default:throw new TypeError("option sameSite is invalid")}return n.partitioned&&(s+="; Partitioned"),s}function zd(e){return Object.prototype.toString.call(e)==="[object Date]"||e instanceof Date}function oc(e,t){let r=(e||"").split(";").filter(f=>typeof f=="string"&&!!f.trim()),n=r.shift()||"",i=Yd(n),o=i.name,s=i.value;try{s=t?.decode===!1?s:(t?.decode||decodeURIComponent)(s)}catch{}let a={name:o,value:s};for(let f of r){let E=f.split("="),A=(E.shift()||"").trimStart().toLowerCase(),R=E.join("=");switch(A){case"expires":{a.expires=new Date(R);break}case"max-age":{a.maxAge=Number.parseInt(R,10);break}case"secure":{a.secure=!0;break}case"httponly":{a.httpOnly=!0;break}case"samesite":{a.sameSite=R;break}default:a[A]=R}}return a}function Yd(e){let t="",r="",n=e.split("=");return n.length>1?(t=n.shift(),r=n.join("=")):r=e,{name:t,value:r}}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Zd=()=>globalThis.process?.release?.name==="node",Xd=()=>!!globalThis.Bun||!!globalThis.process?.versions?.bun,ef=()=>!!globalThis.Deno,tf=()=>typeof globalThis.Netlify=="object",rf=()=>typeof globalThis.EdgeRuntime=="object",nf=()=>globalThis.navigator?.userAgent==="Cloudflare-Workers";function of(){return[[tf,"netlify"],[rf,"edge-light"],[nf,"workerd"],[ef,"deno"],[Xd,"bun"],[Zd,"node"]].flatMap(r=>r[0]()?[r[1]]:[]).at(0)??""}var sf={node:"Node.js",workerd:"Cloudflare Workers",deno:"Deno and Deno Deploy",netlify:"Netlify Edge Functions","edge-light":"Edge Runtime (Vercel Edge Functions, Vercel Edge Middleware, Next.js (Pages Router) Edge API Routes, Next.js (App Router) Edge Route Handlers or Next.js Middleware)"};function bo(){let e=of();return{id:e,prettyName:sf[e]||e,isEdge:["workerd","deno","netlify","edge-light"].includes(e)}}function Ft({inlineDatasources:e,overrideDatasources:t,env:r,clientVersion:n}){let i,o=Object.keys(e)[0],s=e[o]?.url,a=t[o]?.url;if(o===void 0?i=void 0:a?i=a:s?.value?i=s.value:s?.fromEnvVar&&(i=r[s.fromEnvVar]),s?.fromEnvVar!==void 0&&i===void 0)throw bo().id==="workerd"?new $(`error: Environment variable not found: ${s.fromEnvVar}.

In Cloudflare module Workers, environment variables are available only in the Worker's \`env\` parameter of \`fetch\`.
To solve this, provide the connection string directly: https://pris.ly/d/cloudflare-datasource-url`,n):new $(`error: Environment variable not found: ${s.fromEnvVar}.`,n);if(i===void 0)throw new $("error: Missing URL environment variable, value, or override.",n);return i}c();u();p();m();d();l();c();u();p();m();d();l();c();u();p();m();d();l();var Yn=class extends Error{clientVersion;cause;constructor(t,r){super(t),this.clientVersion=r.clientVersion,this.cause=r.cause}get[Symbol.toStringTag](){return this.name}};var fe=class extends Yn{isRetryable;constructor(t,r){super(t,r),this.isRetryable=r.isRetryable??!0}};c();u();p();m();d();l();function N(e,t){return{...e,isRetryable:t}}var st=class extends fe{name="InvalidDatasourceError";code="P6001";constructor(t,r){super(t,N(r,!1))}};D(st,"InvalidDatasourceError");function Zn(e){let t={clientVersion:e.clientVersion},r=Object.keys(e.inlineDatasources)[0],n=Ft({inlineDatasources:e.inlineDatasources,overrideDatasources:e.overrideDatasources,clientVersion:e.clientVersion,env:{...e.env,...typeof g<"u"?g.env:{}}}),i;try{i=new URL(n)}catch{throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\``,t)}let{protocol:o,searchParams:s}=i;if(o!=="prisma:"&&o!==sn)throw new st(`Error validating datasource \`${r}\`: the URL must start with the protocol \`prisma://\` or \`prisma+postgres://\``,t);let a=s.get("api_key");if(a===null||a.length<1)throw new st(`Error validating datasource \`${r}\`: the URL must contain a valid API key`,t);let f=gi(i)?"http:":"https:";g.env.TEST_CLIENT_ENGINE_REMOTE_EXECUTOR&&i.searchParams.has("use_http")&&(f="http:");let E=new URL(i.href.replace(o,f));return{apiKey:a,url:E}}c();u();p();m();d();l();var sc=$e(Es()),$t=class{apiKey;tracingHelper;logLevel;logQueries;engineHash;constructor({apiKey:t,tracingHelper:r,logLevel:n,logQueries:i,engineHash:o}){this.apiKey=t,this.tracingHelper=r,this.logLevel=n,this.logQueries=i,this.engineHash=o}build({traceparent:t,transactionId:r}={}){let n={Accept:"application/json",Authorization:`Bearer ${this.apiKey}`,"Content-Type":"application/json","Prisma-Engine-Hash":this.engineHash,"Prisma-Engine-Version":sc.enginesVersion};this.tracingHelper.isEnabled()&&(n.traceparent=t??this.tracingHelper.getTraceParent()),r&&(n["X-Transaction-Id"]=r);let i=this.#t();return i.length>0&&(n["X-Capture-Telemetry"]=i.join(", ")),n}#t(){let t=[];return this.tracingHelper.isEnabled()&&t.push("tracing"),this.logLevel&&t.push(this.logLevel),this.logQueries&&t.push("query"),t}};c();u();p();m();d();l();function af(e){return e[0]*1e3+e[1]/1e6}function Vt(e){return new Date(af(e))}var ac=K("prisma:client:clientEngine:remoteExecutor"),Xn=class{#t;#e;#r;#n;#s;constructor(t){this.#t=t.clientVersion,this.#n=t.logEmitter,this.#s=t.tracingHelper;let{url:r,apiKey:n}=Zn({clientVersion:t.clientVersion,env:t.env,inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources});this.#r=new Eo(r),this.#e=new $t({apiKey:n,engineHash:t.clientVersion,logLevel:t.logLevel,logQueries:t.logQueries,tracingHelper:t.tracingHelper})}async getConnectionInfo(){return await this.#i({path:"/connection-info",method:"GET"})}async execute({plan:t,placeholderValues:r,batchIndex:n,model:i,operation:o,transaction:s,customFetch:a}){return(await this.#i({path:s?`/transaction/${s.id}/query`:"/query",method:"POST",body:{model:i,operation:o,plan:t,params:r},batchRequestIdx:n,fetch:a})).data}async startTransaction(t){return{...await this.#i({path:"/transaction/start",method:"POST",body:t}),payload:void 0}}async commitTransaction(t){await this.#i({path:`/transaction/${t.id}/commit`,method:"POST"})}async rollbackTransaction(t){await this.#i({path:`/transaction/${t.id}/rollback`,method:"POST"})}disconnect(){return Promise.resolve()}apiKey(){return this.#e.apiKey}async#i({path:t,method:r,body:n,fetch:i=globalThis.fetch,batchRequestIdx:o}){let s=await this.#r.request({method:r,path:t,headers:this.#e.build(),body:n,fetch:i});s.ok||await this.#a(s,o);let a=await s.json();return typeof a.extensions=="object"&&a.extensions!==null&&this.#o(a.extensions),a}async#a(t,r){let n=t.headers.get("Prisma-Error-Code"),i=await t.text(),o,s=i;try{o=JSON.parse(i)}catch{o={}}typeof o.code=="string"&&(n=o.code),typeof o.error=="string"?s=o.error:typeof o.message=="string"?s=o.message:typeof o.InvalidRequestError=="object"&&o.InvalidRequestError!==null&&typeof o.InvalidRequestError.reason=="string"&&(s=o.InvalidRequestError.reason),s=s||`HTTP ${t.status}: ${t.statusText}`;let a=typeof o.meta=="object"&&o.meta!==null?o.meta:o;throw new ee(s,{clientVersion:this.#t,code:n??"P6000",batchRequestIdx:r,meta:a})}#o(t){if(t.logs)for(let r of t.logs)this.#c(r);t.traces&&this.#s.dispatchEngineSpans(t.traces)}#c(t){switch(t.level){case"debug":case"trace":ac(t);break;case"error":case"warn":case"info":{this.#n.emit(t.level,{timestamp:Vt(t.timestamp),message:t.attributes.message??"",target:t.target??"RemoteExecutor"});break}case"query":{this.#n.emit("query",{query:t.attributes.query??"",timestamp:Vt(t.timestamp),duration:t.attributes.duration_ms??0,params:t.attributes.params??"",target:t.target??"RemoteExecutor"});break}default:throw new Error(`Unexpected log level: ${t.level}`)}}},Eo=class{#t;#e;#r;constructor(t){this.#t=t,this.#e=new Map}async request({method:t,path:r,headers:n,body:i,fetch:o}){let s=new URL(r,this.#t),a=this.#n(s);a&&(n.Cookie=a),this.#r&&(n["Accelerate-Query-Engine-Jwt"]=this.#r);let f=await o(s.href,{method:t,body:i!==void 0?JSON.stringify(i):void 0,headers:n});return ac(t,s,f.status,f.statusText),this.#r=f.headers.get("Accelerate-Query-Engine-Jwt")??void 0,this.#s(s,f),f}#n(t){let r=[],n=new Date;for(let[i,o]of this.#e){if(o.expires&&o.expires<n){this.#e.delete(i);continue}let s=o.domain??t.hostname,a=o.path??"/";t.hostname.endsWith(s)&&t.pathname.startsWith(a)&&r.push(ic(o.name,o.value))}return r.length>0?r.join("; "):void 0}#s(t,r){let n=r.headers.getSetCookie?.()||[];if(n.length===0){let i=r.headers.get("Set-Cookie");i&&n.push(i)}for(let i of n){let o=oc(i),s=o.domain??t.hostname,a=o.path??"/",f=`${s}:${a}:${o.name}`;this.#e.set(f,{name:o.name,value:o.value,domain:s,path:a,expires:o.expires})}}};c();u();p();m();d();l();var xo,lc={async loadQueryCompiler(e){let{clientVersion:t,compilerWasm:r}=e;if(r===void 0)throw new $("WASM query compiler was unexpectedly `undefined`",t);return xo===void 0&&(xo=(async()=>{let n=await r.getRuntime(),i=await r.getQueryCompilerWasmModule();if(i==null)throw new $("The loaded wasm module was unexpectedly `undefined` or `null` once loaded",t);let o={"./query_compiler_bg.js":n},s=new WebAssembly.Instance(i,o),a=s.exports.__wbindgen_start;return n.__wbg_set_wasm(s.exports),a(),n.QueryCompiler})()),await xo}};var lf="P2038",Ir=K("prisma:client:clientEngine"),uc=globalThis;uc.PRISMA_WASM_PANIC_REGISTRY={set_message(e){throw new de(e,Wn)}};var kr=class{name="ClientEngine";#t;#e={type:"disconnected"};#r;#n;config;datamodel;logEmitter;logQueries;logLevel;tracingHelper;#s;constructor(t,r,n){if(r)this.#n={remote:!0};else if(t.adapter)this.#n={remote:!1,driverAdapterFactory:t.adapter},Ir("Using driver adapter: %O",t.adapter);else throw new $("Missing configured driver adapter. Engine type `client` requires an active driver adapter. Please check your PrismaClient initialization code.",t.clientVersion,lf);this.#r=n??lc,this.config=t,this.logQueries=t.logQueries??!1,this.logLevel=t.logLevel??"error",this.logEmitter=t.logEmitter,this.datamodel=t.inlineSchema,this.tracingHelper=t.tracingHelper,t.enableDebugLogs&&(this.logLevel="debug"),this.logQueries&&(this.#s=i=>{this.logEmitter.emit("query",{...i,params:yr(i.params),target:"ClientEngine"})})}applyPendingMigrations(){throw new Error("Cannot call applyPendingMigrations on engine type client.")}async#i(){switch(this.#e.type){case"disconnected":{let t=this.tracingHelper.runInChildSpan("connect",async()=>{let r,n;try{r=await this.#a(),n=await this.#o(r)}catch(o){throw this.#e={type:"disconnected"},n?.free(),await r?.disconnect(),o}let i={executor:r,queryCompiler:n};return this.#e={type:"connected",engine:i},i});return this.#e={type:"connecting",promise:t},await t}case"connecting":return await this.#e.promise;case"connected":return this.#e.engine;case"disconnecting":return await this.#e.promise,await this.#i()}}async#a(){return this.#n.remote?new Xn({clientVersion:this.config.clientVersion,env:this.config.env,inlineDatasources:this.config.inlineDatasources,logEmitter:this.logEmitter,logLevel:this.logLevel,logQueries:this.logQueries,overrideDatasources:this.config.overrideDatasources,tracingHelper:this.tracingHelper}):await Kn.connect({driverAdapterFactory:this.#n.driverAdapterFactory,tracingHelper:this.tracingHelper,transactionOptions:{...this.config.transactionOptions,isolationLevel:this.#m(this.config.transactionOptions.isolationLevel)},onQuery:this.#s,provider:this.config.activeProvider})}async#o(t){let r=this.#t;r===void 0&&(r=await this.#r.loadQueryCompiler(this.config),this.#t=r);let{provider:n,connectionInfo:i}=await t.getConnectionInfo();try{return this.#u(()=>new r({datamodel:this.datamodel,provider:n,connectionInfo:i}),void 0,!1)}catch(o){throw this.#c(o)}}#c(t){if(t instanceof de)return t;try{let r=JSON.parse(t.message);return new $(r.message,this.config.clientVersion,r.error_code)}catch{return t}}#l(t,r){if(t instanceof $)return t;if(t.code==="GenericFailure"&&t.message?.startsWith("PANIC:"))return new de(cc(this,t.message,r),this.config.clientVersion);if(t instanceof ae)return new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion});try{let n=JSON.parse(t);return new ie(`${n.message}
${n.backtrace}`,{clientVersion:this.config.clientVersion})}catch{return t}}#p(t){return t instanceof de?t:typeof t.message=="string"&&typeof t.code=="string"?new ee(t.message,{code:t.code,meta:t.meta,clientVersion:this.config.clientVersion}):typeof t.message=="string"?new ie(t.message,{clientVersion:this.config.clientVersion}):t}#u(t,r,n=!0){let i=uc.PRISMA_WASM_PANIC_REGISTRY.set_message,o;globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=s=>{o=s};try{return t()}finally{if(globalThis.PRISMA_WASM_PANIC_REGISTRY.set_message=i,o)throw this.#t=void 0,n&&this.stop().catch(s=>Ir("failed to disconnect:",s)),new de(cc(this,o,r),this.config.clientVersion)}}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the client engine, it is only relevant and implemented for the binary engine. Please add your event listener to the `process` object directly instead.')}async start(){await this.#i()}async stop(){switch(this.#e.type){case"disconnected":return;case"connecting":return await this.#e.promise,await this.stop();case"connected":{let t=this.#e.engine,r=this.tracingHelper.runInChildSpan("disconnect",async()=>{try{await t.executor.disconnect(),t.queryCompiler.free()}finally{this.#e={type:"disconnected"}}});return this.#e={type:"disconnecting",promise:r},await r}case"disconnecting":return await this.#e.promise}}version(){return"unknown"}async transaction(t,r,n){let i,{executor:o}=await this.#i();try{if(t==="start"){let s=n;i=await o.startTransaction({...s,isolationLevel:this.#m(s.isolationLevel)})}else if(t==="commit"){let s=n;await o.commitTransaction(s)}else if(t==="rollback"){let s=n;await o.rollbackTransaction(s)}else Me(t,"Invalid transaction action.")}catch(s){throw this.#l(s)}return i?{id:i.id,payload:void 0}:void 0}async request(t,{interactiveTransaction:r,customDataProxyFetch:n}){Ir("sending request");let i=JSON.stringify(t),{executor:o,queryCompiler:s}=await this.#i().catch(f=>{throw this.#l(f,i)}),a;try{a=this.#u(()=>this.#d({queries:[t],execute:()=>s.compile(i)}))}catch(f){throw this.#p(f)}try{Ir("query plan created",a);let f={},E=await o.execute({plan:a,model:t.modelName,operation:t.action,placeholderValues:f,transaction:r,batchIndex:void 0,customFetch:n?.(globalThis.fetch)});return Ir("query plan executed"),{data:{[t.action]:E}}}catch(f){throw this.#l(f,i)}}async requestBatch(t,{transaction:r,customDataProxyFetch:n}){if(t.length===0)return[];let i=t[0].action,o=JSON.stringify(St(t,r)),{executor:s,queryCompiler:a}=await this.#i().catch(E=>{throw this.#l(E,o)}),f;try{f=this.#u(()=>this.#d({queries:t,execute:()=>a.compileBatch(o)}))}catch(E){throw this.#p(E)}try{let E;r?.kind==="itx"&&(E=r.options);let A={};switch(f.type){case"multi":{if(r?.kind!=="itx"){let C=r?.options.isolationLevel?{...this.config.transactionOptions,isolationLevel:r.options.isolationLevel}:this.config.transactionOptions;E=await this.transaction("start",{},C)}let R=[],S=!1;for(let[C,L]of f.plans.entries())try{let k=await s.execute({plan:L,placeholderValues:A,model:t[C].modelName,operation:t[C].action,batchIndex:C,transaction:E,customFetch:n?.(globalThis.fetch)});R.push({data:{[t[C].action]:k}})}catch(k){R.push(k),S=!0;break}return E!==void 0&&r?.kind!=="itx"&&(S?await this.transaction("rollback",{},E):await this.transaction("commit",{},E)),R}case"compacted":{if(!t.every(C=>C.action===i))throw new Error("All queries in a batch must have the same action");let R=await s.execute({plan:f.plan,placeholderValues:A,model:t[0].modelName,operation:i,batchIndex:void 0,transaction:E,customFetch:n?.(globalThis.fetch)});return Ha(R,f).map(C=>({data:{[i]:C}}))}}}catch(E){throw this.#l(E,o)}}metrics(t){throw new Error("Method not implemented.")}async apiKey(){let{executor:t}=await this.#i();return t.apiKey()}#m(t){switch(t){case void 0:return;case"ReadUncommitted":return"READ UNCOMMITTED";case"ReadCommitted":return"READ COMMITTED";case"RepeatableRead":return"REPEATABLE READ";case"Serializable":return"SERIALIZABLE";case"Snapshot":return"SNAPSHOT";default:throw new ee(`Inconsistent column data: Conversion failed: Invalid isolation level \`${t}\``,{code:"P2023",clientVersion:this.config.clientVersion,meta:{providedIsolationLevel:t}})}}#d({queries:t,execute:r}){return this.tracingHelper.runInChildSpan({name:"compile",attributes:{models:t.map(n=>n.modelName).filter(n=>n!==void 0),actions:t.map(n=>n.action)}},r)}};function cc(e,t,r){return Fa({binaryTarget:void 0,title:t,version:e.config.clientVersion,engineVersion:"unknown",database:e.config.activeProvider,query:r})}c();u();p();m();d();l();c();u();p();m();d();l();var qt=class extends fe{name="ForcedRetryError";code="P5001";constructor(t){super("This request must be retried",N(t,!0))}};D(qt,"ForcedRetryError");c();u();p();m();d();l();var at=class extends fe{name="NotImplementedYetError";code="P5004";constructor(t,r){super(t,N(r,!1))}};D(at,"NotImplementedYetError");c();u();p();m();d();l();c();u();p();m();d();l();var Q=class extends fe{response;constructor(t,r){super(t,r),this.response=r.response;let n=this.response.headers.get("prisma-request-id");if(n){let i=`(The request id was: ${n})`;this.message=this.message+" "+i}}};var lt=class extends Q{name="SchemaMissingError";code="P5005";constructor(t){super("Schema needs to be uploaded",N(t,!0))}};D(lt,"SchemaMissingError");c();u();p();m();d();l();c();u();p();m();d();l();var Po="This request could not be understood by the server",Or=class extends Q{name="BadRequestError";code="P5000";constructor(t,r,n){super(r||Po,N(t,!1)),n&&(this.code=n)}};D(Or,"BadRequestError");c();u();p();m();d();l();var Dr=class extends Q{name="HealthcheckTimeoutError";code="P5013";logs;constructor(t,r){super("Engine not started: healthcheck timeout",N(t,!0)),this.logs=r}};D(Dr,"HealthcheckTimeoutError");c();u();p();m();d();l();var _r=class extends Q{name="EngineStartupError";code="P5014";logs;constructor(t,r,n){super(r,N(t,!0)),this.logs=n}};D(_r,"EngineStartupError");c();u();p();m();d();l();var Mr=class extends Q{name="EngineVersionNotSupportedError";code="P5012";constructor(t){super("Engine version is not supported",N(t,!1))}};D(Mr,"EngineVersionNotSupportedError");c();u();p();m();d();l();var To="Request timed out",Nr=class extends Q{name="GatewayTimeoutError";code="P5009";constructor(t,r=To){super(r,N(t,!1))}};D(Nr,"GatewayTimeoutError");c();u();p();m();d();l();var cf="Interactive transaction error",Lr=class extends Q{name="InteractiveTransactionError";code="P5015";constructor(t,r=cf){super(r,N(t,!1))}};D(Lr,"InteractiveTransactionError");c();u();p();m();d();l();var uf="Request parameters are invalid",Ur=class extends Q{name="InvalidRequestError";code="P5011";constructor(t,r=uf){super(r,N(t,!1))}};D(Ur,"InvalidRequestError");c();u();p();m();d();l();var vo="Requested resource does not exist",Fr=class extends Q{name="NotFoundError";code="P5003";constructor(t,r=vo){super(r,N(t,!1))}};D(Fr,"NotFoundError");c();u();p();m();d();l();var Ao="Unknown server error",Bt=class extends Q{name="ServerError";code="P5006";logs;constructor(t,r,n){super(r||Ao,N(t,!0)),this.logs=n}};D(Bt,"ServerError");c();u();p();m();d();l();var Ro="Unauthorized, check your connection string",$r=class extends Q{name="UnauthorizedError";code="P5007";constructor(t,r=Ro){super(r,N(t,!1))}};D($r,"UnauthorizedError");c();u();p();m();d();l();var Co="Usage exceeded, retry again later",Vr=class extends Q{name="UsageExceededError";code="P5008";constructor(t,r=Co){super(r,N(t,!0))}};D(Vr,"UsageExceededError");async function pf(e){let t;try{t=await e.text()}catch{return{type:"EmptyError"}}try{let r=JSON.parse(t);if(typeof r=="string")switch(r){case"InternalDataProxyError":return{type:"DataProxyError",body:r};default:return{type:"UnknownTextError",body:r}}if(typeof r=="object"&&r!==null){if("is_panic"in r&&"message"in r&&"error_code"in r)return{type:"QueryEngineError",body:r};if("EngineNotStarted"in r||"InteractiveTransactionMisrouted"in r||"InvalidRequestError"in r){let n=Object.values(r)[0].reason;return typeof n=="string"&&!["SchemaMissing","EngineVersionNotSupported"].includes(n)?{type:"UnknownJsonError",body:r}:{type:"DataProxyError",body:r}}}return{type:"UnknownJsonError",body:r}}catch{return t===""?{type:"EmptyError"}:{type:"UnknownTextError",body:t}}}async function qr(e,t){if(e.ok)return;let r={clientVersion:t,response:e},n=await pf(e);if(n.type==="QueryEngineError")throw new ee(n.body.message,{code:n.body.error_code,clientVersion:t});if(n.type==="DataProxyError"){if(n.body==="InternalDataProxyError")throw new Bt(r,"Internal Data Proxy error");if("EngineNotStarted"in n.body){if(n.body.EngineNotStarted.reason==="SchemaMissing")return new lt(r);if(n.body.EngineNotStarted.reason==="EngineVersionNotSupported")throw new Mr(r);if("EngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,logs:o}=n.body.EngineNotStarted.reason.EngineStartupError;throw new _r(r,i,o)}if("KnownEngineStartupError"in n.body.EngineNotStarted.reason){let{msg:i,error_code:o}=n.body.EngineNotStarted.reason.KnownEngineStartupError;throw new $(i,t,o)}if("HealthcheckTimeout"in n.body.EngineNotStarted.reason){let{logs:i}=n.body.EngineNotStarted.reason.HealthcheckTimeout;throw new Dr(r,i)}}if("InteractiveTransactionMisrouted"in n.body){let i={IDParseError:"Could not parse interactive transaction ID",NoQueryEngineFoundError:"Could not find Query Engine for the specified host and transaction ID",TransactionStartError:"Could not start interactive transaction"};throw new Lr(r,i[n.body.InteractiveTransactionMisrouted.reason])}if("InvalidRequestError"in n.body)throw new Ur(r,n.body.InvalidRequestError.reason)}if(e.status===401||e.status===403)throw new $r(r,jt(Ro,n));if(e.status===404)return new Fr(r,jt(vo,n));if(e.status===429)throw new Vr(r,jt(Co,n));if(e.status===504)throw new Nr(r,jt(To,n));if(e.status>=500)throw new Bt(r,jt(Ao,n));if(e.status>=400)throw new Or(r,jt(Po,n))}function jt(e,t){return t.type==="EmptyError"?e:`${e}: ${JSON.stringify(t)}`}c();u();p();m();d();l();function pc(e){let t=Math.pow(2,e)*50,r=Math.ceil(Math.random()*t)-Math.ceil(t/2),n=t+r;return new Promise(i=>setTimeout(()=>i(n),n))}c();u();p();m();d();l();var Fe="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/";function mc(e){let t=new TextEncoder().encode(e),r="",n=t.byteLength,i=n%3,o=n-i,s,a,f,E,A;for(let R=0;R<o;R=R+3)A=t[R]<<16|t[R+1]<<8|t[R+2],s=(A&16515072)>>18,a=(A&258048)>>12,f=(A&4032)>>6,E=A&63,r+=Fe[s]+Fe[a]+Fe[f]+Fe[E];return i==1?(A=t[o],s=(A&252)>>2,a=(A&3)<<4,r+=Fe[s]+Fe[a]+"=="):i==2&&(A=t[o]<<8|t[o+1],s=(A&64512)>>10,a=(A&1008)>>4,f=(A&15)<<2,r+=Fe[s]+Fe[a]+Fe[f]+"="),r}c();u();p();m();d();l();function dc(e){if(!!e.generator?.previewFeatures.some(r=>r.toLowerCase().includes("metrics")))throw new $("The `metrics` preview feature is not yet available with Accelerate.\nPlease remove `metrics` from the `previewFeatures` in your schema.\n\nMore information about Accelerate: https://pris.ly/d/accelerate",e.clientVersion)}c();u();p();m();d();l();var fc={"@prisma/debug":"workspace:*","@prisma/engines-version":"6.16.0-7.****************************************","@prisma/fetch-engine":"workspace:*","@prisma/get-platform":"workspace:*"};c();u();p();m();d();l();c();u();p();m();d();l();var Br=class extends fe{name="RequestError";code="P5010";constructor(t,r){super(`Cannot fetch data from service:
${t}`,N(r,!0))}};D(Br,"RequestError");async function ct(e,t,r=n=>n){let{clientVersion:n,...i}=t,o=r(fetch);try{return await o(e,i)}catch(s){let a=s.message??"Unknown error";throw new Br(a,{clientVersion:n,cause:s})}}var df=/^[1-9][0-9]*\.[0-9]+\.[0-9]+$/,gc=K("prisma:client:dataproxyEngine");async function ff(e,t){let r=fc["@prisma/engines-version"],n=t.clientVersion??"unknown";if(g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION)return g.env.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION||globalThis.PRISMA_CLIENT_DATA_PROXY_CLIENT_VERSION;if(e.includes("accelerate")&&n!=="0.0.0"&&n!=="in-memory")return n;let[i,o]=n?.split("-")??[];if(o===void 0&&df.test(i))return i;if(o!==void 0||n==="0.0.0"||n==="in-memory"){let[s]=r.split("-")??[],[a,f,E]=s.split("."),A=gf(`<=${a}.${f}.${E}`),R=await ct(A,{clientVersion:n});if(!R.ok)throw new Error(`Failed to fetch stable Prisma version, unpkg.com status ${R.status} ${R.statusText}, response body: ${await R.text()||"<empty body>"}`);let S=await R.text();gc("length of body fetched from unpkg.com",S.length);let C;try{C=JSON.parse(S)}catch(L){throw console.error("JSON.parse error: body fetched from unpkg.com: ",S),L}return C.version}throw new at("Only `major.minor.patch` versions are supported by Accelerate.",{clientVersion:n})}async function yc(e,t){let r=await ff(e,t);return gc("version",r),r}function gf(e){return encodeURI(`https://unpkg.com/prisma@${e}/package.json`)}var hc=3,jr=K("prisma:client:dataproxyEngine"),Qr=class{name="DataProxyEngine";inlineSchema;inlineSchemaHash;inlineDatasources;config;logEmitter;env;clientVersion;engineHash;tracingHelper;remoteClientVersion;host;headerBuilder;startPromise;protocol;constructor(t){dc(t),this.config=t,this.env=t.env,this.inlineSchema=mc(t.inlineSchema),this.inlineDatasources=t.inlineDatasources,this.inlineSchemaHash=t.inlineSchemaHash,this.clientVersion=t.clientVersion,this.engineHash=t.engineVersion,this.logEmitter=t.logEmitter,this.tracingHelper=t.tracingHelper}apiKey(){return this.headerBuilder.apiKey}version(){return this.engineHash}async start(){this.startPromise!==void 0&&await this.startPromise,this.startPromise=(async()=>{let{apiKey:t,url:r}=this.getURLAndAPIKey();this.host=r.host,this.protocol=r.protocol,this.headerBuilder=new $t({apiKey:t,tracingHelper:this.tracingHelper,logLevel:this.config.logLevel??"error",logQueries:this.config.logQueries,engineHash:this.engineHash}),this.remoteClientVersion=await yc(this.host,this.config),jr("host",this.host),jr("protocol",this.protocol)})(),await this.startPromise}async stop(){}propagateResponseExtensions(t){t?.logs?.length&&t.logs.forEach(r=>{switch(r.level){case"debug":case"trace":jr(r);break;case"error":case"warn":case"info":{this.logEmitter.emit(r.level,{timestamp:Vt(r.timestamp),message:r.attributes.message??"",target:r.target??"BinaryEngine"});break}case"query":{this.logEmitter.emit("query",{query:r.attributes.query??"",timestamp:Vt(r.timestamp),duration:r.attributes.duration_ms??0,params:r.attributes.params??"",target:r.target??"BinaryEngine"});break}default:r.level}}),t?.traces?.length&&this.tracingHelper.dispatchEngineSpans(t.traces)}onBeforeExit(){throw new Error('"beforeExit" hook is not applicable to the remote query engine')}async url(t){return await this.start(),`${this.protocol}//${this.host}/${this.remoteClientVersion}/${this.inlineSchemaHash}/${t}`}async uploadSchema(){let t={name:"schemaUpload",internal:!0};return this.tracingHelper.runInChildSpan(t,async()=>{let r=await ct(await this.url("schema"),{method:"PUT",headers:this.headerBuilder.build(),body:this.inlineSchema,clientVersion:this.clientVersion});r.ok||jr("schema response status",r.status);let n=await qr(r,this.clientVersion);if(n)throw this.logEmitter.emit("warn",{message:`Error while uploading schema: ${n.message}`,timestamp:new Date,target:""}),n;this.logEmitter.emit("info",{message:`Schema (re)uploaded (hash: ${this.inlineSchemaHash})`,timestamp:new Date,target:""})})}request(t,{traceparent:r,interactiveTransaction:n,customDataProxyFetch:i}){return this.requestInternal({body:t,traceparent:r,interactiveTransaction:n,customDataProxyFetch:i})}async requestBatch(t,{traceparent:r,transaction:n,customDataProxyFetch:i}){let o=n?.kind==="itx"?n.options:void 0,s=St(t,n);return(await this.requestInternal({body:s,customDataProxyFetch:i,interactiveTransaction:o,traceparent:r})).map(f=>(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f?this.convertProtocolErrorsToClientError(f.errors):f))}requestInternal({body:t,traceparent:r,customDataProxyFetch:n,interactiveTransaction:i}){return this.withRetry({actionGerund:"querying",callback:async({logHttpCall:o})=>{let s=i?`${i.payload.endpoint}/graphql`:await this.url("graphql");o(s);let a=await ct(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r,transactionId:i?.id}),body:JSON.stringify(t),clientVersion:this.clientVersion},n);a.ok||jr("graphql response status",a.status),await this.handleError(await qr(a,this.clientVersion));let f=await a.json();if(f.extensions&&this.propagateResponseExtensions(f.extensions),"errors"in f)throw this.convertProtocolErrorsToClientError(f.errors);return"batchResult"in f?f.batchResult:f}})}async transaction(t,r,n){let i={start:"starting",commit:"committing",rollback:"rolling back"};return this.withRetry({actionGerund:`${i[t]} transaction`,callback:async({logHttpCall:o})=>{if(t==="start"){let s=JSON.stringify({max_wait:n.maxWait,timeout:n.timeout,isolation_level:n.isolationLevel}),a=await this.url("transaction/start");o(a);let f=await ct(a,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),body:s,clientVersion:this.clientVersion});await this.handleError(await qr(f,this.clientVersion));let E=await f.json(),{extensions:A}=E;A&&this.propagateResponseExtensions(A);let R=E.id,S=E["data-proxy"].endpoint;return{id:R,payload:{endpoint:S}}}else{let s=`${n.payload.endpoint}/${t}`;o(s);let a=await ct(s,{method:"POST",headers:this.headerBuilder.build({traceparent:r.traceparent}),clientVersion:this.clientVersion});await this.handleError(await qr(a,this.clientVersion));let f=await a.json(),{extensions:E}=f;E&&this.propagateResponseExtensions(E);return}}})}getURLAndAPIKey(){return Zn({clientVersion:this.clientVersion,env:this.env,inlineDatasources:this.inlineDatasources,overrideDatasources:this.config.overrideDatasources})}metrics(){throw new at("Metrics are not yet supported for Accelerate",{clientVersion:this.clientVersion})}async withRetry(t){for(let r=0;;r++){let n=i=>{this.logEmitter.emit("info",{message:`Calling ${i} (n=${r})`,timestamp:new Date,target:""})};try{return await t.callback({logHttpCall:n})}catch(i){if(!(i instanceof fe)||!i.isRetryable)throw i;if(r>=hc)throw i instanceof qt?i.cause:i;this.logEmitter.emit("warn",{message:`Attempt ${r+1}/${hc} failed for ${t.actionGerund}: ${i.message??"(unknown)"}`,timestamp:new Date,target:""});let o=await pc(r);this.logEmitter.emit("warn",{message:`Retrying after ${o}ms`,timestamp:new Date,target:""})}}}async handleError(t){if(t instanceof lt)throw await this.uploadSchema(),new qt({clientVersion:this.clientVersion,cause:t});if(t)throw t}convertProtocolErrorsToClientError(t){return t.length===1?kn(t[0],this.config.clientVersion,this.config.activeProvider):new ie(JSON.stringify(t),{clientVersion:this.config.clientVersion})}applyPendingMigrations(){throw new Error("Method not implemented.")}};c();u();p();m();d();l();function wc({url:e,adapter:t,copyEngine:r,targetBuildType:n}){let i=[],o=[],s=k=>{i.push({_tag:"warning",value:k})},a=k=>{let M=k.join(`
`);o.push({_tag:"error",value:M})},f=!!e?.startsWith("prisma://"),E=an(e),A=!!t,R=f||E;!A&&r&&R&&n!=="client"&&n!=="wasm-compiler-edge"&&s(["recommend--no-engine","In production, we recommend using `prisma generate --no-engine` (See: `prisma generate --help`)"]);let S=R||!r;A&&(S||n==="edge")&&(n==="edge"?a(["Prisma Client was configured to use the `adapter` option but it was imported via its `/edge` endpoint.","Please either remove the `/edge` endpoint or remove the `adapter` from the Prisma Client constructor."]):R?a(["You've provided both a driver adapter and an Accelerate database URL. Driver adapters currently cannot connect to Accelerate.","Please provide either a driver adapter with a direct database URL or an Accelerate URL and no driver adapter."]):r||a(["Prisma Client was configured to use the `adapter` option but `prisma generate` was run with `--no-engine`.","Please run `prisma generate` without `--no-engine` to be able to use Prisma Client with the adapter."]));let C={accelerate:S,ppg:E,driverAdapters:A};function L(k){return k.length>0}return L(o)?{ok:!1,diagnostics:{warnings:i,errors:o},isUsing:C}:{ok:!0,diagnostics:{warnings:i},isUsing:C}}function bc({copyEngine:e=!0},t){let r;try{r=Ft({inlineDatasources:t.inlineDatasources,overrideDatasources:t.overrideDatasources,env:{...t.env,...g.env},clientVersion:t.clientVersion})}catch{}let{ok:n,isUsing:i,diagnostics:o}=wc({url:r,adapter:t.adapter,copyEngine:e,targetBuildType:"wasm-compiler-edge"});for(let R of o.warnings)un(...R.value);if(!n){let R=o.errors[0];throw new oe(R.value,{clientVersion:t.clientVersion})}let s=ft(t.generator),a=s==="library",f=s==="binary",E=s==="client",A=(i.accelerate||i.ppg)&&!i.driverAdapters;return E?new kr(t,A):i.accelerate?new Qr(t):(i.driverAdapters,i.accelerate,new So({clientVersion:t.clientVersion}))}var So=class{constructor(t){return new Proxy(this,{get(r,n){let i=`In order to run Prisma Client on edge runtime, either:
- Use Prisma Accelerate: https://pris.ly/d/accelerate
- Use Driver Adapters: https://pris.ly/d/driver-adapters`;throw new oe(i,t)}})}};c();u();p();m();d();l();function Ec({generator:e}){return e?.previewFeatures??[]}c();u();p();m();d();l();var xc=e=>({command:e});c();u();p();m();d();l();c();u();p();m();d();l();var Pc=e=>e.strings.reduce((t,r,n)=>`${t}@P${n}${r}`);c();u();p();m();d();l();l();function Qt(e){try{return Tc(e,"fast")}catch{return Tc(e,"slow")}}function Tc(e,t){return JSON.stringify(e.map(r=>Ac(r,t)))}function Ac(e,t){if(Array.isArray(e))return e.map(r=>Ac(r,t));if(typeof e=="bigint")return{prisma__type:"bigint",prisma__value:e.toString()};if(ht(e))return{prisma__type:"date",prisma__value:e.toJSON()};if(me.isDecimal(e))return{prisma__type:"decimal",prisma__value:e.toJSON()};if(y.isBuffer(e))return{prisma__type:"bytes",prisma__value:e.toString("base64")};if(yf(e))return{prisma__type:"bytes",prisma__value:y.from(e).toString("base64")};if(ArrayBuffer.isView(e)){let{buffer:r,byteOffset:n,byteLength:i}=e;return{prisma__type:"bytes",prisma__value:y.from(r,n,i).toString("base64")}}return typeof e=="object"&&t==="slow"?Rc(e):e}function yf(e){return e instanceof ArrayBuffer||e instanceof SharedArrayBuffer?!0:typeof e=="object"&&e!==null?e[Symbol.toStringTag]==="ArrayBuffer"||e[Symbol.toStringTag]==="SharedArrayBuffer":!1}function Rc(e){if(typeof e!="object"||e===null)return e;if(typeof e.toJSON=="function")return e.toJSON();if(Array.isArray(e))return e.map(vc);let t={};for(let r of Object.keys(e))t[r]=vc(e[r]);return t}function vc(e){return typeof e=="bigint"?e.toString():Rc(e)}var hf=/^(\s*alter\s)/i,Cc=K("prisma:client");function Io(e,t,r,n){if(!(e!=="postgresql"&&e!=="cockroachdb")&&r.length>0&&hf.exec(t))throw new Error(`Running ALTER using ${n} is not supported
Using the example below you can still execute your query with Prisma, but please note that it is vulnerable to SQL injection attacks and requires you to take care of input sanitization.

Example:
  await prisma.$executeRawUnsafe(\`ALTER USER prisma WITH PASSWORD '\${password}'\`)

More Information: https://pris.ly/d/execute-raw
`)}var ko=({clientMethod:e,activeProvider:t})=>r=>{let n="",i;if(Rn(r))n=r.sql,i={values:Qt(r.values),__prismaRawParameters__:!0};else if(Array.isArray(r)){let[o,...s]=r;n=o,i={values:Qt(s||[]),__prismaRawParameters__:!0}}else switch(t){case"sqlite":case"mysql":{n=r.sql,i={values:Qt(r.values),__prismaRawParameters__:!0};break}case"cockroachdb":case"postgresql":case"postgres":{n=r.text,i={values:Qt(r.values),__prismaRawParameters__:!0};break}case"sqlserver":{n=Pc(r),i={values:Qt(r.values),__prismaRawParameters__:!0};break}default:throw new Error(`The ${t} provider does not support ${e}`)}return i?.values?Cc(`prisma.${e}(${n}, ${i.values})`):Cc(`prisma.${e}(${n})`),{query:n,parameters:i}},Sc={requestArgsToMiddlewareArgs(e){return[e.strings,...e.values]},middlewareArgsToRequestArgs(e){let[t,...r]=e;return new we(t,r)}},Ic={requestArgsToMiddlewareArgs(e){return[e]},middlewareArgsToRequestArgs(e){return e[0]}};c();u();p();m();d();l();function Oo(e){return function(r,n){let i,o=(s=e)=>{try{return s===void 0||s?.kind==="itx"?i??=kc(r(s)):kc(r(s))}catch(a){return Promise.reject(a)}};return{get spec(){return n},then(s,a){return o().then(s,a)},catch(s){return o().catch(s)},finally(s){return o().finally(s)},requestTransaction(s){let a=o(s);return a.requestTransaction?a.requestTransaction(s):a},[Symbol.toStringTag]:"PrismaPromise"}}}function kc(e){return typeof e.then=="function"?e:Promise.resolve(e)}c();u();p();m();d();l();var wf=mi.split(".")[0],bf={isEnabled(){return!1},getTraceParent(){return"00-10-10-00"},dispatchEngineSpans(){},getActiveContext(){},runInChildSpan(e,t){return t()}},Do=class{isEnabled(){return this.getGlobalTracingHelper().isEnabled()}getTraceParent(t){return this.getGlobalTracingHelper().getTraceParent(t)}dispatchEngineSpans(t){return this.getGlobalTracingHelper().dispatchEngineSpans(t)}getActiveContext(){return this.getGlobalTracingHelper().getActiveContext()}runInChildSpan(t,r){return this.getGlobalTracingHelper().runInChildSpan(t,r)}getGlobalTracingHelper(){let t=globalThis[`V${wf}_PRISMA_INSTRUMENTATION`],r=globalThis.PRISMA_INSTRUMENTATION;return t?.helper??r?.helper??bf}};function Oc(){return new Do}c();u();p();m();d();l();function Dc(e,t=()=>{}){let r,n=new Promise(i=>r=i);return{then(i){return--e===0&&r(t()),i?.(n)}}}c();u();p();m();d();l();function _c(e){return typeof e=="string"?e:e.reduce((t,r)=>{let n=typeof r=="string"?r:r.level;return n==="query"?t:t&&(r==="info"||t==="info")?"info":n},void 0)}c();u();p();m();d();l();c();u();p();m();d();l();function ei(e){return typeof e.batchRequestIdx=="number"}c();u();p();m();d();l();function Mc(e){if(e.action!=="findUnique"&&e.action!=="findUniqueOrThrow")return;let t=[];return e.modelName&&t.push(e.modelName),e.query.arguments&&t.push(_o(e.query.arguments)),t.push(_o(e.query.selection)),t.join("")}function _o(e){return`(${Object.keys(e).sort().map(r=>{let n=e[r];return typeof n=="object"&&n!==null?`(${r} ${_o(n)})`:r}).join(" ")})`}c();u();p();m();d();l();var Ef={aggregate:!1,aggregateRaw:!1,createMany:!0,createManyAndReturn:!0,createOne:!0,deleteMany:!0,deleteOne:!0,executeRaw:!0,findFirst:!1,findFirstOrThrow:!1,findMany:!1,findRaw:!1,findUnique:!1,findUniqueOrThrow:!1,groupBy:!1,queryRaw:!1,runCommandRaw:!0,updateMany:!0,updateManyAndReturn:!0,updateOne:!0,upsertOne:!0};function Mo(e){return Ef[e]}c();u();p();m();d();l();var ti=class{constructor(t){this.options=t;this.batches={}}batches;tickActive=!1;request(t){let r=this.options.batchBy(t);return r?(this.batches[r]||(this.batches[r]=[],this.tickActive||(this.tickActive=!0,g.nextTick(()=>{this.dispatchBatches(),this.tickActive=!1}))),new Promise((n,i)=>{this.batches[r].push({request:t,resolve:n,reject:i})})):this.options.singleLoader(t)}dispatchBatches(){for(let t in this.batches){let r=this.batches[t];delete this.batches[t],r.length===1?this.options.singleLoader(r[0].request).then(n=>{n instanceof Error?r[0].reject(n):r[0].resolve(n)}).catch(n=>{r[0].reject(n)}):(r.sort((n,i)=>this.options.batchOrder(n.request,i.request)),this.options.batchLoader(r.map(n=>n.request)).then(n=>{if(n instanceof Error)for(let i=0;i<r.length;i++)r[i].reject(n);else for(let i=0;i<r.length;i++){let o=n[i];o instanceof Error?r[i].reject(o):r[i].resolve(o)}}).catch(n=>{for(let i=0;i<r.length;i++)r[i].reject(n)}))}}get[Symbol.toStringTag](){return"DataLoader"}};c();u();p();m();d();l();l();function ut(e,t){if(t===null)return t;switch(e){case"bigint":return BigInt(t);case"bytes":{let{buffer:r,byteOffset:n,byteLength:i}=y.from(t,"base64");return new Uint8Array(r,n,i)}case"decimal":return new me(t);case"datetime":case"date":return new Date(t);case"time":return new Date(`1970-01-01T${t}Z`);case"bigint-array":return t.map(r=>ut("bigint",r));case"bytes-array":return t.map(r=>ut("bytes",r));case"decimal-array":return t.map(r=>ut("decimal",r));case"datetime-array":return t.map(r=>ut("datetime",r));case"date-array":return t.map(r=>ut("date",r));case"time-array":return t.map(r=>ut("time",r));default:return t}}function No(e){let t=[],r=xf(e);for(let n=0;n<e.rows.length;n++){let i=e.rows[n],o={...r};for(let s=0;s<i.length;s++)o[e.columns[s]]=ut(e.types[s],i[s]);t.push(o)}return t}function xf(e){let t={};for(let r=0;r<e.columns.length;r++)t[e.columns[r]]=null;return t}var Pf=K("prisma:client:request_handler"),ri=class{client;dataloader;logEmitter;constructor(t,r){this.logEmitter=r,this.client=t,this.dataloader=new ti({batchLoader:Ca(async({requests:n,customDataProxyFetch:i})=>{let{transaction:o,otelParentCtx:s}=n[0],a=n.map(R=>R.protocolQuery),f=this.client._tracingHelper.getTraceParent(s),E=n.some(R=>Mo(R.protocolQuery.action));return(await this.client._engine.requestBatch(a,{traceparent:f,transaction:Tf(o),containsWrite:E,customDataProxyFetch:i})).map((R,S)=>{if(R instanceof Error)return R;try{return this.mapQueryEngineResult(n[S],R)}catch(C){return C}})}),singleLoader:async n=>{let i=n.transaction?.kind==="itx"?Nc(n.transaction):void 0,o=await this.client._engine.request(n.protocolQuery,{traceparent:this.client._tracingHelper.getTraceParent(),interactiveTransaction:i,isWrite:Mo(n.protocolQuery.action),customDataProxyFetch:n.customDataProxyFetch});return this.mapQueryEngineResult(n,o)},batchBy:n=>n.transaction?.id?`transaction-${n.transaction.id}`:Mc(n.protocolQuery),batchOrder(n,i){return n.transaction?.kind==="batch"&&i.transaction?.kind==="batch"?n.transaction.index-i.transaction.index:0}})}async request(t){try{return await this.dataloader.request(t)}catch(r){let{clientMethod:n,callsite:i,transaction:o,args:s,modelName:a}=t;this.handleAndLogRequestError({error:r,clientMethod:n,callsite:i,transaction:o,args:s,modelName:a,globalOmit:t.globalOmit})}}mapQueryEngineResult({dataPath:t,unpacker:r},n){let i=n?.data,o=this.unpack(i,t,r);return g.env.PRISMA_CLIENT_GET_TIME?{data:o}:o}handleAndLogRequestError(t){try{this.handleRequestError(t)}catch(r){throw this.logEmitter&&this.logEmitter.emit("error",{message:r.message,target:t.clientMethod,timestamp:new Date}),r}}handleRequestError({error:t,clientMethod:r,callsite:n,transaction:i,args:o,modelName:s,globalOmit:a}){if(Pf(t),vf(t,i))throw t;if(t instanceof ee&&Af(t)){let E=Lc(t.meta);Pn({args:o,errors:[E],callsite:n,errorFormat:this.client._errorFormat,originalMethod:r,clientVersion:this.client._clientVersion,globalOmit:a})}let f=t.message;if(n&&(f=dn({callsite:n,originalMethod:r,isPanic:t.isPanic,showColors:this.client._errorFormat==="pretty",message:f})),f=this.sanitizeMessage(f),t.code){let E=s?{modelName:s,...t.meta}:t.meta;throw new ee(f,{code:t.code,clientVersion:this.client._clientVersion,meta:E,batchRequestIdx:t.batchRequestIdx})}else{if(t.isPanic)throw new de(f,this.client._clientVersion);if(t instanceof ie)throw new ie(f,{clientVersion:this.client._clientVersion,batchRequestIdx:t.batchRequestIdx});if(t instanceof $)throw new $(f,this.client._clientVersion);if(t instanceof de)throw new de(f,this.client._clientVersion)}throw t.clientVersion=this.client._clientVersion,t}sanitizeMessage(t){return this.client._errorFormat&&this.client._errorFormat!=="pretty"?yt(t):t}unpack(t,r,n){if(!t||(t.data&&(t=t.data),!t))return t;let i=Object.keys(t)[0],o=Object.values(t)[0],s=r.filter(E=>E!=="select"&&E!=="include"),a=Mi(o,s),f=i==="queryRaw"?No(a):et(a);return n?n(f):f}get[Symbol.toStringTag](){return"RequestHandler"}};function Tf(e){if(e){if(e.kind==="batch")return{kind:"batch",options:{isolationLevel:e.isolationLevel}};if(e.kind==="itx")return{kind:"itx",options:Nc(e)};Me(e,"Unknown transaction kind")}}function Nc(e){return{id:e.id,payload:e.payload}}function vf(e,t){return ei(e)&&t?.kind==="batch"&&e.batchRequestIdx!==t.index}function Af(e){return e.code==="P2009"||e.code==="P2012"}function Lc(e){if(e.kind==="Union")return{kind:"Union",errors:e.errors.map(Lc)};if(Array.isArray(e.selectionPath)){let[,...t]=e.selectionPath;return{...e,selectionPath:t}}return e}c();u();p();m();d();l();var Uc=Wn;c();u();p();m();d();l();var Bc=$e(xi());c();u();p();m();d();l();var V=class extends Error{constructor(t){super(t+`
Read more at https://pris.ly/d/client-constructor`),this.name="PrismaClientConstructorValidationError"}get[Symbol.toStringTag](){return"PrismaClientConstructorValidationError"}};D(V,"PrismaClientConstructorValidationError");var Fc=["datasources","datasourceUrl","errorFormat","adapter","log","transactionOptions","omit","__internal"],$c=["pretty","colorless","minimal"],Vc=["info","query","warn","error"],Rf={datasources:(e,{datasourceNames:t})=>{if(e){if(typeof e!="object"||Array.isArray(e))throw new V(`Invalid value ${JSON.stringify(e)} for "datasources" provided to PrismaClient constructor`);for(let[r,n]of Object.entries(e)){if(!t.includes(r)){let i=Ht(r,t)||` Available datasources: ${t.join(", ")}`;throw new V(`Unknown datasource ${r} provided to PrismaClient constructor.${i}`)}if(typeof n!="object"||Array.isArray(n))throw new V(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(n&&typeof n=="object")for(let[i,o]of Object.entries(n)){if(i!=="url")throw new V(`Invalid value ${JSON.stringify(e)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`);if(typeof o!="string")throw new V(`Invalid value ${JSON.stringify(o)} for datasource "${r}" provided to PrismaClient constructor.
It should have this form: { url: "CONNECTION_STRING" }`)}}}},adapter:(e,t)=>{if(!e&&ft(t.generator)==="client")throw new V('Using engine type "client" requires a driver adapter to be provided to PrismaClient constructor.');if(e!==null){if(e===void 0)throw new V('"adapter" property must not be undefined, use null to conditionally disable driver adapters.');if(ft(t.generator)==="binary")throw new V('Cannot use a driver adapter with the "binary" Query Engine. Please use the "library" Query Engine.')}},datasourceUrl:e=>{if(typeof e<"u"&&typeof e!="string")throw new V(`Invalid value ${JSON.stringify(e)} for "datasourceUrl" provided to PrismaClient constructor.
Expected string or undefined.`)},errorFormat:e=>{if(e){if(typeof e!="string")throw new V(`Invalid value ${JSON.stringify(e)} for "errorFormat" provided to PrismaClient constructor.`);if(!$c.includes(e)){let t=Ht(e,$c);throw new V(`Invalid errorFormat ${e} provided to PrismaClient constructor.${t}`)}}},log:e=>{if(!e)return;if(!Array.isArray(e))throw new V(`Invalid value ${JSON.stringify(e)} for "log" provided to PrismaClient constructor.`);function t(r){if(typeof r=="string"&&!Vc.includes(r)){let n=Ht(r,Vc);throw new V(`Invalid log level "${r}" provided to PrismaClient constructor.${n}`)}}for(let r of e){t(r);let n={level:t,emit:i=>{let o=["stdout","event"];if(!o.includes(i)){let s=Ht(i,o);throw new V(`Invalid value ${JSON.stringify(i)} for "emit" in logLevel provided to PrismaClient constructor.${s}`)}}};if(r&&typeof r=="object")for(let[i,o]of Object.entries(r))if(n[i])n[i](o);else throw new V(`Invalid property ${i} for "log" provided to PrismaClient constructor`)}},transactionOptions:e=>{if(!e)return;let t=e.maxWait;if(t!=null&&t<=0)throw new V(`Invalid value ${t} for maxWait in "transactionOptions" provided to PrismaClient constructor. maxWait needs to be greater than 0`);let r=e.timeout;if(r!=null&&r<=0)throw new V(`Invalid value ${r} for timeout in "transactionOptions" provided to PrismaClient constructor. timeout needs to be greater than 0`)},omit:(e,t)=>{if(typeof e!="object")throw new V('"omit" option is expected to be an object.');if(e===null)throw new V('"omit" option can not be `null`');let r=[];for(let[n,i]of Object.entries(e)){let o=Sf(n,t.runtimeDataModel);if(!o){r.push({kind:"UnknownModel",modelKey:n});continue}for(let[s,a]of Object.entries(i)){let f=o.fields.find(E=>E.name===s);if(!f){r.push({kind:"UnknownField",modelKey:n,fieldName:s});continue}if(f.relationName){r.push({kind:"RelationInOmit",modelKey:n,fieldName:s});continue}typeof a!="boolean"&&r.push({kind:"InvalidFieldValue",modelKey:n,fieldName:s})}}if(r.length>0)throw new V(If(e,r))},__internal:e=>{if(!e)return;let t=["debug","engine","configOverride"];if(typeof e!="object")throw new V(`Invalid value ${JSON.stringify(e)} for "__internal" to PrismaClient constructor`);for(let[r]of Object.entries(e))if(!t.includes(r)){let n=Ht(r,t);throw new V(`Invalid property ${JSON.stringify(r)} for "__internal" provided to PrismaClient constructor.${n}`)}}};function jc(e,t){for(let[r,n]of Object.entries(e)){if(!Fc.includes(r)){let i=Ht(r,Fc);throw new V(`Unknown property ${r} provided to PrismaClient constructor.${i}`)}Rf[r](n,t)}if(e.datasourceUrl&&e.datasources)throw new V('Can not use "datasourceUrl" and "datasources" options at the same time. Pick one of them')}function Ht(e,t){if(t.length===0||typeof e!="string")return"";let r=Cf(e,t);return r?` Did you mean "${r}"?`:""}function Cf(e,t){if(t.length===0)return null;let r=t.map(i=>({value:i,distance:(0,Bc.default)(e,i)}));r.sort((i,o)=>i.distance<o.distance?-1:1);let n=r[0];return n.distance<3?n.value:null}function Sf(e,t){return qc(t.models,e)??qc(t.types,e)}function qc(e,t){let r=Object.keys(e).find(n=>qe(n)===t);if(r)return e[r]}function If(e,t){let r=At(e);for(let o of t)switch(o.kind){case"UnknownModel":r.arguments.getField(o.modelKey)?.markAsError(),r.addErrorMessage(()=>`Unknown model name: ${o.modelKey}.`);break;case"UnknownField":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>`Model "${o.modelKey}" does not have a field named "${o.fieldName}".`);break;case"RelationInOmit":r.arguments.getDeepField([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>'Relations are already excluded by default and can not be specified in "omit".');break;case"InvalidFieldValue":r.arguments.getDeepFieldValue([o.modelKey,o.fieldName])?.markAsError(),r.addErrorMessage(()=>"Omit field option value must be a boolean.");break}let{message:n,args:i}=xn(r,"colorless");return`Error validating "omit" option:

${i}

${n}`}c();u();p();m();d();l();function Qc(e){return e.length===0?Promise.resolve([]):new Promise((t,r)=>{let n=new Array(e.length),i=null,o=!1,s=0,a=()=>{o||(s++,s===e.length&&(o=!0,i?r(i):t(n)))},f=E=>{o||(o=!0,r(E))};for(let E=0;E<e.length;E++)e[E].then(A=>{n[E]=A,a()},A=>{if(!ei(A)){f(A);return}A.batchRequestIdx===E?f(A):(i||(i=A),a())})})}var Je=K("prisma:client");typeof globalThis=="object"&&(globalThis.NODE_CLIENT=!0);var kf={requestArgsToMiddlewareArgs:e=>e,middlewareArgsToRequestArgs:e=>e},Of=Symbol.for("prisma.client.transaction.id"),Df={id:0,nextId(){return++this.id}};function _f(e){class t{_originalClient=this;_runtimeDataModel;_requestHandler;_connectionPromise;_disconnectionPromise;_engineConfig;_accelerateEngineConfig;_clientVersion;_errorFormat;_tracingHelper;_previewFeatures;_activeProvider;_globalOmit;_extensions;_engine;_appliedParent;_createPrismaPromise=Oo();constructor(n){e=n?.__internal?.configOverride?.(e)??e,Da(e),n&&jc(n,e);let i=new Cn().on("error",()=>{});this._extensions=Rt.empty(),this._previewFeatures=Ec(e),this._clientVersion=e.clientVersion??Uc,this._activeProvider=e.activeProvider,this._globalOmit=n?.omit,this._tracingHelper=Oc();let o=e.relativeEnvPaths&&{rootEnvPath:e.relativeEnvPaths.rootEnvPath&&en.resolve(e.dirname,e.relativeEnvPaths.rootEnvPath),schemaEnvPath:e.relativeEnvPaths.schemaEnvPath&&en.resolve(e.dirname,e.relativeEnvPaths.schemaEnvPath)},s;if(n?.adapter){s=n.adapter;let f=e.activeProvider==="postgresql"||e.activeProvider==="cockroachdb"?"postgres":e.activeProvider;if(s.provider!==f)throw new $(`The Driver Adapter \`${s.adapterName}\`, based on \`${s.provider}\`, is not compatible with the provider \`${f}\` specified in the Prisma schema.`,this._clientVersion);if(n.datasources||n.datasourceUrl!==void 0)throw new $("Custom datasource configuration is not compatible with Prisma Driver Adapters. Please define the database connection string directly in the Driver Adapter configuration.",this._clientVersion)}let a=e.injectableEdgeEnv?.();try{let f=n??{},E=f.__internal??{},A=E.debug===!0;A&&K.enable("prisma:client");let R=en.resolve(e.dirname,e.relativePath);ps.existsSync(R)||(R=e.dirname),Je("dirname",e.dirname),Je("relativePath",e.relativePath),Je("cwd",R);let S=E.engine||{};if(f.errorFormat?this._errorFormat=f.errorFormat:g.env.NODE_ENV==="production"?this._errorFormat="minimal":g.env.NO_COLOR?this._errorFormat="colorless":this._errorFormat="colorless",this._runtimeDataModel=e.runtimeDataModel,this._engineConfig={cwd:R,dirname:e.dirname,enableDebugLogs:A,allowTriggerPanic:S.allowTriggerPanic,prismaPath:S.binaryPath??void 0,engineEndpoint:S.endpoint,generator:e.generator,showColors:this._errorFormat==="pretty",logLevel:f.log&&_c(f.log),logQueries:f.log&&!!(typeof f.log=="string"?f.log==="query":f.log.find(C=>typeof C=="string"?C==="query":C.level==="query")),env:a?.parsed??{},flags:[],engineWasm:e.engineWasm,compilerWasm:e.compilerWasm,clientVersion:e.clientVersion,engineVersion:e.engineVersion,previewFeatures:this._previewFeatures,activeProvider:e.activeProvider,inlineSchema:e.inlineSchema,overrideDatasources:_a(f,e.datasourceNames),inlineDatasources:e.inlineDatasources,inlineSchemaHash:e.inlineSchemaHash,tracingHelper:this._tracingHelper,transactionOptions:{maxWait:f.transactionOptions?.maxWait??2e3,timeout:f.transactionOptions?.timeout??5e3,isolationLevel:f.transactionOptions?.isolationLevel},logEmitter:i,isBundled:e.isBundled,adapter:s},this._accelerateEngineConfig={...this._engineConfig,accelerateUtils:{resolveDatasourceUrl:Ft,getBatchRequestPayload:St,prismaGraphQLToJSError:kn,PrismaClientUnknownRequestError:ie,PrismaClientInitializationError:$,PrismaClientKnownRequestError:ee,debug:K("prisma:client:accelerateEngine"),engineVersion:Gc.version,clientVersion:e.clientVersion}},Je("clientVersion",e.clientVersion),this._engine=bc(e,this._engineConfig),this._requestHandler=new ri(this,i),f.log)for(let C of f.log){let L=typeof C=="string"?C:C.emit==="stdout"?C.level:null;L&&this.$on(L,k=>{Xt.log(`${Xt.tags[L]??""}`,k.message||k.query)})}}catch(f){throw f.clientVersion=this._clientVersion,f}return this._appliedParent=fr(this)}get[Symbol.toStringTag](){return"PrismaClient"}$on(n,i){return n==="beforeExit"?this._engine.onBeforeExit(i):n&&this._engineConfig.logEmitter.on(n,i),this}$connect(){try{return this._engine.start()}catch(n){throw n.clientVersion=this._clientVersion,n}}async $disconnect(){try{await this._engine.stop()}catch(n){throw n.clientVersion=this._clientVersion,n}finally{us()}}$executeRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"executeRaw",args:o,transaction:n,clientMethod:i,argsMapper:ko({clientMethod:i,activeProvider:a}),callsite:je(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$executeRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0){let[s,a]=Hc(n,i);return Io(this._activeProvider,s.text,s.values,Array.isArray(n)?"prisma.$executeRaw`<SQL>`":"prisma.$executeRaw(sql`<SQL>`)"),this.$executeRawInternal(o,"$executeRaw",s,a)}throw new oe("`$executeRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$executeRaw`UPDATE User SET cool = ${true} WHERE email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#executeraw\n",{clientVersion:this._clientVersion})})}$executeRawUnsafe(n,...i){return this._createPrismaPromise(o=>(Io(this._activeProvider,n,i,"prisma.$executeRawUnsafe(<SQL>, [...values])"),this.$executeRawInternal(o,"$executeRawUnsafe",[n,...i])))}$runCommandRaw(n){if(e.activeProvider!=="mongodb")throw new oe(`The ${e.activeProvider} provider does not support $runCommandRaw. Use the mongodb provider.`,{clientVersion:this._clientVersion});return this._createPrismaPromise(i=>this._request({args:n,clientMethod:"$runCommandRaw",dataPath:[],action:"runCommandRaw",argsMapper:xc,callsite:je(this._errorFormat),transaction:i}))}async $queryRawInternal(n,i,o,s){let a=this._activeProvider;return this._request({action:"queryRaw",args:o,transaction:n,clientMethod:i,argsMapper:ko({clientMethod:i,activeProvider:a}),callsite:je(this._errorFormat),dataPath:[],middlewareArgsMapper:s})}$queryRaw(n,...i){return this._createPrismaPromise(o=>{if(n.raw!==void 0||n.sql!==void 0)return this.$queryRawInternal(o,"$queryRaw",...Hc(n,i));throw new oe("`$queryRaw` is a tag function, please use it like the following:\n```\nconst result = await prisma.$queryRaw`SELECT * FROM User WHERE id = ${1} OR email = ${'<EMAIL>'};`\n```\n\nOr read our docs at https://www.prisma.io/docs/concepts/components/prisma-client/raw-database-access#queryraw\n",{clientVersion:this._clientVersion})})}$queryRawTyped(n){return this._createPrismaPromise(i=>{if(!this._hasPreviewFlag("typedSql"))throw new oe("`typedSql` preview feature must be enabled in order to access $queryRawTyped API",{clientVersion:this._clientVersion});return this.$queryRawInternal(i,"$queryRawTyped",n)})}$queryRawUnsafe(n,...i){return this._createPrismaPromise(o=>this.$queryRawInternal(o,"$queryRawUnsafe",[n,...i]))}_transactionWithArray({promises:n,options:i}){let o=Df.nextId(),s=Dc(n.length),a=n.map((f,E)=>{if(f?.[Symbol.toStringTag]!=="PrismaPromise")throw new Error("All elements of the array need to be Prisma Client promises. Hint: Please make sure you are not awaiting the Prisma client calls you intended to pass in the $transaction function.");let A=i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel,R={kind:"batch",id:o,index:E,isolationLevel:A,lock:s};return f.requestTransaction?.(R)??f});return Qc(a)}async _transactionWithCallback({callback:n,options:i}){let o={traceparent:this._tracingHelper.getTraceParent()},s={maxWait:i?.maxWait??this._engineConfig.transactionOptions.maxWait,timeout:i?.timeout??this._engineConfig.transactionOptions.timeout,isolationLevel:i?.isolationLevel??this._engineConfig.transactionOptions.isolationLevel},a=await this._engine.transaction("start",o,s),f;try{let E={kind:"itx",...a};f=await n(this._createItxClient(E)),await this._engine.transaction("commit",o,a)}catch(E){throw await this._engine.transaction("rollback",o,a).catch(()=>{}),E}return f}_createItxClient(n){return Pe(fr(Pe(ha(this),[se("_appliedParent",()=>this._appliedParent._createItxClient(n)),se("_createPrismaPromise",()=>Oo(n)),se(Of,()=>n.id)])),[Ct(Pa)])}$transaction(n,i){let o;typeof n=="function"?this._engineConfig.adapter?.adapterName==="@prisma/adapter-d1"?o=()=>{throw new Error("Cloudflare D1 does not support interactive transactions. We recommend you to refactor your queries with that limitation in mind, and use batch transactions with `prisma.$transactions([])` where applicable.")}:o=()=>this._transactionWithCallback({callback:n,options:i}):o=()=>this._transactionWithArray({promises:n,options:i});let s={name:"transaction",attributes:{method:"$transaction"}};return this._tracingHelper.runInChildSpan(s,o)}_request(n){n.otelParentCtx=this._tracingHelper.getActiveContext();let i=n.middlewareArgsMapper??kf,o={args:i.requestArgsToMiddlewareArgs(n.args),dataPath:n.dataPath,runInTransaction:!!n.transaction,action:n.action,model:n.model},s={operation:{name:"operation",attributes:{method:o.action,model:o.model,name:o.model?`${o.model}.${o.action}`:o.action}}},a=async f=>{let{runInTransaction:E,args:A,...R}=f,S={...n,...R};A&&(S.args=i.middlewareArgsToRequestArgs(A)),n.transaction!==void 0&&E===!1&&delete S.transaction;let C=await Ra(this,S);return S.model?xa({result:C,modelName:S.model,args:S.args,extensions:this._extensions,runtimeDataModel:this._runtimeDataModel,globalOmit:this._globalOmit}):C};return this._tracingHelper.runInChildSpan(s.operation,()=>a(o))}async _executeRequest({args:n,clientMethod:i,dataPath:o,callsite:s,action:a,model:f,argsMapper:E,transaction:A,unpacker:R,otelParentCtx:S,customDataProxyFetch:C}){try{n=E?E(n):n;let L={name:"serialize"},k=this._tracingHelper.runInChildSpan(L,()=>Ii({modelName:f,runtimeDataModel:this._runtimeDataModel,action:a,args:n,clientMethod:i,callsite:s,extensions:this._extensions,errorFormat:this._errorFormat,clientVersion:this._clientVersion,previewFeatures:this._previewFeatures,globalOmit:this._globalOmit}));return K.enabled("prisma:client")&&(Je("Prisma Client call:"),Je(`prisma.${i}(${aa(n)})`),Je("Generated request:"),Je(JSON.stringify(k,null,2)+`
`)),A?.kind==="batch"&&await A.lock,this._requestHandler.request({protocolQuery:k,modelName:f,action:a,clientMethod:i,dataPath:o,callsite:s,args:n,extensions:this._extensions,transaction:A,unpacker:R,otelParentCtx:S,otelChildCtx:this._tracingHelper.getActiveContext(),globalOmit:this._globalOmit,customDataProxyFetch:C})}catch(L){throw L.clientVersion=this._clientVersion,L}}$metrics=new pr(this);_hasPreviewFlag(n){return!!this._engineConfig.previewFeatures?.includes(n)}$applyPendingMigrations(){return this._engine.applyPendingMigrations()}$extends=wa}return t}function Hc(e,t){return Mf(e)?[new we(e,t),Sc]:[e,Ic]}function Mf(e){return Array.isArray(e)&&Array.isArray(e.raw)}c();u();p();m();d();l();var Nf=new Set(["toJSON","$$typeof","asymmetricMatch",Symbol.iterator,Symbol.toStringTag,Symbol.isConcatSpreadable,Symbol.toPrimitive]);function Lf(e){return new Proxy(e,{get(t,r){if(r in t)return t[r];if(!Nf.has(r))throw new TypeError(`Invalid enum value: ${String(r)}`)}})}c();u();p();m();d();l();l();var export_warnEnvConflicts=void 0;export{mn as DMMF,K as Debug,me as Decimal,Ko as Extensions,pr as MetricsClient,$ as PrismaClientInitializationError,ee as PrismaClientKnownRequestError,de as PrismaClientRustPanicError,ie as PrismaClientUnknownRequestError,oe as PrismaClientValidationError,Yo as Public,we as Sql,Dp as createParam,Bp as defineDmmfProperty,et as deserializeJsonResponse,No as deserializeRawResult,Yu as dmmfToRuntimeDataModel,Gp as empty,_f as getPrismaClient,bo as getRuntime,Hp as join,Lf as makeStrictEnum,Qp as makeTypedQueryFactory,vi as objectEnumValues,na as raw,Ii as serializeJsonQuery,Ci as skip,ia as sqltag,export_warnEnvConflicts as warnEnvConflicts,un as warnOnce};
//# sourceMappingURL=wasm-compiler-edge.mjs.map
